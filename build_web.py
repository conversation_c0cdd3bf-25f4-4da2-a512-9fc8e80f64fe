import os
import sys
import subprocess
import math
import re


def get_next_version(current_image):
	current_version = current_image.split('v')[1]
	decimals = list(map(int, current_version.split('.')))

	ones = int((decimals[2] + 1) % math.inf)
	tens = int((decimals[1] + (0 if ones else 1)) % math.inf)
	hund = (decimals[0] + (0 if tens or not decimals[1] else 1))

	return 'image-v{0}.{1}.{2}'.format(hund, tens, ones)


custom_image = None
if len(sys.argv) > 1:
    custom_image = sys.argv[1]

os.chdir('agrichain')

print('Taking Latest Pull for Web....')
subprocess.call(['git', 'pull', '-r'])

print('Getting Latest Available Tag....')
latest_image = subprocess.check_output(['git', 'describe', '--abbrev=0', '--tags']).strip().decode('utf-8')
next_version = custom_image or get_next_version(latest_image)

print("Updating package.json for image....")
PACKAGE_JSON="./package.json"
VERSION_KEY="version"
json_data = subprocess.check_output(["cat", PACKAGE_JSON])
json_data = json_data.decode("utf-8")
json_data = re.sub(f"\"{VERSION_KEY}\": \".*\"", f"\"{VERSION_KEY}\": \"{next_version}\"", json_data)
proc = subprocess.Popen(["echo", json_data], stdout=subprocess.PIPE)
stdout, stderr = proc.communicate()
stdout = stdout.strip()
with open(PACKAGE_JSON, "wb") as f:
    f.write(stdout)

print('Committing Changes....')
subprocess.call("git commit -am 'Story-01 | bumping self [ci skip]'", shell=True)
subprocess.call('git push', shell=True)
print('Changes Pushed.....')

print('Tagging with Latest Image....')
subprocess.call(['git', 'tag', next_version])

print('Pushing Changes (if any)...')
subprocess.call(['git', 'push'])
print('Pushing Tags....')
subprocess.call(['git', 'push', '--tags'])

print('Circle CI is now Building Image....')
print('Web Image: {image}'.format(image=next_version))
