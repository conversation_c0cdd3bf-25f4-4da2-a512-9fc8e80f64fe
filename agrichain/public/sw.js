// Register event listener for the 'push' event.
let url;

const format_time = date_obj =>  {
  // formats a javascript Date object into a 12h AM/PM time string
  var hour = date_obj.getHours();
  var minute = date_obj.getMinutes();
  var amPM = (hour > 11) ? "PM" : "AM";
  if(hour > 12) {
    hour -= 12;
  } else if(hour === 0) {
    hour = "12";
  }
  if(minute < 10) {
    minute = "0" + minute;
  }
  if( hour < 10 ){
    hour = "0" + hour ;
  }
  return hour + ":" + minute + ' ' + amPM;
}

const format_date = date_obj => {
  var days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
  "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
];
  var dayIndex = date_obj.getDay();
  return days[dayIndex] + " " + date_obj.getDate() + " " + monthNames[date_obj.getMonth()];
}


self.addEventListener('push', event => {
  // Retrieve the textual payload from event.data (a PushMessageData object).
  // Other formats are supported (ArrayBuffer, Blob, JSON), check out the documentation
  // on https://developer.mozilla.org/en-US/docs/Web/API/PushMessageData.
  const eventInfo = event.data.text();
  const data = JSON.parse(eventInfo);
  const head = data.head || 'New Notification 🕺🕺';
  const start = new Date(data.start);
  const end = new Date(data.end);

  let body = data.body || 'This is default content. Your notification didn\'t have one 🙄🙄';
  body = body.replace('start_date', format_date(start));
  body = body.replace('start_time', format_time(start));
  body = body.replace('end_date', format_date(end));
  body = body.replace('end_time', format_time(end));
  body = body.replace(/\<b\>/g, '');
  body = body.replace(/\<\/b\>/g, '');

  url = data.url;
  if(url.match('site-bookings'))
    url += '?siteId=' + data.site_id + '&companyId=' + data.company_id;

  // Keep the service worker alive until the notification is created.
  event.waitUntil(
    self.registration.showNotification(head, {
      body: body,
      icon: 'https://agrichain-api-production.s3-ap-southeast-2.amazonaws.com/assets/agrichain-logo-icon.png'
    })
  );
});

self.addEventListener('notificationclick', event => {
  if(url) {
    event.waitUntil(
      clients.matchAll({
        type: 'window'
      }).then(windowClients => {
        for (var i = 0; i < windowClients.length; i++) {
          const client = windowClients[i];
          if (client.url === url && 'focus' in client)
            return client.focus();
        }
        if (clients.openWindow)
          return clients.openWindow(url);
      })
    );
  }
});
