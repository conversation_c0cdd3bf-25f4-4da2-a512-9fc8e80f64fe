FROM node:16
MAINTAINER Sny <<EMAIL>>
ARG NODE_ENV=production
ENV NPM_CONFIG_LOGLEVEL warn
ENV PORT=${PORT:-3000}
ENV API_URL=${API_URL:-http://127.0.0.1:9000}
ENV ERRBIT_URL=${ERRBIT_URL}
ENV ERRBIT_KEY=${ERRBIT_KEY}
ENV SENTRY_DSN_KEY=${SENTRY_DSN_KEY}
ENV ABLY_KEY=${ABLY_KEY}
ENV CLARITY_ID=${CLARITY_ID}
ENV RSA_PRIVATE_SSH_KEY=${RSA_PRIVATE_SSH_KEY}
ENV FRESHDESK_SHARED_SECRET_KEY=${FRESHDESK_SHARED_SECRET_KEY}
ENV DAS_PRIVATE_KEY=${DAS_PRIVATE_KEY}
ENV NO_UI_LOG=true
RUN mkdir /app
WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH
COPY package.json /app/
COPY package-lock.json /app/
RUN npm ci --production=false --force
COPY start.sh /app/
COPY webpack.config.js /app/
ADD .babelrc /app/
ADD . /app/
RUN chmod +x start.sh
CMD ["sh", "-c", "./start.sh"]
