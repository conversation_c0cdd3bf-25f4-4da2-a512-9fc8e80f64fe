{"name": "agrich<PERSON>", "version": "image-v2.11.9", "private": true, "main": "index.js", "homepage": ".", "dependencies": {"@airbrake/browser": "^2.1.7", "@babel/cli": "^7.0.0-beta.40", "@babel/core": "^7.16.0", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-proposal-object-rest-spread": "^7.16.0", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.16.0", "@babel/preset-react": "^7.16.0", "@babel/runtime": "7.0.0-beta.42", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.54", "@mui/material": "^5.13.1", "@mui/styles": "^5.13.1", "@mui/x-date-pickers": "^6.5.0", "@sentry/browser": "^8.33.0", "@sentry/react": "^8.32.0", "@toast-ui/react-calendar": "^2.1.3", "@vis.gl/react-google-maps": "^1.5.1", "ably": "^1.2.13", "alertifyjs": "^1.13.1", "autosuggest-highlight": "^3.2.0", "axios": "^0.30.0", "babel-loader": "^8.2.2", "chart.js": "2.9.4", "copy-webpack-plugin": "^4.6.0", "core-js": "^3.19.1", "cross-fetch": "^3.1.4", "css-loader": "^2.1.1", "dayjs": "^1.11.7", "file-loader": "^4.0.0", "html-webpack-plugin": "^3.2.0", "jwt-simple": "^0.5.6", "lodash": "^4.17.21", "lz-string": "^1.5.0", "mark.js": "^8.11.1", "mini-css-extract-plugin": "1.6.0", "moment": "2.29.4", "moment-timezone": "^0.5.35", "msal": "^1.4.16", "node-sass": "^7.0.3", "powerbi-client": "^2.23.1", "query-string": "^6.14.1", "react": "^17.0.2", "react-aad-msal": "^2.3.5", "react-autosuggest": "^9.4.3", "react-beautiful-dnd": "^13.1.0", "react-color": "^2.19.3", "react-csv": "^2.2.2", "react-dom": "^16.14.0", "react-draggable": "^4.4.4", "react-hook-form": "^5.7.2", "react-meta-tags": "^0.7.4", "react-microsoft-clarity": "^1.2.0", "react-places-autocomplete": "^7.3.0", "react-print": "^1.3.1", "react-quill": "^1.3.5", "react-redux": "^7.2.6", "react-router-dom": "^5.3.0", "react-virtuoso": "^4.12.3", "react-window": "^1.8.6", "recompose": "^0.30.0", "redux": "^4.1.2", "redux-thunk": "^2.4.1", "sass-loader": "^6.0.6", "signature_pad": "^5.0.9", "stacktrace-js": "^2.0.2", "string-format": "^2.0.0", "style-loader": "^0.23.1", "styled-components": "^4.4.0", "typeface-roboto": "0.0.54", "url-loader": "^4.1.1", "uuid": "^8.3.2", "webpack": "^4.46.0", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.4.0", "big.js": "^6.2.1"}, "scripts": {"start": "node --max-old-space-size=2560 ./node_modules/webpack-dev-server/bin/webpack-dev-server.js --progress --host 0.0.0.0 --port ${PORT} --env API_URL=${API_URL} --env NODE_ENV=${NODE_ENV} --env NO_UI_LOG=${NO_UI_LOG} --env ERRBIT_KEY=${ERRBIT_KEY} --env ERRBIT_URL=${ERRBIT_URL} --env SENTRY_DSN_KEY=${SENTRY_DSN_KEY} --env ABLY_KEY=${ABLY_KEY} --env CLARITY_ID=${CLARITY_ID} --env RSA_PRIVATE_SSH_KEY=${RSA_PRIVATE_SSH_KEY} --env FRESHDESK_SHARED_SECRET_KEY=${FRESHDESK_SHARED_SECRET_KEY} --mode ${NODE_ENV} --hot", "build": "node --max-old-space-size=2560 ./node_modules/webpack/bin/webpack.js --progress --host 0.0.0.0 --port 443 --env API_URL=${API_URL} --env NODE_ENV=${NODE_ENV} --env NO_UI_LOG=${NO_UI_LOG} --env ERRBIT_KEY=${ERRBIT_KEY} --env ERRBIT_URL=${ERRBIT_URL} --env SENTRY_DSN_KEY=${SENTRY_DSN_KEY} --env ABLY_KEY=${ABLY_KEY} --env CLARITY_ID=${CLARITY_ID} --env RSA_PRIVATE_SSH_KEY=${RSA_PRIVATE_SSH_KEY} --env FRESHDESK_SHARED_SECRET_KEY=${FRESHDESK_SHARED_SECRET_KEY} --mode ${NODE_ENV}", "eslint": "./node_modules/.bin/eslint ./src"}, "devDependencies": {"@babel/eslint-parser": "^7.16.0", "eslint": "^8.2.0", "eslint-config-airbnb": "^19.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "^7.27.0", "eslint-plugin-spellcheck": "^0.0.19", "prettier": "^1.19.1", "react-is": "^16.13.1"}}