const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { ProvidePlugin, DefinePlugin, IgnorePlugin } = require('webpack');

module.exports = (env) => {
  return {
    mode: 'production',
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          use: ['babel-loader'],
          exclude: /node_modules/,
        },
        {
          test: /\.mjs$/,
          include: /node_modules/,
          type: 'javascript/auto'
        },
        {
          test: /\.(png|jpg|woff|woff2|eot|ttf|svg)$/,
          loader: 'url-loader'
        },
        {
          test: /\.(scss|css)$/,
          use: [{
            loader: 'style-loader', // creates style nodes from JS strings
          }, {
            loader: 'css-loader', // translates CSS into CommonJS
          }, {
            loader: 'sass-loader', // compiles Sass to CSS
          }],
        },
      ],
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        minSize: 100000,
        minChunks: 1,
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
        cacheGroups: {
          default: false,
          vendors: false,
          react: {
            chunks: 'all',
            test: /[\\/]node_modules[\\/](react|react-dom|react-router-dom)[\\/]/,
            name: "react"
          },
          lodash: {
            chunks: 'all',
            test: /[\\/]node_modules[\\/](lodash)[\\/]/,
            name: "lodash"
          },
          moment: {
            chunks: 'all',
            test: /[\\/]node_modules[\\/](moment|moment-timezone)[\\/]/,
            name: "moment",
            reuseExistingChunk: true
          },
          mui: {
            chunks: 'all',
            test: /[\\/]node_modules[\\/](@mui[\\/]material)[\\/]/,
            name: "mui",
            reuseExistingChunk: true
          },
          muiIcons: {
            chunks: 'all',
            test: /[\\/]node_modules[\\/](@mui[\\/]icons-material)[\\/]/,
            name: "mui-icons",
            reuseExistingChunk: true
          },
          muiStyles: {
            chunks: 'all',
            test: /[\\/]node_modules[\\/](@mui[\\/]styles)[\\/]/,
            name: "mui-styles",
            reuseExistingChunk: true
          },
          muiLab: {
            chunks: 'all',
            test: /[\\/]node_modules[\\/](@mui[\\/]lab)[\\/]/,
            name: "mui-lab",
            reuseExistingChunk: true
          },
          vendor: {
            name: 'vendor',
            chunks: 'all',
            test: /[\\/]node_modules[\\/](!react)(!react-dom)(!react-router-dom)(!@mui)(!lodash)(!moment)(!moment-timezone)[\\/]/,
            reuseExistingChunk: true,
            priority: 20
          }
        }
      }
    },
    devServer: {
      compress: env.NODE_ENV == 'production',
      client: {
        overlay: {
          errors: true,
          warnings: false,
        },
      },
      static: {
        directory: path.resolve(__dirname, "public"),
        staticOptions: {}
      },
      allowedHosts: 'all',
      historyApiFallback: {
        index: 'index.html',
      },
    },
    devtool: env.NODE_ENV == 'production' ? "source-map" : undefined,
    plugins: [
      new HtmlWebpackPlugin({
        template: './public/index.html',
      }),
      new MiniCssExtractPlugin({filename: 'bundle.css'}),
      new CopyWebpackPlugin(['src/assets']),
      new ProvidePlugin({
        $: 'jquery',
        jQuery: 'jquery',
        'window.jQuery': 'jquery',
        Popper: ['popper.js', 'default'],
      }),
      new DefinePlugin({
        'process.env.API_URL': JSON.stringify(env.API_URL),
        'process.env.NODE_ENV': JSON.stringify(env.NODE_ENV) || 'dev',
        'process.env.NO_UI_LOG': JSON.stringify(env.NO_UI_LOG),
        'process.env.ERRBIT_KEY': JSON.stringify(env.ERRBIT_KEY),
        'process.env.ERRBIT_URL': JSON.stringify(env.ERRBIT_URL),
        'process.env.ABLY_KEY': JSON.stringify(env.ABLY_KEY),
        'process.env.CLARITY_ID': JSON.stringify(env.CLARITY_ID),
        'process.env.RSA_PRIVATE_SSH_KEY': JSON.stringify(env.RSA_PRIVATE_SSH_KEY),
        'process.env.FRESHDESK_SHARED_SECRET_KEY': JSON.stringify(env.FRESHDESK_SHARED_SECRET_KEY),
        'process.env.GOOGLE_MAPS_API_KEY': JSON.stringify(env.GOOGLE_MAPS_API_KEY), /*eslint no-undef: 0*/
        'process.env.DAS_PRIVATE_KEY': JSON.stringify(env.DAS_PRIVATE_KEY),
        'process.env.SENTRY_DSN_KEY': JSON.stringify(env.SENTRY_DSN_KEY),
      }),
      new IgnorePlugin(/^\.\/locale$/, /moment$/)
    ],
    resolve: {
      extensions: ['.js', '.jsx'],
    },
  };
};
