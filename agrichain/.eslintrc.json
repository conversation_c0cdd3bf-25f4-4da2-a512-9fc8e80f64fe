{"parser": "@babel/eslint-parser", "extends": ["eslint:recommended", "plugin:react/recommended", "prettier"], "settings": {"react": {"version": "16.4.2"}}, "env": {"browser": true, "es6": true}, "globals": {"it": true, "alertify": true, "window": true, "document": true, "localStorage": true, "FormData": true, "FileReader": true, "Blob": true, "navigator": true, "Headers": true, "Request": true, "fetch": true, "globalThis": true}, "plugins": ["react", "prettier", "spellcheck"], "parserOptions": {"ecmaVersion": 6, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "rules": {"import/prefer-default-export": 0, "import/no-named-as-default": 0, "react/prefer-stateless-function": 0, "no-process-env": "error", "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx"]}], "react/prop-types": 0, "no-underscore-dangle": 0, "react/display-name": "off", "react/jsx-key": "off", "spellcheck/spell-checker": ["error", {"comments": true, "strings": true, "identifiers": true, "templates": true, "lang": "en_US", "skipWords": ["<PERSON><PERSON>", "pendo", "dict", "aff", "hunspellchecker", "hunspell", "utils", "alertify", "alertifyjs", "axios", "Minimalistic", "<PERSON>r", "Ngrs", "bhc", "Freshdesk", "platformfeatures", "farmfield", "fieldset", "inload", "outload", "outloading", "inloading", "inloads", "outloads", "storages", "fm", "fms", "<PERSON><PERSON><PERSON>", "msg", "pdf", "pdfs", "rego", "regos", "dec", "decs", "unregister", "aba", "upsert", "unlink", "unlinked", "freightorder", "freightcontract", "freightcontracttype", "freightorders", "freightcontracts", "msmall", "xsmall", "xxsmall", "xxxsmall", "xlarge", "xxlarge", "validator", "validators", "sso", "msal", "uri", "uris", "url", "urls", "uuidv4", "abn", "bsb", "marketzones", "marketzone", "companysetup", "farmsetup", "cors", "sys", "str", "tooltip", "xero", "rgb", "quaternary", "datetime", "shrinkages", "gst", "epr", "eprs", "ascii", "realtime", "undef", "xhr", "evenodd", "svg", "unix", "agrich<PERSON>", "grey", "mui", "webpush", "warehousefee", "vendee", "num", "delhi", "payment", "unshrunk", "pathname", "func", "lightgray", "checkbox", "unmark", "integrations", "repost", "href", "unreg", "dom", "gmo", "unmount", "dont", "vendordec", "counterparties", "analysed", "showoptions", "jsonify", "companysite", "swipeable", "ghms", "creatable", "initialise", "timezone", "timezones", "pragma", "Uint8", "invoicable", "createable", "requestorder", "reseason", "reseasoned", "footertext", "ownerships", "datacallback", "itemfor", "iat", "jti", "packagings", "aobserver", "receival", "receivals", "impu", "confirmable", "amendable", "voidable", "centre", "noopener", "<PERSON><PERSON><PERSON><PERSON>", "agri", "formatter", "formater", "tui", "ajs", "subcontainer", "vert", "sublabel", "csv", "droppable", "listbox", "struct", "sms", "gte", "lte", "deliverysite", "pickupsite", "scriptjs", "<PERSON><PERSON><PERSON>", "waypoints", "geo", "notbooked", "parentdomid", "subtree", "<PERSON><PERSON>", "rect", "klass", "unselect", "unselected", "subtab", "transferrable", "bool", "authorise", "authorised", "signup", "simplesignup", "apca", "dup", "debounce", "searchable", "emp", "atleast", "filestate", "8zm4", "cancelled", "changeset", "changelog", "nums", "homestorage", "systemstorage", "loc", "donut", "barcode", "mousedown", "mouseup", "keydown", "keyup", "onchange", "customisation", "customisations", "customisable", "customise", "customised", "unarchive", "unhide", "blockquote", "viewable", "utcoffset", "65vh", "daynames", "meridiem", "dataset", "elementid", "readonly", "txt", "<PERSON><PERSON><PERSON><PERSON>", "incompletion", "unmatch", "tmsx", "incompleted", "rss", "els", "timegrid", "navi", "unallocated", "crm", "kms", "submittable", "margintop", "exc", "multivalue", "curr", "ident", "workspace", "orderdetails", "bto", "handledelivery", "78vh", "isoweek", "mis", "nowrap", "rms", "outturn", "outturns", "inturn", "inturns", "billto", "pos", "rtl", "multiline", "fff", "setselected", "subheader", "cartesian", "familyname", "givenname", "cashboard", "rcti", "customitem", "warehousestorageitem", "warehousetransferitem", "warehouseinloaditem", "warehousethroughputinloaditem", "warehouseoutloaditem", "warehousethroughputoutloaditem", "reasonobject", "payables", "grainlevy", "titletransfer", "canolaloads", "carryitem", "businessname", "darkgray", "dropdown", "snackbar", "<PERSON><PERSON>", "clearable", "autoselect", "autosuggest", "backtrace", "isopen", "calc", "cro", "subtype", "<PERSON><PERSON>", "Tmt", "deburr", "Hoc", "isnull", "closable", "behaviour", "autofill", "farmemployee", "lloyed", "rfp", "overweights", "openid", "subscriptionitem", "brokerageinvoice", "noshow", "resize", "enquiries", "customer’s", "provider’s", "seller’s", "buyer’s", "tts", "fms", "acc", "curr", "airbrake", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errbit", "dsn", "Tri", "rinstrum", "mettler", "onloadend", "fullscreenchange", "bgcolor", "epritem", "stockswap", "regradereseason", "Reseasons", "warehousestockswapitem", "warehouseregradereseasonitem", "aus", "dayjs", "datasets", "ctx", "stor", "grainstor", "grainsoft", "Unarchived", "iexact", "litre", "moistures", "Moistures", "Hrefs", "grainlevies", "<PERSON>by", "recommodity", "gta", "octopusbot", "lll", "tippy", "impex", "impexdocs", "scroller", "fis", "daff", "ifs", "girdf", "gif", "abns", "ccc", "das", "shipmentdocs", "shipmentfolderdetail", "lloy<PERSON>", "incoterms", "Refrence", "monospace", "autoweigh", "powerbi", "hpm", "wpmp", "mailto", "Resending", "bec", "grainscanada", "declarant", "<PERSON><PERSON><PERSON>", "redux", "pointerdown", "commodityvendordec"], "skipIfMatch": ["http://[^s]*", "^[-\\w]+/[-\\w\\.]+$"], "skipWordIfMatch": [".*Uint8.*$", ".*[\\d]+.*"], "minLength": 3}]}}