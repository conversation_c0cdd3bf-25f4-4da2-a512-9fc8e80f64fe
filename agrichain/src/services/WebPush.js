/*eslint no-process-env: 0*/
/*eslint no-undef: 0*/
const APIURL = process.env.API_URL;

export const registerWebpushServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    const reg = await navigator.serviceWorker.register('/sw.js');
    initialiseState(reg);
  }
  // eslint-disable-next-line no-console
  else console.log("You can't send push notifications!");
};

const initialiseState = reg => {
  if (!reg.showNotification) {
    // eslint-disable-next-line no-console
    console.log("Showing notifications isn't supported");
    return;
  }
  if (Notification.permission === 'denied') {
    // eslint-disable-next-line no-console
    console.log('You prevented us from showing notifications');
    return;
  }
  if (!('PushManager' in window)) {
    // eslint-disable-next-line no-console
    console.log("Push isn't allowed in your browser");
    return;
  }
  subscribe(reg);
};

const urlB64ToUint8Array = base64String => {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding)
    // eslint-disable-next-line no-useless-escape
    .replace(/\-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  const outputData = outputArray.map((output, index) => rawData.charCodeAt(index));

  return outputData;
};

const subscribe = async reg => {
  const subscription = await reg.pushManager.getSubscription();
  if (subscription) {
    sendSubData(subscription);
    return;
  }

  const vapidMeta = document.querySelector('meta[name="vapid-key"]');
  // eslint-disable-next-line no-unused-vars
  const userIdMeta = document.querySelector('meta[name="user-id"]');
  const key = vapidMeta.content;
  const options = {
    userVisibleOnly: true,
    // if key exists, create applicationServerKey property
    ...(key && { applicationServerKey: urlB64ToUint8Array(key) }),
  };

  const sub = await reg.pushManager.subscribe(options);
  sendSubData(sub);
};

const sendSubData = async subscription => {
  const browser = navigator.userAgent.match(/(firefox|msie|chrome|safari|trident)/gi)[0].toLowerCase();
  const data = {
    status_type: 'subscribe',
    subscription: subscription.toJSON(),
    browser: browser,
  };

  const res = await fetch(`${APIURL}/webpush/save_information`, {
    method: 'POST',
    body: JSON.stringify(data),
    headers: {
      'content-type': 'application/json',
      Authorization: 'Token ' + localStorage.token,
    },
  });
  // eslint-disable-next-line no-console
  console.log(res.status);
};
