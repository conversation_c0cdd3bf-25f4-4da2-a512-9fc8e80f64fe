import Big from "big.js"

class Number {
  constructor(value, currency, unit, suffix, defaultValue='0') {
    this._rawValue = value
    this.number = 0
    this.value = value
    this.currency = currency
    this.unit = unit
    this.suffix = suffix
    this.defaultValue = defaultValue || 'N/A'
    this.formatted = null
    this._compute()
  }

  isNA = () => this.value === 'N/A'

  _compute() {
    this._toVal()
    this.formatted = this._format()
  }

  recompute() {
    this.value = this._rawValue
    this._compute()
  }

  _toVal() {
    if(!this.value && this.value !== 0)
      this.value = this.defaultValue

    if(this.isNA())
      return

    let str = this.value.toString().replace(/[^\d.-]/g, '');
    this.number = parseFloat(str)
    const isBelowZero = this.number < 0
    let num = parseFloat(Math.abs(str)).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    this.value = isBelowZero ? `-${num}` : num;
  }

  _format() {
    if(this.isNA())
      return this.value

    let val = ''
    if(this.currency)
      val += this.currency + ' '
    if(this.number < 0 && this.currency)
      val = '-' + val + Math.abs(this.value)
    else
      val += this.value
    val += this.suffix || ''
    val += this.unit || ''

    return val
  }

  static add(...args) {
    if (args.length === 0) return 0;
    return args.reduce((sum, val) => {
      return sum.plus(new Big(val));
    }, new Big(0)).toNumber();
  }

  static subtract(a, b) {
    return new Big(a).minus(new Big(b)).toNumber();
  }

  static multiply(...args) {
    if (args.length === 0) return 0;
    return args.reduce((product, val) => {
      return product.times(new Big(val));
    }, new Big(1)).toNumber();
  }

  static divide(a, b) {
    if (parseFloat(b) === 0) return NaN;
    return new Big(a).div(new Big(b)).toNumber();
  }

  static sumBy(array, key) {
    if (!array || !array.length) return 0;
    return array.reduce((sum, item) => {
      let value;

      if (typeof key === 'function') {
        value = key(item);
      } else if (typeof key === 'string') {
        value = item[key];
      } else {
        value = item;
      }

      if (value == null) return sum;
      return sum.plus(new Big(value));
    }, new Big(0)).toNumber();
  }
}

export default Number;
