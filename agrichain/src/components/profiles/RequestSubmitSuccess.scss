@import '../../common/variables.scss';

.request-wrapper{
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1200;
    background: url('/images/bg-login.jpg') left center no-repeat $colorWhite;
    background-size: cover;
    overflow: scroll;

    .request-container {
      position: absolute;
      top: 0;
      bottom: 45px;
      right: 0;
      left: 0;
      margin: auto;
      max-width: 853px;
      max-height: 525px
    }

  }

  .request-content-box {
    height: 520px;
    width: 852px;
    border-radius: 4px;
    background-color: #FFFFFF;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.26), 0 2px 10px 0 rgba(0,0,0,0.16);

    &--header {
      background: $colorBlack;
      height: 64px;
      padding-top: 13px;
      border-radius: 4px 4px 0 0;

      img {
        display: block;
        max-width: 100%;
        height: 40px;
        margin: 0 auto;
      }
    }

    .group-3 {
      height: 24px;
      width: 419px;
      margin-left: 217px;
      margin-top: 27px;

      .group {
        height: 24px;
        width: 24px;
        float:left;
      }

      .vendor-declaration-s {
          margin-left: 5px;
          height: 24px;
          width: 386px;
          color: #6AAE20;
          font-family: Roboto;
          font-size: 20px;
          font-weight: 500;
          letter-spacing: 0;
          line-height: 24px;
          text-align: center;
          float:left;
      }
  }

    .manage-and-automate {
      margin-left: 51px;
      margin-top: 37px;
      height: 66px;
      width: 750px;
      color: #112C42;
      font-family: Roboto;
      font-size: 28px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 33px;
      text-align: center;
    }

    .group-17 {
        height: 149px;
        width: 796px;
        margin-left: 28px;
        margin-top: 41px;

      .group-16 {
        margin-left: 0px;
        margin-top: 0px;
        height: 149px;
        width: 166px;
        float:left;

        .smartphone-1 {
          margin-left: 33px;
          margin-top: 0px;
          height: 100px;
          width: 100px;
        }
        .automate-your-chain {
          margin-top: 13px;
          height: 36px;
          width: 166px;
          color: #595F6F;
          font-family: Roboto;
          font-size: 14px;
          letter-spacing: 0;
          line-height: 18px;
          text-align: center;
        }
      }
      .group-15 {
        margin-left: 44px;
        margin-top: 0px;
        height: 149px;
        width: 166px;
        float:left;

        .graphs {
          margin-left: 33px;
          margin-top: 0px;
          height: 100px;
          width: 100px;
        }
        .view-your-total-on-f {
            margin-top: 13px;
            height: 36px;
            width: 166px;
            color: #59606F;
            font-family: Roboto;
            font-size: 14px;
            letter-spacing: 0;
            line-height: 18px;
            text-align: center;
          }
      }
      .group-7 {
        height: 149px;
        width: 166px;
        margin-left: 44px;
        float:left;

        .silo5 {
          margin-left: 45px;
          height: 100px;
          width: 77px;
        }

        .accurately-blend-you {
          margin-top: 13px;
          height: 36px;
          width: 166px;
          color: #595F6F;
          font-family: Roboto;
          font-size: 14px;
          letter-spacing: 0;
          line-height: 18px;
          text-align: center;
        }
      }

      .group-6 {
        height: 149px;
        width: 166px;
        margin-left: 44px;
        float:left;

        .contract {
          margin-left: 33px;
          margin-top: 0px;
          height: 100px;
          width: 120px;
        }

        .automatically-update {
          margin-top: 13px;
          height: 36px;
          width: 166px;
          color: #595F6F;
          font-family: Roboto;
          font-size: 14px;
          letter-spacing: 0;
          line-height: 18px;
          text-align: center;
        }
      }
    }

    .rectangle-3 {
      left: 205px;
      top: 45px;
      height: 36px;
      width: 192px;
      border-radius: 4px;
      background-color: #6AAE20;
    }

    .buttons-text-secondary-enabled {
      left: 244px;
      top: 45px;
      height: 36px;
      width: 192px;
    }

    &--footer {
      padding-top: 24px;
    }
  }

  .for-help-or-enquiries {
    margin-left: 199px;
    margin-top: 10px;
    height: 14px;
    width: 454px;
    opacity: 0.8;
    color: #FFFFFF;
    font-family: Roboto;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 14px;
    text-align: center;
  }
