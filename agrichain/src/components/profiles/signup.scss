@import '../../common/variables.scss';

.signup-wrapper{
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1200;
  background: url('/images/bg-login.jpg') left center no-repeat $colorWhite;
  background-size: cover;
  overflow: scroll;

  .signup-container {
    position: absolute;
    top: 0;
    bottom: 45px;
    right: 0;
    left: 0;
    margin: auto;
    max-width: 880px;
    max-height: 704px
  }

  .bullet_points {
    font-size: 12px;
    margin-top: -7px;
    color: #808080;
  }
}

.signup-content-box {
  background: $colorWhite;
  border-radius: 4px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.16), 0 2px 5px 0 rgba(0, 0, 0, 0.26);

  &--header {
    background: $colorBlack;
    height: 64px;
    padding-top: 13px;
    border-radius: 4px 4px 0 0;

    img {
      display: block;
      max-width: 100%;
      height: 40px;
      margin: 0 auto;
    }
  }

  &--content {
    padding: 18px 40px 18px;

    h2, p {
      margin: 0;
      padding: 0;
    }

    h2 {
      color: $colorGreen;
      font-size: 28px;
      font-weight: 500;
      line-height: 33px;
      margin-bottom: 8px;
    }

    p {
      line-height: 16px;
      font-size: 14px;
      color: $colorLightGrey;
      margin-bottom: 32px;
    }
  }

  &--footer {
    padding-top: 24px;
  }
}

.signup-business-type-tick {
  position: relative;
  display: block;
  z-index: 2;
  left: 80px;
  top: -18px;
  background-color: #fff;

}

.sign-up-success-tick {
  text-align: center;
  height: 400px;
  margin-top: 67px;
}

.sign-up-form-height {
  min-height: 550px;
}

.fixed-bottom {
  position: absolute;
  bottom: 0;
}

.sign-up-success-heading {
  height: 33px;
  margin-top: 17px;
  font-size: 28px;
  font-weight: 500;
  font-style: normal;
  font-stretch: normal;
  line-height: normal;
  letter-spacing: normal;
  color: $colorGreen;
}

.sign-up-success-text {
  font-size: 14px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  color: #112c42;
}

.sign-up-success-login-button {
  height: 36px;
  border-radius: 4px;
  background-color: #6aae20;
}

.image-tile-card {
  text-align: center;
  padding-top: 24px;
  height: 184px;
  border: rgb(106,174,32);
}

.image-tile-child {
  height: 80px;
  svg {
    width: 90px;
  }
}

.margin-top-20 {
  margin-top: 20px;
}

.question-margin-top {
  margin-top: 40px;
  height: 70px;
}

.question-checkbox {
  margin-top: 27px;
  margin-left: -14px;
}

.numeric-field {
  //width: 167px;
  height: 30px;
}

.padding-left-zero{
  padding-left: 0px !important;
}

.bullet-points-heading {
  font-weight: 500;
  color: #112c42;
}

.business-details-heading {
  margin-top: 13px;
  margin-bottom: 13px;
}

.padding-right-10 {
  padding-right: 10px !important;
}

.tailor-margin {
  //width: 720px;
  //margin: 0 40px;
}

.sign-up-field {
  //max-width: 344px;
  width: 100%;
  height: 80px
}

.sign-up-right-field {
  //margin-left: 40px;
}

.so-we-can-tailor-you {
  width: 599px;
  height: 19px;
  font-size: 16px;
  font-weight: bold;
  font-style: normal;
  font-stretch: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #112c42;
}

.tailors-text {
  //width: 720px;
  height: 80px;
  font-size: 14px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #112c42;
}
