div.stat-card {
    margin: 2px;
    width: 25%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    div.stat-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
        padding: 8px !important;
        padding-bottom: 16px !important;
        div.stat-primary {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            div.stat-param {
                display: flex;
                margin: 5px 0;
                text-align: center;
                justify-content: center;
                width: 100%;
                span.width-50 {
                    width: 50%;
                }
                span {
                    display: flex;
                    flex-direction: column;
                }
            }
        }
    }
    div.stat-icon {
        display: flex;
        border-radius: 50%;
        -webkit-box-align: center;
        align-items: center;
        width: 64px;
        height: 64px;
        -webkit-box-pack: center;
        justify-content: center;
        svg {
            width: 30px;
            height: 30px;
        }
    }
}
div.dashboard-main {
    @media screen and (max-width: 850px) {
        .time-filter-container {
            display: none !important;
        }
        .company-search {
            width: 100% !important;
        }
        .stats-row {
            flex-direction: column;
            .stat-card {
                width: 100%;
            }
        }
    }

}
