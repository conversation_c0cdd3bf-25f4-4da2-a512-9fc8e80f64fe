import React from 'react';
import { connect } from 'react-redux';
import '../common/subTab.scss';

import CashPrices from './CashPricesTab';
import CommonTabs from '../common/CommonTabs';
import { setHeaderText, setBreadcrumbs, setSubHeaderText } from '../../actions/main';
import { getTracks } from '../../actions/main/index';
import ContractBidsTab from '../contract-bids/ContractBidsTab';
import { includes } from 'lodash';

class CashBoard extends React.Component {
  constructor(props) {
    super(props);

    this.CASH_PRICES_URL = '/cash-board/cash-prices';
    this.MY_PRICES_URL = '/cash-board/cash-prices/active';
    this.CASH_PRICES_URL = '/cash-board/cash-prices';
    this.MY_PRICES_SITES_URL = '/cash-board/cash-prices/site-active-prices';
    this.ARCHIVED_PRICES_URL = '/cash-board/cash-prices/archived';
    this.SITE_ARCHIVED_PRICES_URL = '/cash-board/cash-prices/site';
    this.CONTRACT_BIDS_URL = '/contract-bids';
    this.MY_ACTIVE_CONTRACT_BIDS = '/contract-bids/active';
    this.ARCHIVED_CONTRACT_BIDS = '/contract-bids/archived';
    this.state = {
      activeTab: this.props.location.pathname,
    };
  }
  componentDidMount() {
    this.props.dispatch(getTracks());
    this.setHeader();
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    return nextProps.location.pathname !== prevState.activeTab ? { ...prevState, activeTab: nextProps.location.pathname } : prevState;
  }
  handleTabChange = value => this.setState(state => ({ ...state, activeTab: value }));

  setHeader() {
    this.setHeaderText();
    this.setBreadcrumbs();
  }

  setHeaderText() {
    const { dispatch } = this.props;
    dispatch(setHeaderText('Cash Board'));
    dispatch(setSubHeaderText(''));
  }

  setBreadcrumbs() {
    this.props.dispatch(setBreadcrumbs([{ text: 'Cash Board' }]));
  }

  render() {
    const tabs = [
      { label: 'Cash Prices', url: `${this.CASH_PRICES_URL}`, component: () => <CashPrices {...this.props}/> },
      { label: 'Contract Bids', url: `${this.CONTRACT_BIDS_URL}`, component: () => <ContractBidsTab {...this.props} /> },
  ];
  const cashPricesUrls = [this.MY_PRICES_URL, this.CASH_PRICES_URL, this.ARCHIVED_PRICES_URL, this.SITE_ARCHIVED_PRICES_URL, this.MY_PRICES_SITES_URL];
  const contractBidsUrls = [this.CONTRACT_BIDS_URL, this.MY_ACTIVE_CONTRACT_BIDS, this.ARCHIVED_CONTRACT_BIDS];
  const tabValue = this.props.location.pathname ? (this.props.location.pathname.includes('/cash-board/cash-prices') ? '/cash-board/cash-prices': (this.props.location.pathname.includes(this.CONTRACT_BIDS_URL) ? this.CONTRACT_BIDS_URL : this.props.location.pathname)): this.props.location.pathname;
  return (
      <div className="tab">
        <div className='tab-header'>{<CommonTabs value={tabValue} tabs={tabs} />}</div>
        <div key={0} className='tab-content'>
          {includes(cashPricesUrls, this.state.activeTab) && <CashPrices {...this.props} />}
          {includes(contractBidsUrls, this.state.activeTab) && <ContractBidsTab {...this.props} />}
        </div>
      </div>
    );
  }
}

const mapStateToProps = state => {
  return {
    user: state.main.user.user,
    breadcrumbs: state.main.breadcrumbs,
    headerText: state.main.headerText,
  };
};

export default connect(mapStateToProps)(CashBoard);
