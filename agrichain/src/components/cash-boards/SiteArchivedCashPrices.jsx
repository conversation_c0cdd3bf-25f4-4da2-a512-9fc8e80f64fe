import React, { Component } from 'react';
import { connect } from 'react-redux';
import CashPricesTable from '../../containers/CashPricesTable';
import { TAB } from './constants';
import SideDrawer from '../common/SideDrawer';
import {
  editSelectedCashPrice, setSelectedCashPrice, viewSelectedCashPrice, getSiteArchivedCashPrices, siteArchivedCashPricesResponse
} from '../../actions/companies/cash-board';
import AddPriceDialog from './AddPriceDialog';
import Paper from '@mui/material/Paper';
import { isLoading, forceStopLoader } from '../../actions/main';
import Filters from '../common/Filters';
import APIService from '../../services/APIService';
import { get } from 'lodash';
import { defaultViewAction } from '../../common/utils';
import ListingControls from '../common/ListingControls'
import FiltersAppliedChip from '../common/FiltersAppliedChip';

class SiteArchivedCashPrices extends Component {
  constructor(props) {
    super(props);
    this.state = {
      openCloseAddPriceDialog: false,
      applyFilters: false,
      openSideDrawer: false,
      filters: {},
      filterValues: {
        commodity__id__in: [],
        grade__id__in: [],
        season__in: [],
        track__in: [],
        buyer__company__id__in: [],
        site__id__in: [],
      },
    };
    this.closeSideDrawer = this.closeSideDrawer.bind(this);
  }

  componentDidMount() {
    APIService.profiles()
      .filters('site_archived_cash_price')
      .get(this.props.token)
      .then(res => {
        this.setState({
          filters: get(res, 'site_archived_cash_price', {}),
        });
      });
    this.props.getSiteArchivedCashPrices('', true);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.isViewSelectedCashPrice !== this.props.isViewSelectedCashPrice)
      this.setState({ openCloseAddPriceDialog: this.props.isViewSelectedCashPrice });
  }

  closeSideDrawer() {
    this.setState({ openCloseAddPriceDialog: !this.state.openCloseAddPriceDialog });
    this.props.editSelectedCashPrice(false);
    this.props.viewSelectedCashPrice(false);
    this.props.setSelectedCashPrice([]);
  }


  componentWillUnmount() {
    this.props.setCashPricesResponse([]);
    this.props.editSelectedCashPrice(false);
    this.props.viewSelectedCashPrice(false);
    this.props.setSelectedCashPrice([]);
  }

  handleFilters = bool => {
    this.setState({
      applyFilters: bool,
      openSideDrawer: bool,
    });
  };

  resetFilters = () => {
    this.setState({filters: {}, applyFilters: false, openSideDrawer: false}, () => this.handleFilterState('applyFilters', false))
  }

  handleFilterState = (key, value) => {
    this.setState({ [key]: value }, () => {
      if (key === 'applyFilters') {
        const { filters } = this.state;
        APIService.profiles()
          .filters()
          .post({ site_archived_cash_price: filters }, this.props.token)
          .then(res => {
            this.setState({filters: res?.filters?.site_archived_cash_price || {}}, () => {
              this.props.isLoading();
              this.props.setCashPricesResponse([]);
              this.props.getSiteArchivedCashPrices();
            })
          });
      }
    });
  };

  getActionsOptionMapperListItems() {
    return [defaultViewAction];
  }

  render() {
    return (
      <Paper className='paper-table-paginated'>
        <div style={{ position: 'relative' }}>
          <div style={{ position: 'absolute', right: '0px', top: '0px' }}>
            <ListingControls
              controls={[
                {
                  type: 'filter',
                  props: {
                    value: this.state.applyFilters,
                    onClick: () => this.handleFilters(true),
                    applied: Object.entries(this.state.filters).filter(val => val[1].length !== 0)?.length || 0,
                  }
                },
                {
                  type: 'action',
                  props: {
                    showMenus: true,
                    optionMapper: this.getActionsOptionMapperListItems()
                  }
                },
              ]}
            />
          </div>

          {
            this.state.applyFilters &&
              <SideDrawer isOpen={this.state.openSideDrawer} title='Filters' size='big' onClose={() => this.handleFilters(false)} app='filters'>
                <Filters
                  isLoading={this.props.isLoading}
                  forceStopLoader={this.props.forceStopLoader}
                  handleFilterState={this.handleFilterState}
                  filters={this.state.filters}
                  tracks={this.props.tracks}
                  filterValues={this.state.filterValues}
                />
              </SideDrawer>
          }
        </div>
        <FiltersAppliedChip filters={this.state.filters} show style={{paddingRight: '45%'}} onClear={this.resetFilters} />
        <CashPricesTable isAllCashPriceTab={TAB.SITE_ARCHIVED_CASH_PRICE} />

        {
          this.props.isViewSelectedCashPrice &&
            <SideDrawer
              isOpen={this.state.openCloseAddPriceDialog}
              title="View Price"
              onClose={this.closeSideDrawer}
              size='small'>
              <AddPriceDialog onClose={this.closeSideDrawer} isAllCashPriceTab={TAB.SITE_ARCHIVED_CASH_PRICE} />
            </SideDrawer>
        }
      </Paper>
    );
  }
}

const mapStateToProps = state => {
  return {
    user: state.main.user.user,
    tracks: state.main.tracks,
    isViewSelectedCashPrice: state.companies.cashBoard.viewSelectedCashPrice,
  };
};

const mapDispatchToProps = dispatch => ({
  getSiteArchivedCashPrices: (query, loader) => dispatch(getSiteArchivedCashPrices(query, loader, null)),
  setCashPricesResponse: () => dispatch(siteArchivedCashPricesResponse([])),
  editSelectedCashPrice: flag => dispatch(editSelectedCashPrice(flag)),
  setSelectedCashPrice: () => dispatch(setSelectedCashPrice([])),
  viewSelectedCashPrice: flag => dispatch(viewSelectedCashPrice(flag)),
  isLoading: (component) => dispatch(isLoading(component)),
  forceStopLoader: () => dispatch(forceStopLoader()),
});

export default connect(mapStateToProps, mapDispatchToProps)(SiteArchivedCashPrices);
