import React from 'react'
import ListItem from '@mui/material/ListItem'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import Tooltip from '@mui/material/Tooltip'
import AddIcon from '@mui/icons-material/Add'
import SubscriberIcon from '@mui/icons-material/VerifiedUser';
import RegisteredIcon from '@mui/icons-material/PrivacyTip';
import FarmIcon from '../common/icons/Farm'
import { BLACK } from '../../common/constants'


const FarmListItem = ({farm, ...rest}) => {
  return (
    <ListItem {...rest}>
      <ListItemIcon sx={{minWidth: 'auto', marginRight: '16px'}}>
        {
          farm?.transactionParticipation ?
            <Tooltip title='Subscriber'>
              <SubscriberIcon color='success' />
            </Tooltip>:
          (
            farm.isRegistered ?
              <Tooltip title='Registered'>
                <RegisteredIcon color='warning' />
              </Tooltip>:
            (
              farm?.id ?
                <FarmIcon fill={BLACK} title='Site' /> :
              <AddIcon />
            )
          )
        }
      </ListItemIcon>
      <ListItemText
        primary={farm.rawName || farm.name || farm.displayName}
        secondary={farm.companyName || farm?.company?.name || farm?.company?.displayName || ''}
      />
      {
        farm?.stocksManagement &&
          <span className='ellipsis-line-2' style={{fontSize: '10px', color: 'rgba(0, 0, 0, 0.6)', maxWidth: '100px'}}>
            AgriChain Managed
          </span>
      }
    </ListItem>
  )
}

export default FarmListItem
