@import '../../common/variables.scss';

.error-message{
    color: rgb(244, 67, 54);
}
.login-wrapper{
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1981;
  background: url('/images/bg-login.jpg') left center no-repeat $colorWhite;
  background-size: cover;

  .login-container {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 536px;
    min-width: 350px;
  }

  .signup-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70%;
  }

  .cashboard-container {
    position: absolute;
    top: 80%;
    left: 50%;
    transform: translate(-52%, -52%);
    border-radius: 3px;
    background-color: white;
    width: 16%
  }
}

.login-content-box {
  background: $colorWhite;
  border-radius: 4px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.16), 0 2px 5px 0 rgba(0, 0, 0, 0.26);

  &--header {
    background: $colorBlack;
    height: 64px;
    padding-top: 13px;
    border-radius: 4px 4px 0 0;
    display: inline-flex;
    justify-content: center;
    width: 100%;

    img {
      max-width: 100%;
      height: 40px;
    }
  }

  &--content {
    padding: 30px 40px;

    h2, p {
      margin: 0;
      padding: 0;
    }

    h2 {
      color: $colorGreen;
      font-size: 28px;
      font-weight: 500;
      line-height: 33px;
      margin-bottom: 8px;
    }

    p {
      line-height: 16px;
      font-size: 14px;
      color: $colorLightGrey;
      margin-bottom: 32px;
    }
  }
}


.login-button {
    width: 50%;
    margin-left: 0 !important;
}
