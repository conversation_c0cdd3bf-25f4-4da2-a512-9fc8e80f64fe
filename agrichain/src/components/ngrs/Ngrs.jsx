import React from 'react';

import { connect } from 'react-redux';
import NgrsTable from '../../containers/NgrsTable';
import CreateNgr from '../../containers/CreateNgr';
import UpdateNgr from '../../containers/UpdateNgr';
import { getCompanyNgrs } from '../../actions/api/company-ngrs';
import {
  receiveNgrs,
  cancelEditNgr,
  clickAddNgrButton
} from '../../actions/companies/ngrs';
import AddButton from '../common/AddButton';
import Paper from '@mui/material/Paper';
import {setBreadcrumbs} from '../../actions/main';
import isEqual from 'lodash/isEqual';
import {getCompanyIdFromCurrentRoute, isSystemCompany} from '../../common/utils';
import { getCompanyDetails } from '../../actions/companies';
import SideDrawer from '../common/SideDrawer';

class Ngrs extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      openCreateSideDrawer: false,
    };
    this.farmId = this.props.farmId;
    this.handleAddNgrButtonClick = this.handleAddNgrButtonClick.bind(this);
    this.onCloseSideDraw = this.onCloseSideDraw.bind(this);
  }

  componentDidMount() {
    this.getCompanyIfNeeded();
    this.setBreadcrumbs();
    this.props.getNgrs(this.farmId);
  }
  componentDidUpdate() {
    this.setBreadcrumbs();
  }

  getCompanyIfNeeded() {
    if(getCompanyIdFromCurrentRoute() && (!this.props.selectedCompany || this.props.selectedCompany.id !== parseInt(getCompanyIdFromCurrentRoute()))) {
      const companyId = getCompanyIdFromCurrentRoute();
      companyId && this.props.getCompany(companyId);
    }
  }

  setBreadcrumbs() {
    if(this.props.farmId && this.props.farmName){
      const companyId = getCompanyIdFromCurrentRoute();
      const farmName = this.props.farmName;
      const farmId = this.props.farmId;
      let breadcrumbs = [];
      if(companyId && this.props.selectedCompany) {
        const parentRoute = '/companies/' + this.props.selectedCompany.id;
        breadcrumbs = [
          {text: 'Companies', route: '/companies'},
          {text: this.props.selectedCompany.name, route:  parentRoute + '/details?details'},
          {text: 'Farms', route: parentRoute + '/farms'},
          {text: 'NGRs'}
        ];
        const stocksOption = { text: farmName, route: '/stocks/storages-view?farmId=' + farmId };
        const detailsOption = { text: farmName, route: parentRoute + '/farms/' + farmId + '/settings/details?details' };
        if(this.props.canActOnFarm && this.props.selectedFarm.isAssociated) {
          breadcrumbs.splice(breadcrumbs.length-1, 0, stocksOption);
        } else {
          breadcrumbs.splice(breadcrumbs.length-1, 0, detailsOption);
        }
      } else {
        if(this.props.canActOnFarm && (this.props.selectedFarm.isAssociated || isSystemCompany())) {
          breadcrumbs = [
            {text: 'Farms', route: '/farms'},
            {text: farmName, route: '/stocks/storages-view?farmId=' + farmId},
            {text: 'Settings', route: '/farms/' + farmId + '/settings/details?details'},
            {text: 'NGRs'}
            ];
        } else {
          breadcrumbs = [
            {text: 'Farms', route: '/farms'},
          ];

          breadcrumbs = breadcrumbs.concat([
            {text: 'Settings', route: '/farms/' + farmId + '/settings/details?details'},
            {text: 'NGRs'}
          ]);
        }
      }
      if(!isEqual(this.props.breadcrumbs, breadcrumbs)) {
        this.props.setBreadcrumbs(breadcrumbs);
      }
    }
  }

  handleAddNgrButtonClick() {
    this.props.handleAddNgrButtonClick();
    this.setState({
      openCreateSideDrawer: true,
    });
  }

  onCloseSideDraw() {
    this.setState({
      openCreateSideDrawer: false,
    });
  }

  render() {
    return (
      <Paper className="paper-table">
        <div>
          {
            this.props.canActOnFarm ?
            <AddButton label="NGR" onClick={this.handleAddNgrButtonClick} app="farmNGR" />
            : null
          }
          <NgrsTable/>
        </div>
        {this.props.isCreateFormDisplayed &&
         <SideDrawer
           isOpen={this.state.openCreateSideDrawer}
           title="Add NGR"
           size="big"
           onClose={this.onCloseSideDraw}
           >
           <CreateNgr farmId={this.farmId} closeDrawer={this.onCloseSideDraw} />
         </SideDrawer>
        }
        {
          this.props.canActOnFarm ?
          <SideDrawer
            isOpen={this.props.isUpdateFormDisplayed}
            title="Edit NGR"
            size="big"
            onClose={this.props.cancelEditNgr}
            >
            <UpdateNgr farmId={this.farmId} />
          </SideDrawer>
          : null
        }
      </Paper>
    );
  }
}

const mapStateToProps = state => {
  return {
    isCreateFormDisplayed: state.companies.ngrs.isCreateFormDisplayed,
    isUpdateFormDisplayed: state.companies.ngrs.isUpdateFormDisplayed,
    breadcrumbs: state.main.breadcrumbs,
    selectedCompany: state.companies.companies.selectedCompany,
    selectedFarm: state.companies.farms.selectedFarm,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    getNgrs: companyId => dispatch(getCompanyNgrs(companyId, receiveNgrs)),
    handleAddNgrButtonClick: () => dispatch(clickAddNgrButton()),
    setBreadcrumbs: (breadcrumbs) => dispatch(setBreadcrumbs(breadcrumbs)),
    getCompany: (companyId) => dispatch(getCompanyDetails(companyId)),
    cancelEditNgr: () => dispatch(cancelEditNgr())
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(Ngrs);
