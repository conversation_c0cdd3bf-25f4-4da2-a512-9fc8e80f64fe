import React from 'react';

import { connect } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import isEqual from 'lodash/isEqual';
import pickBy from 'lodash/pickBy';
import get from 'lodash/get';
import has from 'lodash/has';
import OutloadsHomeStorageTable from '../../containers/OutloadsHomeStorageTable';
import CreateStorageOutload from '../../containers/CreateStorageOutload';
import UpdateOutload from '../../containers/UpdateOutload';
import { getStorageOutloads } from '../../actions/api/outloads';
import { getCommodities } from '../../actions/api/commodities';
import { receiveOutloads } from '../../actions/companies/outloads';
import AddButton from '../common/AddButton';
import SideDrawer from '../common/SideDrawer';
import {setBreadcrumbs} from '../../actions/main';
import queryString from 'query-string';
import { withRouter } from 'react-router-dom';

class Outloads extends React.Component {
  constructor(props) {
    super(props);
    const queryParams = queryString.parse(props.location.search);
    this.state = {
      isAddOutloadSideDrawerOpened: false,
      isEditOutloadSideDrawerOpened: false,
      outload: null,
      movement: null,
      commodityId: has(queryParams, 'commodityId') ? parseInt(get(queryParams, 'commodityId')) : null,
      ngrId: has(queryParams, 'ngrId') ? parseInt(get(queryParams, 'ngrId')) : null,
      gradeId: has(queryParams, 'gradeId') ? parseInt(get(queryParams, 'gradeId')) : null,
      season: get(queryParams, 'season', null),
    };

    this.openAddOutloadSideDrawer = this.openAddOutloadSideDrawer.bind(this);
    this.closeAddOutloadSideDrawer = this.closeAddOutloadSideDrawer.bind(this);
    this.closeEditOutloadSideDrawer = this.closeEditOutloadSideDrawer.bind(this);
  }

  componentDidMount() {

    this.props.getOutloads(this.props.storageId, this.getQueryParams());

    if (isEmpty(this.props.commodities)) {
      this.props.getCommodities();
    }
  }

  getQueryParams() {
    let params = {
      commodity_id: this.state.commodityId,
      grade_id: this.state.gradeId,
      season: this.state.season,
    };
    if(this.state.ngrId){
      params.ngr_id = this.state.ngrId;
    }
    return pickBy(params, value => value);
  }

  getBreadcrumbs() {
    const { headerUrl, headerText, breadcrumbs } = this.props;
    let newBreadcrumbs = [];
    if(!isEmpty(breadcrumbs))
      newBreadcrumbs = [breadcrumbs[0]];
    newBreadcrumbs = [
      ...newBreadcrumbs,
      {text: headerText, route: headerUrl},
      {text: 'Outloads'}
    ];

    return newBreadcrumbs;
  }

  componentDidUpdate(prevProps){
    const queryParams = queryString.parse(this.props.location.search);
    const commodityId = has(queryParams, 'commodityId') ? parseInt(get(queryParams, 'commodityId')) : null;
    const ngrId = has(queryParams, 'ngrId') ? parseInt(get(queryParams, 'ngrId')) : null;
    const gradeId = has(queryParams, 'gradeId') ? parseInt(get(queryParams, 'gradeId')) : null;
    const season = get(queryParams, 'season');
    const newState = { ...this.state };
    if(commodityId !== newState.commodityId){
      newState.commodityId = commodityId;
    }
    if(ngrId !== newState.ngrId){
      newState.ngrId = ngrId;
    }
    if(gradeId !== newState.gradeId){
      newState.gradeId = gradeId;
    }
    if(season !== newState.season){
      newState.season = season;
    }
    if(!isEqual(this.state,newState)){
      this.setState(newState,() => {
        this.props.getOutloads(this.props.storageId, this.getQueryParams());
      });
    }
    if (prevProps.storageType != this.props.storageType) {
      this.props.getOutloads(this.props.storageId, this.getQueryParams());
    }
    const newBreadcrumbs = this.getBreadcrumbs();
    if(!isEqual(this.props.breadcrumbs, newBreadcrumbs))
      this.props.setBreadcrumbs(newBreadcrumbs);
  }

  openAddOutloadSideDrawer() {
    this.setState({ isAddOutloadSideDrawerOpened: true, isEditOutloadSideDrawerOpened: false });
  }

  closeAddOutloadSideDrawer() {
    this.setState({ isAddOutloadSideDrawerOpened: false });
  }

  closeEditOutloadSideDrawer() {
    this.setState({ isEditOutloadSideDrawerOpened: false, outload: null });
  }

  async handleDefaultCellClick(outload, context) {
    if(outload.freightMovementId)
      window.open(`#/freights/movements/${outload.freightMovementId}/details`);
    else
      context.setState({
        isEditOutloadSideDrawerOpened: true,
        isAddOutloadSideDrawerOpened: false,
        outload
      });
  }

  componentWillUnmount() {
    this.props.resetOutloads();
  }

  render() {
    return (
      <div>
        {
          !this.props.dontShowAddButton &&
          <AddButton
            label="Outload"
            onClick={this.openAddOutloadSideDrawer}
            disabled={!get(this.props, 'storageTonnage.totalTonnage')}
            app="load"
          />
        }
        <OutloadsHomeStorageTable
          ignoreOrder={true}
          handleDefaultCellClick={this.handleDefaultCellClick}
          cellClickContext={this}
        />
        <SideDrawer
          isOpen={this.state.isAddOutloadSideDrawerOpened}
          title="Add Outload"
          size="big"
          onClose={this.closeAddOutloadSideDrawer}
          app="load"
          isCreate={true}
        >
          <CreateStorageOutload
            companyId={this.props.companyId}
            farmId={this.props.farmId}
            storageId={this.props.storageId}
            closeDrawer={this.closeAddOutloadSideDrawer}
            showNgrField={this.props.showNgrField}
            isCreate={true}
          />
        </SideDrawer>
        <SideDrawer
          isOpen={this.state.isEditOutloadSideDrawerOpened}
          title="Edit Outload"
          size="big"
          onClose={this.closeEditOutloadSideDrawer}
          app="load"
          isCreate={false}
        >
          <UpdateOutload
            companyId={this.props.companyId}
            farmId={this.props.farmId}
            storageId={this.props.storageId}
            outload={this.state.outload}
            movement={this.state.movement}
            closeDrawer={this.closeEditOutloadSideDrawer}
            showNgrField={this.props.showNgrField}
            isCreate={false}
          />
        </SideDrawer>
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    token: state.main.user.token,
    commodities: state.master.commodities.items,
    storageTonnage: state.companies.outloads.storageTonnage,
    breadcrumbs: state.main.breadcrumbs,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    resetOutloads: () => dispatch(receiveOutloads([])),
    getOutloads: (storageId, queryParams) => dispatch(
      getStorageOutloads(storageId, receiveOutloads, queryParams)
    ),
    getCommodities: () => dispatch(getCommodities()),
    setBreadcrumbs: (breadcrumbs) => dispatch(setBreadcrumbs(breadcrumbs)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(Outloads));
