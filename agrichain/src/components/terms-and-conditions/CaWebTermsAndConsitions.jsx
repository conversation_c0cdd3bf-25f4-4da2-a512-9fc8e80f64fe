import React, {Component} from 'react';
import './termsAndConditions.scss'

class CanadaWebTermsAndConditions extends Component {

    render() {
        return (
            <div className="full-screen-container term-and-conditions">
            <div className="content-wrapper">
                <div className="content-box--header">
                <img src="images/agrichain-logo.png" alt="Agri Chain Logo" />
                </div>
                <div className="content-body">
                <div id="page">
                    <p className="p0 ft0">COMMUNICATIONS & CONSENT TO ELECTRONIC MESSAGES</p>

                    <p className="p9 ft5">
                    Our communication practices are designed to comply with Canada&apos;s Anti-Spam Legislation (CASL). When you
                    provide us with your electronic contact information (such as your email address) through our service,
                    you may be asked to provide your express consent to receive Commercial Electronic Messages (CEMs)
                    from AgriChain.
                    </p>
                    <p className="p9 ft5">
                    CEMs are electronic messages (like emails) that encourage participation in a commercial activity. These
                    may include newsletters, updates on our products and services, promotional offers, and other marketing
                    communications.
                    </p>

                    <p className="p5 ft0">1. How We Obtain Consent:</p>

                    <p className="p3 ft5">
                    <strong>Express Consent:</strong> We primarily seek your express consent before sending you CEMs. This
                    is typically done when you proactively take an action, such as checking a designated box (which will
                    never be pre-checked) on our website or forms. When we ask for express consent, we will clearly
                    state the purpose(s), identify ourselves as the sender, and inform you of your right to withdraw consent.
                    </p>

                    <p className="p3 ft5">
                    <strong>Implied Consent:</strong> In limited circumstances permitted by CASL, we might infer your
                    consent. This can happen if you have an existing business relationship with us (as defined by CASL),
                    or if you have conspicuously published your contact information or provided it to us without indicating
                    you do not wish to receive CEMs.
                    </p>

                    <p className="p5 ft0">2. Our Commitment:</p>

                    <ul className="p-list ft5">
                    <li>We will clearly identify AgriChain as the sender of any CEM.</li>
                    <li>We will provide our valid contact information in every CEM, including a mailing address and either a phone number, email address, or web address.</li>
                    <li>We will ensure that the information within our CEMs is accurate and not presented in a false or misleading manner.</li>
                    </ul>

                    <p className="p5 ft0">3. Withdrawing Your Consent (Unsubscribing):</p>

                    <p className="p6 ft5">
                    You can withdraw your consent and unsubscribe from receiving CEMs at any time. Every CEM we send will
                    contain a clear and functional unsubscribe mechanism (usually a link). Once you use this mechanism,
                    we will process your request promptly, and in any case, no later than ten (10) business days from the
                    request. You can also manage your communication preferences or withdraw consent by contacting us
                    directly using the information below.
                    </p>

                    <p className="p5 ft0">4. Contact Us:</p>

                    <p className="p6 ft5">
                    For any questions regarding these communication practices or CASL, please contact us at:<a href="mailto:<EMAIL>"> <EMAIL></a>
                    </p>
                </div>
                </div>
            </div>
            </div>
        );
    }

}

export default CanadaWebTermsAndConditions;