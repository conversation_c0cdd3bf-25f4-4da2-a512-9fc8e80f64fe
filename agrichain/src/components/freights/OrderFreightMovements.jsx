import React from 'react';
import { connect } from 'react-redux';
import FreightMovements from "./FreightMovements";
import MovementDetailsContainer from './MovementDetails/MovementDetailsContainer';
import {
  isLoading,
  setBreadcrumbs,
} from '../../actions/main';
import {
  getSelectedFreight,
  receiveFreight,
} from '../../actions/companies/freights';
import get from 'lodash/get';
import {getAbsoluteUrl, getQueryParams} from "../../common/utils";

class OrderFreightMovements extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      setBreadcrumbs: true
    };

    this.movementId = get(this.props, 'movementId');
    this.handleBackClick = this.handleBackClick.bind(this);
    this.setBreadcrumbsForMovementDetails = this.setBreadcrumbsForMovementDetails.bind(this);
  }

  handleBackClick() {
    this.setState({setBreadcrumbs: true}, () => {
      this.props.dispatch(receiveFreight(null));
     document.location.hash = getAbsoluteUrl(document.location.hash);
    });
  }

  componentDidUpdate() {
    this.movementId = getQueryParams(document.location.hash, 'movementId');;
    if(!this.movementId)
      this.props.dispatch(receiveFreight(null));
  }

  componentDidMount() {
    const { movementId } = this.props;
    if (movementId) {
      this.props.dispatch(isLoading('movementDetail'));
      this.props.dispatch(
        getSelectedFreight(movementId, receiveFreight, false, false, false, true, false)
      );
    } else {
      this.props.dispatch(receiveFreight(null));
    }
    this.props.dispatch(setBreadcrumbs([]));
  }

  componentWillUnmount() {
    this.props.dispatch(receiveFreight(null));
  }

  setBreadcrumbsForMovementDetails() {
    if(this.props.selectedFreight && this.props.breadcrumbs && this.state.setBreadcrumbs) {
      let breadcrumbs = this.props.breadcrumbs.slice(0, 2).concat([
        {text: 'Movements', route: '/freights/orders/' + this.props.orderId + '/movements', onClick: this.handleBackClick },
        {text: get(this.props.selectedFreight, 'identifier', '')}
      ]);
      this.setState({setBreadcrumbs: false}, () => {
        this.props.dispatch(setBreadcrumbs(breadcrumbs));
      });
    }
  }
  render() {
    return (
      <div>
        {
          this.props.selectedFreight || this.movementId ?
          <div>
            <MovementDetailsContainer
              {...this.props}
              movement={this.props.selectedFreight}
              dontSetBreadcrumbs={true}
              breadcrumbsFunc={this.setBreadcrumbsForMovementDetails}
              backButtonHandler={this.handleBackClick}
              movementOrder={this.props.order}
            />
          </div>
          : <FreightMovements
              dontRedirect={true}
              dontSetBreadcrumbs={true}
              {...this.props}
              contractId={this.props.contractId}
              contract={this.props.contract}
              movementParentOrder={this.props.order}
              handleAddMovemenetButtonClick={this.props.onHandleAddMovementButtonClick}
            />
        }
      </div>
    );

  }

}
const mapStateToProps = state => {
  return {
    selectedFreight: state.companies.freights.selectedFreight,
    breadcrumbs: state.main.breadcrumbs,
  };
};
export default connect(mapStateToProps)(OrderFreightMovements);
