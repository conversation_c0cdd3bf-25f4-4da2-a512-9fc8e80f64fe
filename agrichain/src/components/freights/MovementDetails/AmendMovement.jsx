import React, { Component } from 'react';

import { connect } from 'react-redux';
import Grid from '@mui/material/Grid';
import { positiveDecimalFilter } from '../../../common/input-filters';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import withStyles from '@mui/styles/withStyles';
import CommonTextField from '../../common/CommonTextField';
import CommonButton from '../../common/CommonButton';
import {required} from '../../../common/validators';
import set from 'lodash/set';
import has from 'lodash/has';
import forEach from 'lodash/forEach';
import {isLoading} from "../../../actions/main";
import {convertEpochToDateFormat, convertTimeToTwelveHour } from "../../../common/momentUtilities";
import { toDateFormat, getCountryCurrency } from '../../../common/utils';
import CommonDatePicker from "../../common/CommonDatePicker";
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import some from 'lodash/some';
import alertifyjs from 'alertifyjs';
import {
  confirmMovementAmend,
  raiseMovementAmendRequest,
  rejectMovementAmend
} from "../../../actions/companies/freights";
import CommonTimePicker from "../../common/CommonTimePicker";
import {RejectionReasonDialog} from "../../rejections/RejectionReasonDialog";
import NumberField from '../../common/NumberField';


const styles = () => ({
  subTitle: {
    fontSize: 13,
    fontWeight: 500,
    color: '#112c42',
  },
  listItem: {
    display: 'block',
    height: 75,
    padding: 0,
  },
  secondaryListItem: {
    padding: 0,
  },
  primaryListItem: {
    padding: 0,
  },
});

const EXCESS_CONTRACT_TONNAGE_ERROR_MESSAGE = 'Cannot be greater than Contract tonnage';
const EXCESS_ORDER_TONNAGE_ERROR_MESSAGE = 'Cannot be greater than Order tonnage';

class AmendMovement extends Component {
  constructor(props){
    super(props);



    this.classes = this.props.classes;

    this.tonnage = this.props.movement.plannedTonnage;
    this.deliveryStartDate = convertEpochToDateFormat(this.props.movement.freightPickup.date);
    this.deliveryEndDate = convertEpochToDateFormat(this.props.movement.freightDelivery.date);
    this.deliveryStartTime = convertTimeToTwelveHour(this.props.movement.freightPickup.timeStart);
    this.deliveryEndTime = convertTimeToTwelveHour(this.props.movement.freightDelivery.timeStart);
    this.freightRateIn = this.props.movement.rateFreightIn;
    this.freightRateOut = this.props.movement.rateFreightOut;

    this.movementId = this.props.movement.id;
    this.isAmendRequestPending = this.props.movement.isAmendRequestPending;
    this.amendable = this.props.movement.amendable;
    this.amendedDetails = this.props.movement.amendedDetails;

    this.state = {
      amendedTonnage: {
        value: get(this.amendedDetails, 'plannedTonnage', null),
        validators: [required()],
        errors: []
      },
      amendedDeliveryStartDate: {
        value: get(this.amendedDetails, 'freightPickup.date', null),
        validators: [required()],
        errors: []
      },
      amendedDeliveryEndDate: {
        value: get(this.amendedDetails, 'freightDelivery.date', null),
        validators: [required()],
        errors: []
      },
      amendedDeliveryStartTime: {
        value: get(this.amendedDetails, 'freightPickup.timeStart', null),
        validators: [required()],
        errors: []
      },
      amendedDeliveryEndTime: {
        value: get(this.amendedDetails, 'freightDelivery.timeStart', null),
        validators: [required()],
        errors: []
      },
      amendedFreightRateIn: {
        value: get(this.amendedDetails, 'rateFreightIn', null),
        validators: [required()],
        errors: []
      },
      amendedFreightRateOut: {
        value: get(this.amendedDetails, 'rateFreightOut', null),
        validators: [required()],
        errors: []
      },

      rejectDialogOpen: {
        value: false,
        validators: [],
        errors: []
      },
      rejectionReason: {
        value: "",
        validators: [],
        errors: []
      },
    };

    this.handleTextFieldChange = this.handleTextFieldChange.bind(this);
    this.addMovementAcceptanceRequestForAmend = this.addMovementAcceptanceRequestForAmend.bind(this);
    this.confirmAmendRequestWrapper = this.confirmAmendRequestWrapper.bind(this);
    this.rejectAmendRequestWrapper = this.rejectAmendRequestWrapper.bind(this);
    this.setFieldErrors = this.setFieldErrors.bind(this);
    this.getFieldErrors = this.getFieldErrors.bind(this);
    this.isFormValid = this.isFormValid.bind(this);
    this.handleBlur = this.handleBlur.bind(this);
    this.closeSidebar = this.props.closeSidebar;
    this.raiseAmendRequest = this.props.raiseAmendRequest;
    this.confirmAmendRequest = this.props.confirmAmendRequest;
    this.rejectAmendRequest = this.props.rejectAmendRequest;
    this.isLoading = this.props.isLoading;
  }

  handleRejectClickOpen = () => {
    const newState = { ...this.state };
    newState.rejectDialogOpen.value = true;
    newState.rejectionReason.validators = [required()];
    if(!newState.rejectionReason.value){
      newState.rejectionReason.errors = [];
    }
    this.setState(newState);
  };

  handleRejectClose = () => {
    this.setState(
      {rejectDialogOpen: { errors: [], value: false, validators: []}}
    );
  };

  handleRejectSubmit = (event) => {
    this.setReasonErrors();
    const data = { rejectionReason: this.state.rejectionReason.value };
    if (this.state.rejectionReason.errors.length === 0) {
      this.rejectAmendRequestWrapper(data);
    }
    event.preventDefault();
  };

  handleReasonChange = (event) => {
    const value = event.target.value;
    const newState = {...this.state};
    newState.rejectionReason.value = value;
    this.setState(newState, () => this.setReasonErrors());
  };

  setReasonErrors(errors) {
    const newState = {...this.state};
    newState.rejectionReason.errors = errors || this.getReasonErrors();
    this.setState(newState);
  }

  getReasonErrors() {
    const errors = [];
    const value = get(this.state, `rejectionReason.value`);
    const validators = get(this.state, `rejectionReason.validators`, []);
    validators.forEach((validator) => {
      if (validator.isInvalid(value)) {
        errors.push(validator.message);
      }
    });
    return errors;
  }

  getStartDateWarning = () => {
    const baseEntity = this.props.movement.order || this.props.movement.commodityContract;
    if(get(baseEntity, 'freightPickup.date') || get(baseEntity, 'deliveryStartDate')) {
      const startDate = get(baseEntity, 'freightPickup.date') || get(baseEntity, 'deliveryStartDate');
      if (this.state.amendedDeliveryStartDate.value < startDate){
        return `Warning: Delivery start date in ${baseEntity.entity === 'freightorder' ? 'order' : 'contract'} is ${toDateFormat(startDate)}`;
      }
    }
  };

  getEndDateWarning = () => {
    const baseEntity = this.props.movement.order || this.props.movement.commodityContract;
    if(get(baseEntity, 'freightDelivery.date') || get(baseEntity, 'deliveryEndDate')) {
      const endDate = get(baseEntity, 'freightDelivery.date') || get(baseEntity, 'deliveryStartDate');
      if (this.state.amendedDeliveryEndDate.value > endDate){
        return `Warning: Delivery end date in ${baseEntity.entity === 'freightorder' ? 'order' : 'contract'} is ${toDateFormat(endDate)}`;
      }
    }
  };

  handleTextFieldChange(event){
    const newState = {...this.state};
    set(newState, event.target.id + '.value', event.target.value);
    this.setFieldErrors();
    this.setTonnageErrors();
    this.setState(newState);
  }

  setTonnageErrors = () => {
    const newState = {...this.state};
    const baseEntity = get(this.props.movement, 'order') || get(this.props.movement, 'commodityContract');
    const baseEntityTonnage = get(this.props.movement, 'parentUnaccountedTonnage', 0);
    if (baseEntityTonnage && parseFloat(baseEntityTonnage) < parseFloat(newState.amendedTonnage.value)){
      newState.amendedTonnage.errors.push(
        baseEntity.entity === 'freightorder' ? EXCESS_ORDER_TONNAGE_ERROR_MESSAGE : EXCESS_CONTRACT_TONNAGE_ERROR_MESSAGE
      );
    }
  };

  handleSelectFieldChange = (value, id) => {
    this.setFieldValue(id, value);
    this.setFieldErrors();
    this.setTonnageErrors();
  };

  setFieldValue = (id, value) => {
    const newState = {...this.state};
    set(newState, id + '.value', value);
    this.setState(newState);
  };

  handleFreightRateChange = (event) => {
    this.setFieldValue(event.target.id, event.target.value);
    this.setFieldErrors();
    setTimeout(() => {
      this.setTonnageErrors();
    }, 100);
  };

  validateData = () => {
    this.setFieldErrors();
    if (this.isFormValid()) {
      if(some([
        (parseFloat(this.state.amendedTonnage.value) || this.tonnage) !==  this.tonnage,
        (parseFloat(this.state.amendedFreightRateIn.value) || this.freightRateIn) !== this.freightRateIn,
        (parseFloat(this.state.amendedFreightRateOut.value) || this.freightRateOut) !== this.freightRateOut,
        (this.state.amendedDeliveryStartDate.value || this.props.movement.freightPickup.date) !== this.props.movement.freightPickup.date,
        (this.state.amendedDeliveryEndDate.value || this.props.movement.freightDelivery.date) !== this.props.movement.freightDelivery.date,
        (this.state.amendedDeliveryStartTime.value || this.props.movement.freightPickup.timeStart) !== this.props.movement.freightPickup.timeStart,
        (this.state.amendedDeliveryEndTime.value || this.props.movement.freightDelivery.timeStart) !== this.props.movement.freightDelivery.timeStart,
      ], Boolean)) {
        this.addMovementAcceptanceRequestForAmend();
      } else {
        alertifyjs.alert("Amend Freight Movement", "You haven't amended any details");
      }
    }
  };

  addMovementAcceptanceRequestForAmend(){
    const payload = {
      plannedTonnage: parseFloat(this.state.amendedTonnage.value || this.tonnage),
      rateFreightIn: parseFloat(this.state.amendedFreightRateIn.value || this.freightRateIn),
      rateFreightOut: parseFloat(this.state.amendedFreightRateOut.value || this.freightRateOut),
      freightPickup: {
        date: this.state.amendedDeliveryStartDate.value || this.props.movement.freightPickup.date,
        timeStart: this.state.amendedDeliveryStartTime.value || this.props.movement.freightPickup.timeStart,
      },
      freightDelivery: {
        date: this.state.amendedDeliveryEndDate.value || this.props.movement.freightDelivery.date,
        timeStart: this.state.amendedDeliveryEndTime.value || this.props.movement.freightDelivery.timeStart,
      }
    };
    this.isLoading('movementDetail');
    this.closeSidebar();
    this.raiseAmendRequest(this.movementId, payload);
  }

  confirmAmendRequestWrapper(){
    this.isLoading('movementDetail');
    this.closeSidebar();
    this.confirmAmendRequest(this.movementId);
  }

  rejectAmendRequestWrapper(data){
    this.isLoading('movementDetail');
    this.closeSidebar();
    this.rejectAmendRequest(this.movementId, data);
  }

  handleBlur(event){
    const newState = {...this.state};
    this.getFieldErrors(newState[event.target.id], newState);
    this.setTonnageErrors();
    this.setState(newState);
  }

  setFieldErrors(){
    const newState = {...this.state};
    let validFields = 0;
    forEach(newState, (field) => {
      const isValid = this.getFieldErrors(field, newState);
      if (isValid){
        validFields += 1;
      }
    });
    if (validFields){
      forEach(newState, (stateField) => {
        stateField.errors = [];
      });
      this.setTonnageErrors();
    }
    this.setState(newState);
  }

  getFieldErrors(field){
    field.errors = [];
    if (has(field, 'value') && has(field, 'validators')){
      field.validators.forEach((validator) => {
        if (validator.isInvalid(field.value)){
          field.errors.push(validator.message);
        }
      });
    }
    return isEmpty(field.errors);
  }

  minDeliveryDate = () => {
    return new Date(this.state.amendedDeliveryStartDate.value || this.props.movement.freightPickup.date);
  };

  maxPickupDate = () => {
    return new Date(this.state.amendedDeliveryEndDate.value || this.props.movement.freightDelivery.date);
  };

  isFormValid(){
    var formValid = true;
    const currentState = {...this.state};
    forEach(currentState, (field) => {
      if (field.errors.length !== 0){
        formValid = false;
      }
    });
    return formValid;
  }

  render(){
    const currency = this.props.movement?.currency || getCountryCurrency()
    return (
      <Grid container spacing={8} style={{display: 'inline'}}>
        <Grid item xs={6}>
          <Typography variant="subheading" className={this.classes.subTitle}>
            Current
          </Typography>
          <List>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Tonnage" className={this.classes.secondaryListItem} />
              <ListItemText primary={`${this.tonnage} MT`} className={this.classes.primaryListItem} />
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery Start Date" className={this.classes.secondaryListItem} />
              <ListItemText primary={`${this.deliveryStartDate}`} className={this.classes.primaryListItem} />
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery Start Time" className={this.classes.secondaryListItem} />
              <ListItemText primary={`${this.deliveryStartTime}`} className={this.classes.primaryListItem} />
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery End Date" className={this.classes.secondaryListItem} />
              <ListItemText primary={`${this.deliveryEndDate}`} className={this.classes.primaryListItem} />
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery End Time" className={this.classes.secondaryListItem} />
              <ListItemText primary={`${this.deliveryEndTime}`} className={this.classes.primaryListItem} />
            </ListItem>
            {((!this.props.movement.isFreightProvider && !this.props.movement.isCustomer && !this.props.movement.isCustomerRegistered) ||
              this.props.movement.isCustomer) && this.props.movement.invoicing === 'Freight Provider to Invoice Broker' && !this.props.movement.orderId &&
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Freight Rate In" className={this.classes.secondaryListItem} />
              <ListItemText primary={this.freightRateIn ? `${currency} ${this.freightRateIn}` : '-'} className={this.classes.primaryListItem} />
            </ListItem>}
            {(this.props.movement.isFreightProvider ||
              (!this.props.movement.isCustomer && !this.props.movement.isCustomerRegistered) ||
              this.props.movement.isCustomer) && !this.props.movement.orderId &&
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary={!this.props.movement.isFreightProvider || this.props.movement.isSelf ? "Freight Rate Out" : "Freight Rate"} className={this.classes.secondaryListItem} />
              <ListItemText primary={`${currency} ${this.freightRateOut}`} className={this.classes.primaryListItem} />
            </ListItem>}
          </List>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="subheading" className={this.classes.subTitle}>
            Amended
          </Typography>
          <List>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Tonnage" className={this.classes.secondaryListItem} />
              <NumberField
                id="amendedTonnage"
                label=""
                value={this.state.amendedTonnage.value}
                disabled={this.isAmendRequestPending}
                helperText={this.state.amendedTonnage.errors[0]}
                onChange={this.handleTextFieldChange}
                onBlur={this.handleBlur}
                maxValue={9999.99}
                lockIconClassName="side-drawer-lock-icon"
                InputProps={{
                  endAdornment: <InputAdornment position="end" style={{color: 'rgb(162,162,162)'}}>MT</InputAdornment>
                }}/>
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery Start Date" className={this.classes.secondaryListItem} />
              <CommonDatePicker
                id="amendedDeliveryStartDate"
                disabled={this.isAmendRequestPending}
                onChange={this.handleSelectFieldChange}
                errorText={this.state.amendedDeliveryStartDate.errors[0] || this.getStartDateWarning()}
                value={this.state.amendedDeliveryStartDate.value}
                style={{float: 'left'}}
                maxDate={this.maxPickupDate()}
                isInSideDrawer
                shouldRemoveLabel
              />
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery Start Time" className={this.classes.secondaryListItem} />
              <div className="relative-pos">
                <CommonTimePicker
                  id="amendedDeliveryStartTime"
                  disabled={this.isAmendRequestPending}
                  onChange={this.handleSelectFieldChange}
                  errorText={this.state.amendedDeliveryStartTime.errors[0]}
                  value={this.state.amendedDeliveryStartTime.value}
                  style={{float: 'left'}}
                  shouldRemoveLabel
                />
                {this.isAmendRequestPending ? <i className='side-drawer-icon-lock'></i>: ''}
              </div>
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery End Date" className={this.classes.secondaryListItem} />
              <CommonDatePicker
                id="amendedDeliveryEndDate"
                disabled={this.isAmendRequestPending}
                onChange={this.handleSelectFieldChange}
                errorText={this.state.amendedDeliveryEndDate.errors[0] || this.getEndDateWarning()}
                value={this.state.amendedDeliveryEndDate.value}
                style={{float: 'left'}}
                minDate={this.minDeliveryDate()}
                isInSideDrawer
                shouldRemoveLabel
              />
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery End Time" className={this.classes.secondaryListItem} />
              <div className="relative-pos">
                <CommonTimePicker
                  id="amendedDeliveryEndTime"
                  disabled={this.isAmendRequestPending}
                  onChange={this.handleSelectFieldChange}
                  errorText={this.state.amendedDeliveryEndTime.errors[0]}
                  value={this.state.amendedDeliveryEndTime.value}
                  style={{float: 'left'}}
                  shouldRemoveLabel
                />
                {this.isAmendRequestPending ? <i className='side-drawer-icon-lock'></i>: ''}
              </div>
            </ListItem>
            {((!this.props.movement.isFreightProvider && !this.props.movement.isCustomer && !this.props.movement.isCustomerRegistered) || this.props.movement.isCustomer) &&
            this.props.movement.invoicing === 'Freight Provider to Invoice Broker' && !this.props.movement.orderId &&
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Freight Rate In" className={this.classes.secondaryListItem} />
              <CommonTextField
                id="amendedFreightRateIn"
                label=""
                helperText={this.state.amendedFreightRateIn.errors[0]}
                disabled={this.isAmendRequestPending}
                value={this.state.amendedFreightRateIn.value}
                onChange={this.handleFreightRateChange}
                onBlur={this.handleBlur}
                onKeyDown={(event)=>positiveDecimalFilter(event, 2,9999.99)}
                lockIconClassName="side-drawer-lock-icon"
                InputProps={{
                  startAdornment: <InputAdornment position="start" style={{color: 'rgb(162,162,162)'}}>$</InputAdornment>
                }}/>
            </ListItem>}
            {(this.props.movement.isCustomer || (!this.props.movement.isCustomer && !this.props.movement.isCustomerRegistered) || this.props.movement.isFreightProvider) && !this.props.movement.orderId &&
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary={!this.props.movement.isFreightProvider || this.props.movement.isSelf ? "Freight Rate Out" : "Freight Rate"} className={this.classes.secondaryListItem} />
              <CommonTextField
                id="amendedFreightRateOut"
                label=""
                helperText={this.state.amendedFreightRateOut.errors[0]}
                disabled={this.isAmendRequestPending}
                value={this.state.amendedFreightRateOut.value}
                onChange={this.handleFreightRateChange}
                onBlur={this.handleBlur}
                onKeyDown={(event)=>positiveDecimalFilter(event, 2,9999.99)}
                lockIconClassName="side-drawer-lock-icon"
                InputProps={{
                  startAdornment: <InputAdornment position="start" style={{color: 'rgb(162,162,162)'}}>$</InputAdornment>
                }}/>
            </ListItem>}
          </List>
        </Grid>

        <div className='button-container'>
          { this.amendable && this.isAmendRequestPending ?
            [
              <CommonButton key="reject" label="Reject" secondary variant="contained" onClick={() => this.handleRejectClickOpen()}/>,
              <CommonButton key="accept" label="Accept" primary variant="contained" onClick={this.confirmAmendRequestWrapper}/>,
            ] :
            !this.isAmendRequestPending ?
              [
                <CommonButton key="close" label="Close" secondary variant="contained" onClick={this.closeSidebar}/>,
                <CommonButton key="amend" label="Amend" primary variant="contained" onClick={this.validateData}/>
              ] :
              [
                <CommonButton key="close" label="Close" secondary variant="contained" onClick={this.closeSidebar}/>,
              ]
          }
        </div>

        {this.amendable &&
          <div className="status-actions-wrap">
            <RejectionReasonDialog
              open={this.state.rejectDialogOpen.value}
              onClose={this.handleRejectClose}
              title="Reject Amend Request"
              value={this.state.rejectionReason.value}
              onChange={this.handleReasonChange}
              helperText={get(this.state, 'rejectionReason.errors[0]', '')}
              onCancel={this.handleRejectClose}
              onReject={this.handleRejectSubmit}
            />
          </div>
        }
      </Grid>
    );
  }
}

const mapStateToProps = state => state;

const mapDispatchToProps = dispatch => ({
  raiseAmendRequest: (movementId, data) => dispatch(raiseMovementAmendRequest(movementId, data)),
  confirmAmendRequest: (movementId) => dispatch(confirmMovementAmend(movementId)),
  rejectAmendRequest: (movementId, data) => dispatch(rejectMovementAmend(movementId, data)),
  isLoading: (component) => dispatch(isLoading(component))
});

export default withStyles(styles)(connect(mapStateToProps, mapDispatchToProps)(AmendMovement));
