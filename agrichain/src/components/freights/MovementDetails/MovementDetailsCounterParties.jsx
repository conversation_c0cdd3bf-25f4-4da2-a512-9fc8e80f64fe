import React from 'react';

import Paper from '@mui/material/Paper';
import KeyboardArrowDown from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUp from '@mui/icons-material/KeyboardArrowUp';
import ContactPhone from '@mui/icons-material/ContactPhone';
import { EMPTY_VALUE } from '../../../common/constants';
import Tooltip from '../../../common/Tooltip';

const renderBasedOnExpandedFlag = expanded => {
  if (expanded) {
    return (<KeyboardArrowUp
      style={{ fill: '#112c42', height: '20px', width: '20px' }}
    />);
  }
  return (<KeyboardArrowDown style={{ fill: '#112c42', height: '20px', width: '20px' }} />);
};

class MovementDetailsCounterParties extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      expanded: true
    };
  }

  toggleExpanded = () => this.setState((prevState) => ({ expanded: !prevState.expanded }));

  getCounterPartiesSectionAsPerGrid = (payload, sectionName) => {
    return (
      <div>
        <h4 className="section-title">{sectionName}</h4>
        <ul>
          {Object.keys(payload).map(function (key, index) {
            return (
              <li key={index}>
                <Tooltip
                  className="field-label ellipses"
                  tooltipText={key}
                  textContent={key}
                />
                {key === 'Contact' && <span className="fieldValue">
                  <ContactPhone style={{
                    fill: '#112c42', height: '20px', width: '20px', verticalAlign: 'bottom',
                    marginRight: '10px'
                  }} />
                  {payload[key] || EMPTY_VALUE}
                </span>}
                {key !== 'Contact' && <Tooltip
                  className="field-value ellipses"
                  tooltipText={payload[key] || EMPTY_VALUE}
                  textContent={payload[key] || EMPTY_VALUE}
                />}
              </li>
            );
          })}
        </ul>
      </div>
    );
  };

  render() {
    const { movement } = this.props;
    const { expanded } = this.state;

    const consignorPayload = {
      Name: movement.freightPickup.consignor.handler.name,
      Contact: movement.freightPickup.consignor.handler.mobile,
    };

    const consigneePayload = {
      Name: movement.freightDelivery.consignee.handler.name,
      Contact: movement.freightDelivery.consignee.handler.mobile,
    };

    const providerPayload = {
      Name: movement.provider.businessName,
      Contact: movement.freightDelivery.consignee.handler.mobile,
    };

    return (
      <Paper className="contract-details-section-container">
        <h2 onClick={this.toggleExpanded}>
          Contact Details
          <span className="expand-icon">
            {renderBasedOnExpandedFlag(expanded)}
          </span>
        </h2>
        {
          <div className="section-details-container">
            {this.getCounterPartiesSectionAsPerGrid(consignorPayload, 'Consignor')}
            {this.getCounterPartiesSectionAsPerGrid(consigneePayload, 'Consignee')}
            {this.getCounterPartiesSectionAsPerGrid(providerPayload, 'Freight Provider')}
          </div>
        }
      </Paper>
    );
  }
}

export default MovementDetailsCounterParties;
