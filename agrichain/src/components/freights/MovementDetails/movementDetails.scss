@import "../../../common/variables.scss";
* {
  box-sizing: border-box;
}
.field-label-list {
  li {
    display: flex;
  }
}
.contract-details-container {
  //Basic Info Section
  .contract-details-status-section {
    display: grid;
    grid-template-columns: auto auto auto auto auto auto;
    line-height: 1;
    .item {
      border-right: 1px solid #e0e0e0;
      padding: 10px 30px;
      span.field-value {
        display: block;
        color: #112c42;
        font-size: 20px;
        line-height: 1;
      }
      @media only screen and (max-width: 1000px) {
        padding: 10px;
      }
    }
    .item1 {
      text-align: center;
    }
    .item2 {
      display: grid;
      grid-template-columns: auto ;
      padding: 0;
      > div {
        padding: 10px 20px;
        border-right: 1px solid #e0e0e0;
        text-align: center;
        &:last-child {
          border-right: none;
        }
        @media only screen and (max-width: 1000px) {
          padding: 10px;
        }
      }
      .outstanding-wrapper {
        padding: 10px 0 0;
      }
      span {
        display: block;
        &.field-value {
          padding: 5px 0;
        }
        &.field-label {
          font-size: 16px;
          color: #999;
        }
        &.created-at {
          color: #999;
          font-size: 12px;
        }
      }
    }
    .item3 {
      padding: 5px 20px;
      border-right: 0;
      ul {
        display: grid;
        margin: 0;
        padding: 0;
        list-style-type: none;
        grid-gap: 10px;
        li {
          background: #8080805e;
          color: #112c42;
          font-size: 12px;
          text-align: center;
          border-radius: 4px;
          padding: 3px 0;
          &.paymentOverdue {
            color: #f44337;
          }
        }
      }
      @media only screen and (max-width: 1000px) {
        padding: 10px;
      }
    }
    .item4 {
      border-right: none;
      padding: 10px 20px;
    }

    & .status-actions {
      min-width: 22%;
      text-align: center;
    }

    & .status-actions-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      justify-content: center;
      align-items: center;

      h4 {
        margin: 10px 0 5px 0;
        font-size: 1.10rem;
      }
      a {
        margin-bottom: 10px;
      }

      a:first-child {
        margin-right: 10px;
      }

      & .status-actions-wrap {
        display: flex;
        flex-direction: column;
        padding-bottom: 15px;
      }

      &.grey-bg {
        background: $colorLightestGrey;
      }
    }

    & .reject-load-status{
      flex-direction: row;
      & .reject-load-actions {
        border-right: 1px solid #e0e0e0;
        padding-right: 15px;
        margin-right: 15px;
      }
    }
  }

  .contract-details-status-section.grid-7 {
      grid-template-columns: auto auto auto auto auto auto auto;
    }

  //CounterParties Section
  .contract-details-section-container {
    margin-bottom: 10px;
    padding: 20px 30px;
    color: #112c42;
    h2 {
      margin: 0;
      padding: 0;
      font-size: 20px;
      font-weight: 600;
      .expand-icon {
        width: auto;
        float: right;
        svg {
          vertical-align: middle;
        }
      }
    }

    .section-title {
      font-size: 13px;
      font-weight: 500;
      margin: 0;
      padding: 7px 0;
    }

    .section-details-container {
      display: grid;
      grid-template-columns: auto auto auto auto;
      margin: 10px 0;
      > div {
        border-right: 1px solid #e0e0e0;
        padding: 0 3px;
        &:last-child {
          border-right: 0;
        }
      }
      .section-details {
        margin: 25px 0;
      }
      ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
        li {
          padding: 7px 0;
          font-size: 13px;
          span {
            display: inline-block;
            &.field-label {
              width: auto;
              font-weight: 500;
              color: #808080;
              vertical-align: top;
            }
            &.field-value {
              width: auto;
              font-weight: normal;
              padding-left:5px;

            }
            img {
              &.thumb1 {
                  height: 75px;
                  width: 125px;
                  border: 1px solid #000;
            }
          }
          }
        }
      }
    }
  }
}

#movement-details-container .section-details-container{
    grid-template-columns: 25% 25% 25% 25%;
}

.action-custom {
  margin: 15% 0 !important;
}

#movement-details-container .section-details-container-5{
  grid-template-columns: 20% 20% 20% 20% 20%;
}

#movement-details-container > div > div {
    margin-bottom: 10px;
}
