import React from 'react'
import <PERSON>TextField from '../common/CommonTextField';
import alertifyjs from 'alertifyjs';
import { cloneDeep, find, get, includes, set, some } from 'lodash';
import CommonButton from '../common/CommonButton';
import NumberField from '../common/NumberField';
import { InputAdornment } from '@mui/material';
import { getCountryDisplayUnit, getCountryLabel } from '../../common/utils';
import CommonSelectInput from '../common/CommonSelectInput';
import { REQUIRED_FIELD } from '../../common/constants';
import SiteAsyncAutocomplete from '../common/autocomplete/SiteAsyncAutocomplete';
import APIService from '../../services/APIService';
import CommodityAutoComplete from '../common/autocomplete/CommodityAutoComplete';
import { addTruck, emptyCreatedTruck } from '../../actions/companies/trucks';
import connect from 'react-redux/es/connect/connect';
import { forceStopLoader, isLoading, validateRego } from '../../actions/main';
import { createTruck } from '../../actions/api/trucks';
import { getCommodities } from '../../actions/api/commodities';

class EmptyContainerForm extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      trucks: [],
      fields: {
        commodityId: cloneDeep(REQUIRED_FIELD),
        farmId: cloneDeep(REQUIRED_FIELD),
        truckId: cloneDeep(REQUIRED_FIELD),
        containerNumber: cloneDeep(REQUIRED_FIELD),
        isoCode: cloneDeep(REQUIRED_FIELD),
        containerTare: cloneDeep(REQUIRED_FIELD),
        truckGross: cloneDeep(REQUIRED_FIELD),
      }
    }
    this.handleSelectFieldChange = this.handleSelectFieldChange.bind(this);
    this.onChange = this.onChange.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.onHandleCreateEmptyContainerDrawer = this.onHandleCreateEmptyContainerDrawer.bind(this);
    this.getFieldErrors = this.getFieldErrors.bind(this);
    this.setFieldErrors = this.setFieldErrors.bind(this);
    this.setFieldValue = this.setFieldValue.bind(this);
    this.setAllFieldErrors = this.setAllFieldErrors.bind(this);
  }

  componentDidUpdate(prevProps) {
    if (
      this.props.createdTruck &&
      (!prevProps.createdTruck || prevProps.createdTruck.id !== this.props.createdTruck.id)
    ) {
      this.handleSelectFieldChange(this.props.createdTruck?.id, 'truckId', this.props.createdTruck);
    }
  }

  onHandleCreateEmptyContainerDrawer = bool => this.setState({ openEmptyContainerSideDrawer: bool });

  getFieldErrors(field, value) {
    field.validators.forEach((validator) => {
      if (validator.isInvalid(value || field.value) && !includes(field.errors, validator.message)) {
        field.errors.push(validator.message);
      } else {
        field.errors = [];
      }
    });
  }

  setFieldErrors(id, value, callback) {
    const newState = { ...this.state };
    this.getFieldErrors(get(newState.fields, id), value);
    this.setState(newState, () => {
      if (callback) {
        callback();
      }
    });
  }

  setFieldValue(id, value, validateAfterSet = false, callback) {
    const newState = { ...this.state };
    set(newState.fields, id + '.value', value);
    this.setState(newState, () => {
      if (validateAfterSet) this.setFieldErrors(id, value);
      if (callback) callback();
    });
  }

  onChange(event) {
    this.handleSelectFieldChange(event.target.value, event.target.id);
  }

  handleSelectFieldChange(value, id, item = false) {
    const newState = { ...this.state };
    if (item && id === 'truckId') {
      newState.trucks = [item]
      this.setState(newState, () => {
        this.props.emptyCreatedTruck();
        this.setFieldValue(id, value, true);
        this.setFieldErrors(id, value);
      })
    } else {
      this.setFieldValue(id, value, true);
      this.setFieldErrors(id, value);
    }
  }

  applyValidatorsOn(fields) {
    Object.values(fields).forEach((field) => {
      this.getFieldErrors(field, field.value);
    });
  }

  setAllFieldErrors() {
    const newState = { ...this.state };
    this.applyValidatorsOn(newState.fields);
    this.setState(newState)
  }

  async handleSubmit() {
    const fields = this.state.fields;
    const truckTare = this.state.fields.truckGross.value - this.state.fields.containerTare.value
    if (this.state.fields.containerTare.value && this.state.fields.containerTare.value && truckTare <= 0)
      return alertifyjs.error('Truck Gross must be greater than Container Tare.');
    const containerData = {
      commodityId: this.state.fields.commodityId.value,
      truckId: this.state.fields.truckId.value,
      containerNumber: this.state.fields.containerNumber.value,
      isoCode: this.state.fields.isoCode.value,
      truckTare: this.state.fields.truckGross.value - this.state.fields.containerTare.value,
      containerTare: this.state.fields.containerTare.value,
    }
    await this.setAllFieldErrors();
    if (!some(this.state.fields, field => field.errors.length > 0))
      APIService.freights().contracts().appendToUrl(`${get(fields.farmId.value, 'id')}/empty-container/`).post(containerData).then(
        response => {
          if (response && response.id)
            alertifyjs.success('Successfully Added', 2, () => window.location.reload())
          else if (response.errors)
            alertifyjs.error(get(response, 'errors.0', 'An Error Occurred!'));
          else
            alertifyjs.error('An Error Occurred!')
          this.props.forceStopLoader();
        }
      );
  }

  render() {
    const unit = getCountryDisplayUnit()
    return (
      <div className="cardForm cardForm--drawer" style={{ marginTop: '15px' }}>
        <div className="cardForm-content row padding-reset">
          <div className="col-sm-6 cardForm-header">
            <CommodityAutoComplete
              id="commodityId"
              onChange={this.handleSelectFieldChange}
              floatingLabelText="Commodity"
              commodityId={this.state.fields.commodityId.value}
              errorText={get(this.state, 'fields.commodityId.errors[0]', '')}
            />
          </div>
          <div className="col-sm-6 form-wrap-70" style={{ display: 'inline-block', marginTop: '16px' }}>
            <SiteAsyncAutocomplete
              variant='standard'
              id='farmId'
              label='Pack Site'
              onChange={this.handleSelectFieldChange}
              selected={this.state.fields.farmId.value}
              minLength={3}
              errorText={get(this.state.fields.farmId, 'errors[0]', '')}
              style={{ float: 'right' }}
              activeSitesOnly
              fetchOnlySitesOfCompany
              fetchTopSitesOnClear
            />
          </div>
          <div className="col-sm-6 form-wrap-70">
            <CommonSelectInput
              search
              allowEmptyOptions
              allowText={false}
              endpoint="trucks/search/"
              queryParams={{ is_active: true }}
              options={[]}
              optionMap={{ id: 'id', name: 'rego', companyId: 'companyId', companyName: 'companyName', totalWeights: 'totalWeights', categoryId: 'categoryId', code: 'code', steerPoint5: 'steerPoint5', steer1Point1: 'steer1Point1', transactionParticipation: 'transactionParticipation' }}
              inputText={get(find(this.state.trucks, { id: this.state.fields.truckId.value }), 'rego')}
              id='truckId'
              label={getCountryLabel('rego')}
              value={this.state.fields.truckId.value}
              selectedValue={this.state.fields.truckId.value}
              create={this.props.createTruck}
              createdTruck={this.props.createdTruck}
              isRegoAvailable={this.props.isRegoAvailable}
              actionCreator={addTruck}
              validate={this.props.validateRego}
              items={this.state.trucks}
              errorText={get(this.state.fields.truckId, 'errors[0]', '')}
              onChange={this.handleSelectFieldChange}
              onInput={(e) => {
                e.target.value = e.target.value.toString().slice(0, 10).replace(/[^0-9a-z]/gi, '');
              }}
            />
          </div>
          <div className="col-sm-6 form-wrap-70">
            <CommonTextField
              id="containerNumber"
              label="Container"
              placeholder="Please enter"
              onChange={this.onChange}
              value={this.state.fields.containerNumber.value}
              helperText={get(this.state.fields.containerNumber, 'errors[0]', '')}
              maxLength="100"
            />
          </div>
          <div className="col-sm-6 form-wrap-70" style={{ display: 'inline-block', marginTop: '16px' }}>
            <CommonTextField
              id="isoCode"
              value={this.state.fields.isoCode.value}
              label="ISO Code"
              helperText={get(this.state.fields.isoCode, 'errors[0]', '')}
              onChange={this.onChange}
            />
          </div>
          <div className="col-sm-6 form-wrap-70" style={{ display: 'inline-block', marginTop: '16px' }}>
            <NumberField
              id='containerTare'
              label='Container Tare'
              placeholder='Please enter'
              value={this.state.fields.containerTare.value}
              onChange={this.onChange}
              maxValue={999999999.99}
              helperText={get(this.state.fields.containerTare, 'errors[0]', '')}
              InputProps={{
                endAdornment: (
                  <InputAdornment position='end'>
                    {unit}
                  </InputAdornment>
                ),
              }}
            />
          </div>
          <div className='col-xs-4 form-wrap' style={{ display: 'inline-block', marginTop: '16px' }}>
            <NumberField
              id='truckGross'
              label='Truck Gross'
              placeholder='Please enter'
              value={this.state.fields.truckGross.value}
              onChange={this.onChange}
              maxValue={999999999.99}
              helperText={get(this.state.fields.truckGross, 'errors[0]', '')}
              InputProps={{
                endAdornment: (
                  <InputAdornment position='end'>
                    {unit}
                  </InputAdornment>
                ),
              }}
            />
          </div>
          <div className='col-xs-12 cardForm-action padding-reset'>
            <CommonButton
              label='Cancel'
              variant='outlined'
              onClick={() => this.props.handleClose()}
            />
            {
              <CommonButton
                type='submit'
                label='Save'
                primary={true}
                variant="contained"
                onClick={() => this.handleSubmit()}
              />
            }
          </div>
        </div>
      </div>
    )
  }
}

const mapStateToProps = state => {
  return {
    token: state.main.user.token,
    userCompanyId: state.main.user.user.companyId,
    user: state.main.user.user,
    commodities: state.master.commodities.items,
    isRegoAvailable: state.main.isRegoAvailable,
    createdTruck: state.companies.companies.company.trucks.createdTruck,
    isLoading: state.main.isLoading,
  };
};

const mapDispatchToProps = dispatch => ({
  getCommodities: includeUnknown => dispatch(getCommodities(includeUnknown)),
  validateRego: (key, value, callback) => dispatch(validateRego(key, value, callback)),
  createTruck: (companyId, data, addTruck) => dispatch(createTruck(companyId, data, addTruck)),
  emptyCreatedTruck: () => dispatch(emptyCreatedTruck()),
  isLoading: () => dispatch(isLoading),
  forceStopLoader: () => dispatch(forceStopLoader())
});

export default connect(mapStateToProps, mapDispatchToProps)(EmptyContainerForm);
