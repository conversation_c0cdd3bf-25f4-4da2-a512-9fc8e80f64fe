$white: #FFF;
$black: #000;
$faded-gray: rgba(0, 0, 0, 0.5);

.freight-scheduler {
    position: absolute;
    top: 88px;
    bottom: 0;
    width: 94.8%;
    margin-left: -10px;
    padding: 0px;
    background: $white;
    .workspace {
        background: $white;
        box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.74);
        -webkit-box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.74);
        -moz-box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.74);
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
        position: absolute;
        top: 48px;
    }
    .section {
        padding: 5px;
        &.dates {
            border-bottom: 1px solid lightgray;
        }
        &.left-section {
            height: 86vh;
            overflow: auto;
            padding-right: 0px;
            border-top: 1px solid lightgray;
            .orders-label {
                font-size: 16px;
            }
            .order {
                margin: 2px 0;
                white-space: nowrap;
                cursor: pointer;
            }
        }
        &.controls {
            border-bottom: 1px solid lightgray;
            padding: 5px 10px;
            padding-left: 0px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            .control {
                margin: 0 5px;
            }
        }
        &.dates {
            padding: 2px;
            border-top: 1px solid lightgray;
        }
    }
    .truck-section {
        display: inline-block;
        padding-right: 5px;
    }
    .right-section {
        border-left: 1px solid lightgray;
        padding: 0px;
        .date-section {
            padding: 0 2px;
            display: inline-block;
            text-align: left;
            &.data-container {
                padding-bottom: 20px;
            }
            .date-label {
                &.selected {
                    font-weight: bold;
                    color: $black;
                }
                color: $faded-gray;
                font-weight: 400;
                .month-day {
                    font-size: 26px;
                }
                .week-day {
                    font-size: 12px;
                    margin-left: 4px;
                    font-weight: bold;
                }
            }
        }
        .movements-section {
            width: 90%;
            display: flex;
        }
        .content {
            height: 77vh;
            position: relative;
            padding: 0;
            overflow: auto;
            .truck-row {
                padding: 0 2px;
                display: flex;
                min-height: 115px;
                &.collapsed {
                    min-height: inherit;
                    max-height: 40px;
                    overflow: hidden;
                    opacity: 0.4;
                }
            }
            .content-container {
                padding: 0;
                .truck, .movement {
                    margin: 2px 0;
                    white-space: nowrap;
                    cursor: pointer;
                }
            }
        }
    }
}

.movement-dialog-title > h6 {
    color: $white;
}

.circle {
    width: 7px;
    height: 7px;
    border-radius: 50%;
    border: 2px solid;
}

.title-label {
    color: #808080;
    font-size:11px;
    width: 44px;
    height: 13px;
}

.container {
    display: inline-block;

    .title-child {
        margin-left: 8px;
    }
}

.dialog-header {
    text-align: center;
    font-size: 18px;
    display: inline-block;
    flex-grow: 1;
    border-right: 1px solid rgba(0, 0, 0, 0.12);
    padding-top: 10px
}

.dialog-header-content {
    font-size: 16px;
    padding: 5px 0px;
}

.header-items {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

span.highlight-search-results {
    background-color: lightgray;
    font-weight: 1000;
    font-size: 15px;
}
