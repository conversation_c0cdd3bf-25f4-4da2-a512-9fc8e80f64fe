import React from 'react';
import { connect } from 'react-redux';
import { allocatedContractOrderWarning, createOrdersForAllocatedContract, getOrders, getOrdersResponse } from '../../actions/companies/orders';
import FreightOrdersTable from '../../containers/FreightOrdersTable';
import FreightAllocationsTable from '../../containers/FreightAllocationsTable';
import Paper from '@mui/material/Paper';
import { setHeaderText, setBreadcrumbs, setSubHeaderText, isSearchApplied } from '../../actions/main';
import { receiveOrder } from '../../actions/companies/orders';
import has from 'lodash/has';
import get from 'lodash/get';
import includes from 'lodash/includes';
import { getContractSubHeaderText, getOrderSubHeaderText, attachCSVEventListener, isDirectToBuyerAllocationEnabled, defaultViewAction, getOrderHeaderText } from '../../common/utils';
import isEqual from 'lodash/isEqual';
import isEmpty from 'lodash/isEmpty';
import APIService from '../../services/APIService';
import { receiveAllocation } from '../../actions/companies/orders';
import {
  CALL_ON_GRAIN_TYPE_ID, FO_GO_INVOICING, COMPANY_ADMIN, OFFICE_ADMIN, SYSTEM, FO_GO_FILTER_STATUSES,
  OBSERVER_TYPE_ID, REQUEST_ORDER_TYPE_IDS, PICKUP_REQUEST_ORDER_TYPE_ID, DELIVERY_REQUEST_ORDER_TYPE_ID, getFreightOrdersGlobalListingHeaders, ORDERS_TABLE_COLUMN_LIMIT, DEFAULT_ORDERS_TABLE_COLUMN_LIMIT, PREDEFINED_DATE_RANGE_FILTER_KEYS, FILTER_KEYS_TO_EXCLUDE, ORDER_FILTER_KEYS_MAPPING,
  ORDER_TYPE_ROUTE_MAPPING
} from '../../common/constants';
import { setDownloadBar } from '../../actions/main';
import Filters from '../common/Filters';
import SideDrawer from '../common/SideDrawer';
import DownloadDataDialog from '../common/DownloadDataDialog';
import { isLoading, forceStopLoader } from '../../actions/main';
import { isAtGlobalOrders } from '../../common/utils';
import CustomHeaderOptions from '../common/CustomHeaderOptions';
import FiltersAppliedChip from '../common/FiltersAppliedChip';
import alertifyjs from 'alertifyjs';
import AllocatedContractWarningDialog from './AllocatedContractWarningDialog';
import ListingControls from '../common/ListingControls'

class FreightOrders extends React.Component {
  constructor(props) {
    super(props);
    this.csvLink = React.createRef();
    this.state = {
      isFetchingOrders: false,
      csvData: [],
      openCogForm: false,
      applyFilters: false,
      openSideDrawer: false,
      filters: {},
      filter_statuses: FO_GO_FILTER_STATUSES,
      invoicing: FO_GO_INVOICING,
      filterValues: {
        commodity__id__in: [],
        planned_grade__id__in: [],
        status__in: [],
        customer__company__id__in: [],
        provider__id__in: [],
        delivery_start_date_range: '',
        delivery_end_date_range: '',
        freight_pickup__date_time__gte: '',
        freight_delivery__date_time__lte: '',
        season__in: [],
        invoicing__in: [],
        freight_pickup__consignor__handler__id__in: [],
        freight_delivery__consignee__handler__id__in: [],
        updated_at_date_range: '',
        updated_at__lte: '',
        updated_at__gte: ''
      },
      customTableColumnOptions: false,
      customTableColumnNames: {},
      customColumnTitle: undefined,
      isFilteredCsv: false,
      csvPopup: false,
      searchView: false,
      isFetchingWarning: false,
      customColumns: true,
    };
    this.handleAddOrderButtonClick = this.handleAddOrderButtonClick.bind(this);
    this.onDownloadResponse = this.onDownloadResponse.bind(this);
    this.onCloseDownloadResponse = this.onCloseDownloadResponse.bind(this);
    this.getActionsOptionMapperListItems = this.getActionsOptionMapperListItems.bind(this);
    this.customCsvEnabled = this.customCsvEnabled.bind(this);
    this.toggleCustomColumnDownloads = this.toggleCustomColumnDownloads.bind(this);
  }

  handleFilters = bool => {
    this.setState({
      applyFilters: bool,
      openSideDrawer: bool,
    });
  };

  resetFilters = () => {
    this.setState({filters: {}, applyFilters: false, openSideDrawer: false}, () => this.handleFilterState('applyFilters', false))
  }

  handleFilterState = (key, value) => {
    this.setState({[key]: value}, () => {
      if(key === 'applyFilters' && isAtGlobalOrders()) {
        const { filters } = this.state;
        APIService.profiles()
                  .filters()
                  .post({ freight_order: filters }, this.props.token)
                  .then(res => {
                    this.props.isLoading();
                    this.setState({ isFetchingOrders: true, filters: res?.filters?.freight_order || {} }, () => {
                      this.props.onGetOrders(null, null);
                    });
                  });
      }
    });
  };

  onCloseDownloadResponse() {
    this.props.setDownloadBar(false);
  }

  onDownloadResponse(message) {
    this.props.setDownloadBar(message, true, this.onCloseDownloadResponse);
  }

  _attachCSVEventListener() {
    attachCSVEventListener('freight-orders-csv-ready', 'Orders', this.onDownloadResponse);
  }
  componentWillUnmount() {
    this.props.unMountOrders();
    this.props.applySearch(null);
    if(window.location.hash.includes('?') && isAtGlobalOrders())
      window.location.hash = window.location.hash.split('?')[0]
  }

  componentDidMount() {
    this._attachCSVEventListener();
    this.setHeaderAndBreadcrumbs();
    this.props.applySearch(null);
    const queryParams = new URLSearchParams(this.props.location.search);

    APIService.profiles()
      .filters('freight_order')
      .get(this.props.token)
      .then(res => {
        this.setState({
          filters: get(res, 'freight_order', {}),
        });
      });

    this.commodityContractId = queryParams.get('commodity_contract_id');
    this.parentOrderId = queryParams.get('parent_order_id') || this.props.parentOrderId;
    if (!this.parentOrderId) {
      this.props.unsetSelectedOrder();
    }
    if (!this.commodityContractId && !this.parentOrderId) {
      this.props.unsetSelectedFreightAllocation();
    }
    if (!this.state.isFetchingOrders) {
      if (this.props.match.params && has(this.props.match.params, 'contract_id')) {
        this.commodityContractId = this.props.match.params.contract_id;
        this.setState({ isFetchingOrders: true }, () => {
          this.props.onGetOrders(null, this.commodityContractId, null, this.props.selectedUnit);
        });
      } else {
        this.setState({ isFetchingOrders: true }, () => {
          this.props.onGetOrders(this.parentOrderId, this.commodityContractId, this.props.isRequestOrder, this.props.selectedUnit);
        });
      }
    }
  }

  componentDidUpdate(prevProps) {
    if (get(prevProps, 'count') !== this.props.count || this.props.order)
      this.setHeaderAndBreadcrumbs();
    const queryParams = new URLSearchParams(this.props.location.search);
    if ((isEmpty(this.props.orders) && !this.state.isFetchingOrders) || prevProps.selectedUnit !== this.props.selectedUnit) {
      if (this.props.match.params && has(this.props.match.params, 'contract_id')) {
        this.commodityContractId = this.props.match.params.contract_id;
        this.setState({ isFetchingOrders: true }, () => {
          this.props.onGetOrders(null, this.commodityContractId, false, this.props.selectedUnit);
        });
      } else if (this.props.parentOrderId !== prevProps.parentOrderId || this.commodityContractId != queryParams.get('commodity_contract_id')) {
        this.parentOrderId = this.props.parentOrderId;
        this.commodityContractId = queryParams.get('commodity_contract_id');
        this.setState({ isFetchingOrders: true }, () => {
          this.props.onGetOrders(this.parentOrderId, this.commodityContractId, false, this.props.selectedUnit);
        });
      }
    }
  }

  setHeaderAndBreadcrumbs() {
    const isCallOnGrain = this.isCallOnGrainOrder()
    const isRequestOrder = this.isRequestOrder()
    const freightOrderLabel = window.NEW_ORDERS_VIEW_TOGGLE ? '' : 'Freight '
    let breadcrumbs = [{ text: `${freightOrderLabel}Orders (${this.props.count})` }];
    let headerText = `${freightOrderLabel}Orders`;
    let subHeaderText = null

    if (this.props.contractId || this.props.contract) {
      breadcrumbs = [
        { text: 'Contracts', route: '/contracts' },
        { text: get(this.props.contract, 'referenceNumber', ''), route: '/contracts/' + this.props.contractId + '/contract' },
        { text: `Orders (${this.props.count})` },
      ];
      headerText = 'Commodity Contract ' + get(this.props.contract, 'referenceNumber', '');
      subHeaderText = getContractSubHeaderText(this.props.contract);
    } else if (this.props.farmId) {
      let farmRoute = '/stocks/storages-view?farmId=' + this.props.farmId
      breadcrumbs = [{ text: 'Farms', route: '/farms' }, { text: this.props.farmName, route: farmRoute }, { text: `Orders (${this.props.count})` }];
      headerText = this.props.farmName;
    } else if (this.props.parentOrderId || this.props.order) {
      const orderTypeId = get(this.props.order, 'typeId')
      const orderType = get(ORDER_TYPE_ROUTE_MAPPING, orderTypeId, 'freights')
      headerText = getOrderHeaderText(this.props.order)

      breadcrumbs = [
        { text: 'Orders', route: `/orders/${orderType}` },
        { text: get(this.props.order, 'identifier', ''), route: '/freights/orders/' + this.props.parentOrderId + '/order' },
        { text: (isCallOnGrain || isRequestOrder) ? `${freightOrderLabel}Orders (${this.props.count})` : `Allocations (${this.props.count})` },
      ];
      subHeaderText = getOrderSubHeaderText(this.props.order)
    }

    if (!isEqual(this.props.breadcrumbs, breadcrumbs))
      this.props.setBreadcrumbs(breadcrumbs);

    if (headerText && !isEqual(this.props.headerText, headerText))
      this.props.setHeaderText(headerText);

    if (subHeaderText && !isEqual(this.props.subHeaderText, subHeaderText))
      this.props.setSubHeaderText(subHeaderText);
  }

  newOrderButton = () => {
    const func = this.props.handleAddOrderButtonClick || this.props.onHandleAddOrderButtonClick;
    const canCreateFreightOrder = get(this.props, 'order.canCreateFreightOrder');
    const order = get(this.props, 'order');
    if (func) func();
    else if (canCreateFreightOrder || !order) window.location = window.NEW_ORDERS_VIEW_TOGGLE ? '/#/orders/new' : '/#/freights/orders/new';
  }

  handleAddOrderButtonClick = () => {
    const { contract, allocations, dispatch, orderWarningFlag, createOrder } = this.props
    const callback = () => {
      if (!orderWarningFlag)
        this.newOrderButton();
    }
    if (this.commodityContractId && isDirectToBuyerAllocationEnabled()) {
      if(allocations?.length > 0) {
        let _allocations = allocations.filter(item => !item?.counterPartyTransactionParticipator);
        dispatch(allocatedContractOrderWarning(_allocations.length > 0, _allocations))
        if(!_allocations?.length)
          callback()
      } else {
        const isSaleContract = get(contract, 'isSaleContract', false);
        const isSeller = contract.isSeller
        const isBuyer = contract.isBuyer
        const path = (isSeller && isSaleContract) ? '/purchase-allocations/' : (isBuyer && !isSeller) ? '/sale-allocations/' : '/purchase-allocations/';
        createOrder(this.commodityContractId, path, callback);
      }
    }
    else this.newOrderButton();
  }

  fetchCSVData = () => {
    const { setDownloadBar } = this.props;
    var param = this.state.isFilteredCsv ? 'show_filters': '';
    setDownloadBar('Your Orders CSV is getting prepared. Please visit <a href="/#/downloads">Downloads</a> in few moments.', true);
    const service = APIService.freights().orders();
    if (this.state.searchView && this.props.isSearchApplied)
      param+= param.length == 0 ? `search=${this.props.isSearchApplied}` : `&search=${this.props.isSearchApplied}`;
    service.appendToUrl(`csv/?${param + '&type_id=1&type_id=2'}`);
    if(this.parentOrderId)
      service.appendToUrl(`&parent_order_id=${this.parentOrderId}`);
    else if(this.commodityContractId)
      service.appendToUrl(`&commodity_contract_id=${this.commodityContractId}`);
    if (this.state.customColumns)
      service.appendToUrl('&custom_csv');
    this.setState({csvPopup: false, searchView: false})

    service
      .get(this.props.token, {
        'Content-Type': 'text/csv',
        Accept: 'text/csv',
      })
      .then(csvData => {
        this.setState({ csvData: csvData || [] });
      });
  };

  canExportCSV() {
    return includes([COMPANY_ADMIN, OFFICE_ADMIN, SYSTEM, OBSERVER_TYPE_ID], get(this.props.currentUser, 'typeId'));
  }

  isRequestOrder() {
    return includes(REQUEST_ORDER_TYPE_IDS, get(this.props.order, 'typeId'))
  }

  isPickupRequestOrder() {
    return get(this.props.order, 'typeId') === PICKUP_REQUEST_ORDER_TYPE_ID
  }

  isDeliveryRequestOrder() {
    return get(this.props.order, 'typeId') === DELIVERY_REQUEST_ORDER_TYPE_ID;
  }

  isCallOnGrainOrder() {
    return get(this.props.order, 'typeId') === CALL_ON_GRAIN_TYPE_ID
  }

  getActionsOptionMapperListItems() {
    return [
      { name: 'Custom Table Columns', fx: () => this.updateCustomTableColumns() },
      defaultViewAction
    ];
  }

  async updateCustomTableColumns() {
    if (this.props.currentUser.company.enableCustomCsv) {
      const tableColumnNames = await APIService.profiles().appendToUrl(`${this.props.currentUser.id}/table-preferences/freight_order_table/`).get(this.props.token);
      this.setState({customTableColumnNames: tableColumnNames, customTableColumnOptions: true});
    }
    else {
      alertifyjs.alert(
        'Permission Denied',
        'This feature is not enabled for your company. Please contact AgriChain support',
        () => { },
      );
    }
  }

  getColumnsMapping() {
    const freightOrderColumns = getFreightOrdersGlobalListingHeaders(false);
    return freightOrderColumns.reduce((obj, objectKey) => ({ ...obj, [objectKey.key]: objectKey.header }), {});
  }

  updateColumnCount(count) {
    this.setState({customColumnTitle: `Edit Columns (${count})`});
  }

  customFilterValueExist = filterKeys => filterKeys.some(key => Boolean(get(this.state.filters, key)))

  filterCriteria = (key, value) => includes(FILTER_KEYS_TO_EXCLUDE, key) ? false : includes(PREDEFINED_DATE_RANGE_FILTER_KEYS, key) && value === 'custom' ? this.customFilterValueExist(get(ORDER_FILTER_KEYS_MAPPING, key)) : value.length !== 0

  customCsvEnabled(isFilteredCsv) {
    const newState = {...this.state};
    newState.isFilteredCsv = isFilteredCsv;
    if (this.props.currentUser.company.enableCustomCsv || this.props.isSearchApplied) {
      newState.csvPopup = true;
      this.setState(newState);
    }
    else {
      newState.customColumns = false;
      this.setState(newState, this.fetchCSVData);
    }
  }

  toggleCustomColumnDownloads = () => {
    this.setState({customColumns: !this.state.customColumns})
  }

  render() {
    const isCallOnGrain = this.isCallOnGrainOrder();
    const isRequestOrder = this.isRequestOrder()
    const canExportCSV = this.canExportCSV()
    const globalListing = isAtGlobalOrders()
    return (
      <Paper className='paper-table'>
        <div style={{ position: 'relative' }}>
          <div style={{ position: 'absolute', right: '0px', top: '0px' }}>
            <ListingControls
              controls={[
                {
                  type: 'new',
                  order: 1,
                  props: {
                    label: this.props.parentOrderId && !isCallOnGrain && !isRequestOrder ? 'Freight Allocation' : 'Order',
                    onClick: this.handleAddOrderButtonClick,
                    app: 'order',
                    tooltipTitle: 'Create a new Freight Order',
                    tooltipPlacement: 'top'
                  }
                },
                {
                  type: 'filter',
                  hidden: !globalListing,
                  props: globalListing ? {
                    value: this.state.applyFilters,
                    onClick: () => this.handleFilters(true),
                    applied: Object.entries(this.state.filters).filter(val => this.filterCriteria(val[0], val[1]))?.length || 0
                  } : {}
                },
                {
                  type: 'report',
                  hidden: !canExportCSV,
                  props: canExportCSV ? {
                    defaultHandler: () => this.customCsvEnabled(false),
                    showMenus: globalListing && !isEmpty(Object.entries(this.state.filters).filter(val => val[1].length !== 0)),
                    optionMapper: [
                      { name: 'Complete List', description: 'List of all orders present', fx: () => this.customCsvEnabled(false) },
                      { name: 'Filtered List', description: 'List of orders matching current filter selection', fx: () => this.customCsvEnabled(true) },
                    ]
                  } : {}
                },
                {
                  type: 'action',
                  props: {
                    showMenus: true,
                    optionMapper: this.getActionsOptionMapperListItems()
                  }
                },
              ]}
            />
            <DownloadDataDialog
              open={this.state.csvPopup}
              onClose={() => this.setState({csvPopup: false, searchView: false})}
              title='Orders Report'
              enableCustomCsv={this.props.currentUser.company.enableCustomCsv}
              isSearchApplied={this.props.isSearchApplied}
              searchView={this.state.searchView}
              onSearchViewChange={() => this.setState({searchView: !this.state.searchView})}
              isFilteredCsv={this.state.isFilteredCsv}
              onDownload={this.fetchCSVData}
              customColumnTitle={this.state.customColumnTitle}
              user={this.props.currentUser}
              csvType="freight_orders_csv"
              updateColumnCount={(count) => this.updateColumnCount(count)}
              toggleCustomColumnDownloads={this.toggleCustomColumnDownloads}
            />
            {
              this.state.applyFilters &&
              <SideDrawer isOpen={this.state.openSideDrawer} title='Filters' size='big' onClose={() => this.handleFilters(false)} app='filters'>
                <Filters
                  isLoading={this.props.isLoading}
                  forceStopLoader={this.props.forceStopLoader}
                  handleFilterState={this.handleFilterState}
                  filters={this.state.filters}
                  statusTemp={this.state.filter_statuses}
                  invoicingTemp={this.state.invoicing}
                  filterValues={this.state.filterValues}
                />
              </SideDrawer>
            }
            <SideDrawer
              isOpen={this.state.customTableColumnOptions}
              title={this.state.customColumnTitle}
              onClose={() => this.setState({customTableColumnOptions: false})}
              size="small"
            >
              <CustomHeaderOptions
                customColumns={this.state.customTableColumnNames}
                closeDrawer={() => this.setState({customTableColumnOptions: false})}
                user={this.props.currentUser}
                token={this.props.token}
                table_type="freight_order_table"
                columnsMapping={this.getColumnsMapping()}
                maxColumnLimit={ORDERS_TABLE_COLUMN_LIMIT}
                updateColumnCount={(count) => this.updateColumnCount(count)}
                defaultColumnLimit={DEFAULT_ORDERS_TABLE_COLUMN_LIMIT}
              />
            </SideDrawer>
            {this.props.orderWarningFlag && isDirectToBuyerAllocationEnabled() && (
            <AllocatedContractWarningDialog {...this.props} newOrderButton={this.newOrderButton} handleToUpdate={() => this.props.closeWarningDialog(false, [])} />
            )}
          </div>
          <FiltersAppliedChip filters={this.state.filters} show={isAtGlobalOrders()} style={{paddingRight: '45%'}} onClear={this.resetFilters} />
          {this.props.parentOrderId && !isCallOnGrain && !isRequestOrder ?
            <FreightAllocationsTable dontRedirect={this.props.dontRedirect} /> :
            <FreightOrdersTable dontRedirect={this.props.dontRedirect} isContractDetailsModule={Boolean(this.props.contractId)}/>
          }
        </div>
      </Paper>
    );
  }
}

const mapDispatchToProps = dispatch => ({
  unMountOrders: () => dispatch(getOrdersResponse([])),
  onGetOrders: (parentOrderId, commodityContractId, onlyLinked, unit) => dispatch(getOrders(parentOrderId, commodityContractId, null, false, onlyLinked, unit)),
  setHeaderText: text => dispatch(setHeaderText(text)),
  setSubHeaderText: text => dispatch(setSubHeaderText(text)),
  setBreadcrumbs: breadcrumbs => dispatch(setBreadcrumbs(breadcrumbs)),
  unsetSelectedOrder: () => dispatch(receiveOrder(null)),
  unsetSelectedFreightAllocation: () => dispatch(receiveAllocation(null)),
  setDownloadBar: (message, isOpen, onClose) => dispatch(setDownloadBar(message, isOpen, onClose)),
  isLoading: (waitForComponent) => dispatch(isLoading(waitForComponent)),
  forceStopLoader: () => dispatch(forceStopLoader()),
  closeWarningDialog: (value, listData) => dispatch(allocatedContractOrderWarning(value, listData)),
  createOrder: (contractId, path, callback) => dispatch(createOrdersForAllocatedContract(contractId, path, callback)),
  applySearch: searchStr => dispatch(isSearchApplied(searchStr)),
});

const mapStateToProps = state => {
  return {
    orders: state.companies.orders.items,
    token: state.main.user.token,
    currentUser: state.main.user.user,
    selectedFarm: state.companies.farms.selectedFarm,
    count: get(state.companies.orders, 'paginationData.count') || 0,
    orderWarningList: get(state.companies.orders, 'orderWarningList'),
    orderWarningFlag: get(state.companies.orders, 'orderWarningFlag'),
    isSearchApplied: state.main.isSearchApplied,
    headerText: state.main.headerText,
    breadcrumbs: state.main.breadcrumbs,
    subHeaderText: state.main.subHeaderText
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(FreightOrders);
