import React, { Component } from 'react';

import { connect } from 'react-redux';
import Grid from '@mui/material/Grid';
import { positiveDecimalFilter } from '../../../common/input-filters';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import withStyles from '@mui/styles/withStyles';
import CommonTextField from '../../common/CommonTextField';
import CommonButton from '../../common/CommonButton';
import { required } from '../../../common/validators';
import set from 'lodash/set';
import has from 'lodash/has';
import forEach from 'lodash/forEach';
import {isLoading} from "../../../actions/main";
import {convertEpochToDateFormat} from "../../../common/momentUtilities";
import CommonDatePicker from "../../common/CommonDatePicker";
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
import some from 'lodash/some';
import alertifyjs from 'alertifyjs';
import {confirmOrderAmend, raiseOrderAmendRequest, rejectOrderAmend} from "../../../actions/companies/freights";
import {RejectionReasonDialog} from "../../rejections/RejectionReasonDialog";
import { getCountryCurrency } from '../../../common/utils';
import NumberField from '../../common/NumberField';

const EXCESS_CONTRACT_TONNAGE_ERROR_MESSAGE = 'Order tonnage cannot be greater than Contract tonnage';
const EXCESS_ORDER_TONNAGE_ERROR_MESSAGE = 'Allocation tonnage cannot be greater than Order tonnage';

const styles = () => ({
  subTitle: {
    fontSize: 13,
    fontWeight: 500,
    color: '#112c42',
  },
  listItem: {
    display: 'block',
    height: 85,
    padding: 0,
  },
  secondaryListItem: {
    padding: 0,
  },
  primaryListItem: {
    padding: 0,
  },
});

class AmendOrder extends Component {
  constructor(props){
    super(props);

    this.classes = this.props.classes;

    this.tonnage = this.props.order.plannedTonnage;
    this.deliveryStartDate = convertEpochToDateFormat(this.props.order.freightPickup.date);
    this.deliveryEndDate = convertEpochToDateFormat(this.props.order.freightDelivery.date);
    this.freightRateIn = this.props.order.rateFreightIn;
    this.freightRateOut = this.props.order.rateFreightOut;

    this.orderId = this.props.order.id;
    this.isAmendRequestPending = this.props.order.isAmendRequestPending;
    this.amendable = this.props.order.amendable;
    this.amendedDetails = this.props.order.amendedDetails;

    this.state = {
      amendedTonnage: {
        value: get(this.amendedDetails, 'plannedTonnage', null),
        validators: [required()],
        errors: []
      },
      amendedDeliveryStartDate: {
        value: get(this.amendedDetails, 'freightPickup.date', null),
        validators: [required()],
        errors: []
      },
      amendedDeliveryEndDate: {
        value: get(this.amendedDetails, 'freightDelivery.date', null),
        validators: [required()],
        errors: []
      },
      amendedFreightRateIn: {
        value: get(this.amendedDetails, 'rateFreightIn', null),
        validators: [required()],
        errors: []
      },
      amendedFreightRateOut: {
        value: get(this.amendedDetails, 'rateFreightOut', null),
        validators: [required()],
        errors: []
      },

      rejectDialogOpen: {
        value: false,
        validators: [],
        errors: []
      },
      rejectionReason: {
        value: "",
        validators: [],
        errors: []
      },
    };

    this.handleTextFieldChange = this.handleTextFieldChange.bind(this);
    this.addOrderAcceptanceRequestForAmend = this.addOrderAcceptanceRequestForAmend.bind(this);
    this.confirmAmendRequestWrapper = this.confirmAmendRequestWrapper.bind(this);
    this.rejectAmendRequestWrapper = this.rejectAmendRequestWrapper.bind(this);
    this.setFieldErrors = this.setFieldErrors.bind(this);
    this.getFieldErrors = this.getFieldErrors.bind(this);
    this.isFormValid = this.isFormValid.bind(this);
    this.handleBlur = this.handleBlur.bind(this);
    this.closeSidebar = this.props.closeSidebar;
    this.raiseAmendRequest = this.props.raiseAmendRequest;
    this.confirmAmendRequest = this.props.confirmAmendRequest;
    this.rejectAmendRequest = this.props.rejectAmendRequest;
    this.isLoading = this.props.isLoading;
  }

  handleRejectClickOpen = () => {
    const newState = { ...this.state };
    newState.rejectDialogOpen.value = true;
    newState.rejectionReason.validators = [required()];
    if(!newState.rejectionReason.value){
      newState.rejectionReason.errors = [];
    }
    this.setState(newState);
  };

  handleRejectClose = () => {
    this.setState(
      {rejectDialogOpen: { errors: [], value: false, validators: []}}
    );
  };

  handleRejectSubmit = (event) => {
    this.setReasonErrors();
    const data = { rejectionReason: this.state.rejectionReason.value };
    if (this.state.rejectionReason.errors.length === 0) {
      this.rejectAmendRequestWrapper(data);
    }
    event.preventDefault();
  };

  handleReasonChange = (event) => {
    const value = event.target.value;
    const newState = {...this.state};
    newState.rejectionReason.value = value;
    this.setState(newState, () => this.setReasonErrors());
  };

  setReasonErrors(errors) {
    const newState = {...this.state};
    newState.rejectionReason.errors = errors || this.getReasonErrors();
    this.setState(newState);
  }

  getReasonErrors() {
    const errors = [];
    const value = get(this.state, `rejectionReason.value`);
    const validators = get(this.state, `rejectionReason.validators`, []);
    validators.forEach((validator) => {
      if (validator.isInvalid(value)) {
        errors.push(validator.message);
      }
    });
    return errors;
  }

  handleTextFieldChange(event){
    const newState = {...this.state};
    set(newState, event.target.id + '.value', event.target.value);
    this.setFieldErrors();
    this.setTonnageErrors();
    this.setState(newState);
  }

  setTonnageErrors = () => {
    const newState = {...this.state};
    const baseEntity = get(this.props.order, 'parentOrderId') || get(this.props.order, 'commodityContractId');
    const parentUnaccountedTonnage = parseFloat(get(this.props.order, 'parentUnaccountedTonnage', 0));
    const parentAccountedTonnage = parseFloat(get(this.props.order, 'parentAccountedTonnage', 0));
    const tonnage = parseFloat(newState.amendedTonnage.value);
    if (baseEntity){
      if(tonnage > parentUnaccountedTonnage){
        newState.amendedTonnage.errors.push(
          get(this.props.order, 'parentOrderId') ? EXCESS_ORDER_TONNAGE_ERROR_MESSAGE : EXCESS_CONTRACT_TONNAGE_ERROR_MESSAGE
        );
      } else if(tonnage < parentAccountedTonnage){
        newState.amendedTonnage.errors.push(
          [`Tonnage should be greater than ${parentAccountedTonnage} MT as it has already been planned`]
        );
      }
    }
  };

  handleSelectFieldChange = (value, id) => {
    this.setFieldValue(id, value);
    this.setFieldErrors();
  };

  setFieldValue = (id, value) => {
    const newState = {...this.state};
    set(newState, id + '.value', value);
    this.setState(newState);
  };

  handleFreightRateChange = (event) => {
    this.setFieldValue(event.target.id, event.target.value);
    this.setFieldErrors();
    setTimeout(() => {
      this.setRateError();
      this.setTonnageErrors();
    }, 100);
  };

  validateData = () => {
    this.setFieldErrors();
    if (this.isFormValid()) {
      if(some([
          (parseFloat(this.state.amendedTonnage.value) || this.tonnage) !==  this.tonnage,
          (parseFloat(this.state.amendedFreightRateIn.value) || this.freightRateIn) !== this.freightRateIn,
          (parseFloat(this.state.amendedFreightRateOut.value) || this.freightRateOut) !== this.freightRateOut,
          (this.state.amendedDeliveryStartDate.value || this.props.order.freightPickup.date) !== this.props.order.freightPickup.date,
          (this.state.amendedDeliveryEndDate.value || this.props.order.freightDelivery.date) !== this.props.order.freightDelivery.date,
        ], Boolean)) {
          this.addOrderAcceptanceRequestForAmend();
      } else {
        alertifyjs.alert("Amend Freight Order", "You haven't amended any details");
      }
    }
  };

  addOrderAcceptanceRequestForAmend(){
    const payload = {
      plannedTonnage: this.state.amendedTonnage.value || this.tonnage,
      rateFreightIn: this.state.amendedFreightRateIn.value || this.freightRateIn,
      rateFreightOut: this.state.amendedFreightRateOut.value || this.freightRateOut,
      freightPickup:{date: this.state.amendedDeliveryStartDate.value || this.props.order.freightPickup.date},
      freightDelivery:{date: this.state.amendedDeliveryEndDate.value || this.props.order.freightDelivery.date}
    };
    this.isLoading('orderDetail');
    this.closeSidebar();
    this.raiseAmendRequest(this.orderId, payload);
  }

  confirmAmendRequestWrapper(){
    this.isLoading('orderDetail');
    this.closeSidebar();
    this.confirmAmendRequest(this.orderId);
  }

  rejectAmendRequestWrapper(data){
    this.isLoading('orderDetail');
    this.closeSidebar();
    this.rejectAmendRequest(this.orderId, data);
  }

  handleBlur(event){
    const newState = {...this.state};
    this.getFieldErrors(newState[event.target.id], newState);
    this.setRateError();
    this.setTonnageErrors();
    this.setState(newState);
  }

  setRateError = () => {
    this.setRateOutError();
    this.setRateInError();
  };

  setRateOutError = () => {
    const newState = {...this.state};
    newState.amendedFreightRateOut.errors = [];
    this.setState(newState);
  };

  setRateInError = () => {
    const newState = {...this.state};
    newState.amendedFreightRateIn.errors = [];
    this.setState(newState);
  };

  setFieldErrors(){
    const newState = {...this.state};
    let validFields = 0;
    forEach(newState, (field) => {
      const isValid = this.getFieldErrors(field, newState);
      if (isValid){
        validFields += 1;
      }
    });
    if (validFields){
      forEach(newState, (stateField) => {
        stateField.errors = [];
      });
      this.setRateError();
      this.setTonnageErrors();
    }
    this.setState(newState);
  }

  getFieldErrors(field){
    field.errors = [];
    if (has(field, 'value') && has(field, 'validators')){
      field.validators.forEach((validator) => {
        if (validator.isInvalid(field.value)){
          field.errors.push(validator.message);
        }
      });
    }
    return isEmpty(field.errors);
  }

  minDeliveryDate = () => {
    return new Date(this.state.amendedDeliveryStartDate.value || this.props.order.freightPickup.date);
  };

  maxPickupDate = () => {
    return new Date(this.state.amendedDeliveryEndDate.value || this.props.order.freightDelivery.date);
  };

  isFormValid(){
    var formValid = true;
    const currentState = {...this.state};
    forEach(currentState, (field) => {
      if (field.errors.length !== 0){
        formValid = false;
      }
    });
    return formValid;
  }

  render(){
    const currency = this.props.order?.currency || getCountryCurrency()
    return (
      <Grid container spacing={8} style={{display: 'inline'}}>
        <Grid item xs={6}>
          <Typography variant="subheading" className={this.classes.subTitle}>
            Current
          </Typography>
          <List>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Tonnage" className={this.classes.secondaryListItem} />
              <ListItemText primary={`${this.tonnage} MT`} className={this.classes.primaryListItem} />
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery Start Date" className={this.classes.secondaryListItem} />
              <ListItemText primary={`${this.deliveryStartDate}`} className={this.classes.primaryListItem} />
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery End Date" className={this.classes.secondaryListItem} />
              <ListItemText primary={`${this.deliveryEndDate}`} className={this.classes.primaryListItem} />
            </ListItem>
            {((!this.props.order.isFreightProvider && !this.props.order.isCustomer && !this.props.order.isCustomerRegistered) ||
              this.props.order.isCustomer) && this.props.order.invoicing === 'Freight Provider to Invoice Broker' &&
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Freight Rate In" className={this.classes.secondaryListItem} />
              <ListItemText primary={this.freightRateIn ? `${currency} ${this.freightRateIn}` : '-'} className={this.classes.primaryListItem} />
            </ListItem>}
            {(this.props.order.isFreightProvider ||
              (!this.props.order.isCustomer && !this.props.order.isCustomerRegistered) ||
              this.props.order.isCustomer) &&
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary={!this.props.order.isFreightProvider || this.props.order.isSelf ? "Freight Rate Out" : "Freight Rate"} className={this.classes.secondaryListItem} />
              <ListItemText primary={`${currency} ${this.freightRateOut}`} className={this.classes.primaryListItem} />
            </ListItem>}
          </List>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="subheading" className={this.classes.subTitle}>
            Amended
          </Typography>
          <List>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Tonnage" className={this.classes.secondaryListItem} />
              <NumberField
                id="amendedTonnage"
                label=""
                value={this.state.amendedTonnage.value}
                disabled={this.isAmendRequestPending}
                helperText={this.state.amendedTonnage.errors[0]}
                onChange={this.handleTextFieldChange}
                onBlur={this.handleBlur}
                maxValue={9999.99}
                lockIconClassName="side-drawer-lock-icon"
                InputProps={{
                  endAdornment: <InputAdornment position="end" style={{color: 'rgb(162,162,162)'}}>MT</InputAdornment>
                }}/>
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery Start Date" className={this.classes.secondaryListItem} />
              <CommonDatePicker
                id="amendedDeliveryStartDate"
                disabled={this.isAmendRequestPending}
                onChange={this.handleSelectFieldChange}
                errorText={this.state.amendedDeliveryStartDate.errors[0]}
                value={this.state.amendedDeliveryStartDate.value}
                style={{float: 'left'}}
                maxDate={this.maxPickupDate()}
                isInSideDrawer
                shouldRemoveLabel
              />
            </ListItem>
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Delivery End Date" className={this.classes.secondaryListItem} />
              <CommonDatePicker
                id="amendedDeliveryEndDate"
                disabled={this.isAmendRequestPending}
                onChange={this.handleSelectFieldChange}
                errorText={this.state.amendedDeliveryEndDate.errors[0]}
                value={this.state.amendedDeliveryEndDate.value}
                style={{float: 'left'}}
                minDate={this.minDeliveryDate()}
                isInSideDrawer
                shouldRemoveLabel
              />
            </ListItem>
            {((!this.props.order.isFreightProvider && !this.props.order.isCustomer && !this.props.order.isCustomerRegistered) || this.props.order.isCustomer) &&
            this.props.order.invoicing === 'Freight Provider to Invoice Broker' &&
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary="Freight Rate In" className={this.classes.secondaryListItem} />
              <CommonTextField
                id="amendedFreightRateIn"
                label=""
                helperText={this.state.amendedFreightRateIn.errors[0]}
                disabled={this.isAmendRequestPending}
                value={this.state.amendedFreightRateIn.value}
                onChange={this.handleFreightRateChange}
                onBlur={this.handleBlur}
                onKeyDown={(event)=>positiveDecimalFilter(event, 2,9999.99)}
                lockIconClassName="side-drawer-lock-icon"
                InputProps={{
                  startAdornment: <InputAdornment position="start" style={{color: 'rgb(162,162,162)'}}>$</InputAdornment>
                }}/>
            </ListItem>}
            {(this.props.order.isCustomer || (!this.props.order.isCustomer && !this.props.order.isCustomerRegistered) || this.props.order.isFreightProvider) &&
            <ListItem className={this.classes.listItem}>
              <ListItemText secondary={!this.props.order.isFreightProvider || this.props.order.isSelf ? "Freight Rate Out" : "Freight Rate"} className={this.classes.secondaryListItem} />
              <CommonTextField
                id="amendedFreightRateOut"
                label=""
                helperText={this.state.amendedFreightRateOut.errors[0]}
                disabled={this.isAmendRequestPending}
                value={this.state.amendedFreightRateOut.value}
                onChange={this.handleFreightRateChange}
                onBlur={this.handleBlur}
                onKeyDown={(event)=>positiveDecimalFilter(event, 2,9999.99)}
                lockIconClassName="side-drawer-lock-icon"
                InputProps={{
                  startAdornment: <InputAdornment position="start" style={{color: 'rgb(162,162,162)'}}>$</InputAdornment>
                }}/>
            </ListItem>}
          </List>
        </Grid>

        <div className='button-container'>
          { this.amendable && this.isAmendRequestPending ?
            [
              <CommonButton key="reject" label="Reject" secondary variant="contained" onClick={() => this.handleRejectClickOpen()}/>,
              <CommonButton key="accept" label="Accept" primary variant="contained" onClick={this.confirmAmendRequestWrapper}/>,
            ] :
            !this.isAmendRequestPending ?
              [
                <CommonButton key="close" label="Close" secondary variant="contained" onClick={this.closeSidebar}/>,
                <CommonButton key="amend" label="Amend" primary variant="contained" onClick={this.validateData}/>
              ] :
              [
                <CommonButton key="close" label="Close" secondary variant="contained" onClick={this.closeSidebar}/>,
              ]
          }
        </div>

        {this.amendable &&
          <div className="status-actions-wrap">
            <RejectionReasonDialog
              open={this.state.rejectDialogOpen.value}
              onClose={this.handleRejectClose}
              title="Reject Amend Request"
              value={this.state.rejectionReason.value}
              onChange={this.handleReasonChange}
              helperText={get(this.state, 'rejectionReason.errors[0]', '')}
              onCancel={this.handleRejectClose}
              onReject={this.handleRejectSubmit}
            />
          </div>
        }
      </Grid>
    );
  }
}

const mapStateToProps = state => state;

const mapDispatchToProps = dispatch => ({
  raiseAmendRequest: (orderId, data) => dispatch(raiseOrderAmendRequest(orderId, data)),
  confirmAmendRequest: (orderId) => dispatch(confirmOrderAmend(orderId)),
  rejectAmendRequest: (orderId, data) => dispatch(rejectOrderAmend(orderId, data)),
  isLoading: (component) => dispatch(isLoading(component))
});

export default withStyles(styles)(connect(mapStateToProps, mapDispatchToProps)(AmendOrder));
