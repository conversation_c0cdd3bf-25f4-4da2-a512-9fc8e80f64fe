.contract-preview {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;

  & .padding-reset {
    padding-left: 0;
    padding-right: 0;
  }

  & .text-right {
    text-align: right;
    @media screen and (max-width: 768px){
      text-align: left;
    }
  }

  .contract-header {
    margin-bottom: 5px;
    border-bottom: 2px solid #8a8b8a;
    padding-bottom: 15px;

    .agri-logo {
      text-align: center;
      img {
        height: 70px;
      }
      h2 {
        font-size: 22px;
      }
    }

    h1 {
      font-size: 28px;
      padding: 40px 0;
      margin: 0;
      color: #000;
    }

    &--content {
      display: flex;
      align-items: flex-start;

      h4 {
        font-size: 20px;
        margin: 0;
      }
    }

    p {
      margin: 0;
      padding: 2px 0;
      color: #636463;
      font-size: 14px;

      span {
        width: 160px;
        font-size: 13px;
        text-transform: uppercase;
        font-weight: 500;
        color: #636463;
      }
    }
  }

  .contract-body {
    .description {
      font-size: 14px;
      padding: 10px 0 13px;
      margin: 0 0 20px;
      line-height: 1.6;
      color: #636463;
      border-bottom: 2px solid #8a8b8a;

      p {
        margin: 5px 0;
      }
    }

    .pre-label {
      display: inline-block;
      min-width: 140px;
      text-transform: uppercase;
      font-size: 13px;
      width: 140px;
    }
    .pre-content {
      display: inline-block;
      width: calc(100% - 140px);
      color: #636463;
      font-weight: 400;
      font-size: 15px;
    }
  }

  .contract-details {
    padding-bottom: 20px;
    display: flex;
    border-bottom: 2px solid #8a8b8a;

    &__left, &__right {
      padding: 0;

      h4 {
        font-size: 13px;
        margin: 0;
        color: #636463;
        font-weight: 500;
      }
      h5 {
        margin: 0 0 15px 0;
        font-size: 20px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      p {
        margin: 0;
        padding: 6px 0;
        color: #636463;
        display: flex;

        span {
          display: inline-block;
          font-weight: 500;
        }
      }
    }
    &__left{
      padding-right: 10px;
    }
  }
.margintop-20 {
  margin-top: 20px;
}
  .commodity-details, .delivery-details {
    display: inline-block;
    width: 100%;
    margin-top: 20px;
    p {
      font-size: 14px;
      color: #000;
      margin: 0;
      padding: 5px 0;

      span {
        color: rgba(0, 0, 0, 0.6);
        display: inline-block;
        width: 170px;
      }

      .pre-content {
        width: calc(100% - 190px);
      }
    }
  }

  .contract-conditions {
    border-bottom: 2px solid #8a8b8a;
    h4 {
      font-size: 20px;
      margin: 0;
    }
    p{
      color: #636463;
      font-size: 14px;
      white-space: pre-line;
      padding: 10px 0 20px;
    }
  }

  .delivery-details {
    p {
      display: flex;
    }
  }

  .contract-footer {
    padding: 10px 0;
    font-size: 11px;
    color: #636463;
  }
}