@import "../../../common/variables.scss";
.contract-status {
  display: flex;
  margin-bottom: 10px;
  .contract-status-bar {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    width: 74%;

    li {
      border-right: 1px solid $colorGrey;
      padding: 12px 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 18%;

      &:last-child {
        border-right: none;
      }

      &.status {
        text-align: center;
        line-height: 1.2;
      }

      &.status-tags {
        flex-direction: row;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding: 8px;
        width: 28%;
        border-right: 0;
      }
    }

    .field-label {
      color: $colorLightGrey;
      font-size: 1rem;
      font-weight: 300;
    }

    .field-value {
      font-size: 1.250rem;
      color: $colorBlack;
    }

    .created-at {
      text-align: center;
      color: $colorLightGrey;
      font-size: 12px;
      font-weight: 300;
      padding-top: 2px;
    }

    .field-tag {
      color: $colorBlack;
      font-size: 11px;
      padding: 2px 5px;
      min-width: 110px;
      border-radius: 4px;
      background: $colorGrey;
      margin:5px;
      text-align: center;
      display: block;
    }
  }

  & .status-actions {
      min-width: 22%;
      text-align: center;
  }

  & .status-actions-container {
      display: inline-block;
      flex-direction: column;
      height: 100%;
      justify-content: center;
      align-items: center;

      h4 {
        margin: 10px 0 5px 0;
        font-size: 1.10rem;
      }
      a {
        margin-bottom: 10px;
      }

      a:first-child {
        margin-right: 10px;
      }

      & .status-actions-wrap {
        display: flex;
        flex-direction: column;
        padding-bottom: 15px;
      }

    &.grey-bg {
      background: $colorLightestGrey;
    }
  }
}

.contract-details-container {
  .contract-details-section-container {   //CounterParties Section
    padding: 20px 30px;
    margin-bottom: 10px;
    color: #112c42;
    h2 {
      margin: 0;
      padding: 0;
      font-size: 20px;
      font-weight: 600;
      .expand-icon {
        width: auto;
        float: right;
        svg {
          vertical-align: middle;
        }
      }
    }

    .section-title {
      font-size: 13px;
      font-weight: 500;
      margin: 0;
      padding: 7px 0;
    }

    .section-details-container, .section-details-container-4, .section-details-container-2 {
      display: grid;
      grid-template-columns: 33% 33% 33%;
      margin-top: 10px;
      > div {
        border-right: 1px solid #e0e0e0;
        padding: 0 10px;
        &:last-child {
          border-right: 0;
        }
      }
      .section-details {
        margin: 25px 0;
      }
      ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
        li {
          padding: 7px 0;
          font-size: 13px;
          span {
            display: inline-block;
            &.field-label {
              width: 41%;
              font-weight: 500;
              color: #808080;
              padding-right:5px;
            }
            &.field-value {
              width: 59%;
              font-weight: normal;
              padding-left:5px;
            }
          }
        }
      }
    }
    .section-details-container-4 {
      grid-template-columns: 25% 25% 25% 25%;
    }
    .section-details-container-2 {
      grid-template-columns: 50% 50%;
    }
    .section-details-container-2 > div {
      border-right: none;
      padding: 0 10px;
    }
  }
}

.amend-section{
  padding-left: 0px;
}

.amend-section ul {
  padding: 0px;
  li {
    list-style-type: none;
  }
  .field-label {
    display: inline-block;
    width: 40%;
    font-weight: 500;
    color: #808080;
    font-size: 13px;
  }

  .field-value {
    display: inline-block;
    width: 60%;
    font-size: 13px;
  }

  .full-width-field-value {
    display: inline-block;
    width: 100%;
    font-size: 13px;
  }
}

.transfer-status {
  display: flex;
  margin-bottom: 10px;
  padding: 0 30px;
  &.new {
      margin-bottom: 8px;
      padding: 0;
  }
  .transfer-status-bar {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    width: 100%;
    li {
      border-right: 1px solid $colorGrey;
      min-width: 180px;
      padding: 12px 10px 25px;
      display: flex;
      align-items: center;
      justify-content: center;

      &.status {
        text-align: center;
        line-height: 1.2;
        padding-left: 0;
      }

      &.status-tags {
        flex-direction: row;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding: 8px;
        width: 28%;
        border-right: 0;
      }
    }
    &.new {
        li {
            padding: px;
            min-width: none;
        }
    }

    .field-label {
      color: $colorLightGrey;
      font-size: 1rem;
      font-weight: 300;
    }

    .field-value {
      font-size: 1.250rem;
      color: $colorBlack;
    }

    .created-at {
      color: $colorLightGrey;
      font-size: 12px;
      font-weight: 300;
      padding-top: 2px;
    }

    .field-tag {
      color: $colorBlack;
      font-size: 11px;
      padding: 2px 5px;
      min-width: 110px;
      border-radius: 4px;
      background: $colorGrey;
      margin:5px;
      text-align: center;
      display: block;
    }
  }

  & .status-actions {
    display: flex;
    align-items: center;
    width: 26%;
  }

  & .status-actions-container {
      display: inline-block;
      flex-direction: column;
      height: 100%;
      justify-content: center;
      align-items: center;

      h4 {
        margin: 10px 0 5px 0;
        font-size: 1.10rem;
      }
      a {
        margin-bottom: 10px;
      }

      a:first-child {
        margin-right: 10px;
      }

      & .status-actions-wrap {
        display: flex;
        flex-direction: column;
        padding-bottom: 15px;
      }

    &.grey-bg {
      background: $colorLightestGrey;
    }
  }
}
