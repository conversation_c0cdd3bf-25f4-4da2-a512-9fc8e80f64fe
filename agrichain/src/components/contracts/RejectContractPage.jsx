import React from 'react';
import CommonTextField from '../common/CommonTextField';
import CommonButton from '../common/CommonButton';
import './acceptRejectContract.scss';

const RejectContractPage = () => (
  <div className="full-screen-container">
    <div className="content-container">
      <div className="content-box">
        <div className="content-box--header">
          <img src="images/agrichain-logo.png" alt="Agri Chain Logo"/>
        </div>
        <div className="content-box--content">
          <h4 className="reject-title">Reject Contract</h4>
          <p>Please enter your reason for rejecting below.</p>
          <CommonTextField
            label=""
            placeholder=""
            id="rejectReason"
            multiline={true}
            className="reject-reason-field"
            InputProps={{
              disableUnderline: true,
            }}
          />
          <CommonButton
            variant="contained"
            label="ACCEPT"
            primary
          />
        </div>
      </div>
      <div className="content-box-link">See how <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> can empower your business <a>here</a>.</div>
    </div>
  </div>
);

export default RejectContractPage;