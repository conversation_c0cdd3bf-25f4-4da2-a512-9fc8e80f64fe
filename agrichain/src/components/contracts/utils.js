import { get, find, isArray, isEmpty, includes, cloneDeep } from 'lodash';
import APIService from '../../services/APIService';
import {
  CONTRACTS_TABLE_OPTIONS_ITEMS,
  DIRECT_TO_BUYER_ALLOCATION,
  STOCK_ALLOCATION,
  THROUGH_WAREHOUSE_ALLOCATION,
} from '../../common/constants';
import {
  isSystemCompany, getIndexOfSpliceItem, regeneratePDF, currentUserCompany, currentUser
} from '../../common/utils';

export const getActionMenuOptions = (contract, subItems, clickedOption, listing) => {
  let contractActionOptions = cloneDeep(CONTRACTS_TABLE_OPTIONS_ITEMS());
  if (window.NEW_ORDERS_VIEW_TOGGLE) {
    contractActionOptions.splice(1, 0, {
      key: 'freight_orders',
      text: 'Orders',
      subItems: [
        { key: 'freight_orders_add', text: 'Add' },
        { key: 'freight_orders_view', text: 'View' },
      ],
    })
  }
  else {
    contractActionOptions.splice(1, 0, ...[{
      key: 'freight_orders',
      text: 'Freight Orders',
      subItems: [
        { key: 'freight_orders_add', text: 'Add' },
        { key: 'freight_orders_view', text: 'View' },
      ],
    },
    {
      key: 'call_on_grain_orders',
      text: 'Call On Grain Orders',
      subItems: [
        { key: 'call_on_grain_orders_add', text: 'Add', sideDrawer: true },
        { key: 'call_on_grain_orders_view', text: 'View' },
      ],
    }])
  }

  const { editOrAmendLabel, isAmendRequestPending, canMarkPaid, canViewVendorDec } = contract;
  const vendorDecSpliceItem = {key: 'vendor_dec'};
  if (!canViewVendorDec){
    const vendorDecIndex = getIndexOfSpliceItem(contractActionOptions, vendorDecSpliceItem);
    contractActionOptions.splice(vendorDecIndex, 1);
  }
  let sendEmailItem = find(contractActionOptions, item => item.key === 'contract_send_email');
  if (sendEmailItem && contract?.lastEmailAction?.actionType && !contract?.lastEmailAction?.hasPendingEmail && currentUserCompany()?.enableResendingEmail)
    sendEmailItem['text'] = 'Resend Email';
  if (!window.SEND_EMAIL_LATER_TOGGLE){
    const sendEmailSpliceItem = {key: 'contract_send_email'};
    const sendEmailDecIndex = getIndexOfSpliceItem(contractActionOptions, sendEmailSpliceItem);
    contractActionOptions.splice(sendEmailDecIndex, 1);
  }

  if (editOrAmendLabel) {
    const amendIndex = getIndexOfSpliceItem(contractActionOptions, {key: 'amend'});
    contractActionOptions[amendIndex].text = editOrAmendLabel;
  }

  if (isSystemCompany()){
    contractActionOptions.push({key: 'invoiced_outside', text: 'Invoiced Outside'});
  }

  if (isAmendRequestPending)
    contractActionOptions.push({key: 'review_amendment', text: 'Review Amendment'});

  if (canMarkPaid)
    contractActionOptions.push({key: 'mark_as_paid', text: 'Mark as Paid'});

  if(isSystemCompany())
    contractActionOptions.push({key: 'regenerate_pdf', text: 'Regenerate PDF'});

  const isSellerPartySigned = currentUser()?.id == contract.seller?.contactId && contract?.isCreatedByBuyer && !contract?.showSellerSignature
  const isBuyerPartySigned =  currentUser()?.id == contract.buyer?.contactId && contract?.isCreatedBySeller && !contract?.showBuyerContract
  if (contract?.sellerSignature && contract.buyerSignature && (isSellerPartySigned || isBuyerPartySigned))
    contractActionOptions.push({key: 'add_signature', text: 'Add Signature to Contract'});

  contractActionOptions.push({key: 'show_hierarchy', text: 'Show Hierarchy'});
  if (!listing && [DIRECT_TO_BUYER_ALLOCATION, THROUGH_WAREHOUSE_ALLOCATION].some(allocationType => includes(currentUserCompany().contractAllocations, allocationType)))
    contractActionOptions.push({key: 'contract_allocation', text: 'Contract Allocation'});
  if(!listing && includes(currentUserCompany().contractAllocations, STOCK_ALLOCATION) && currentUserCompany().id === get(contract, 'seller.companyId'))
    contractActionOptions.push({key: 'stock_allocation', text: 'Stock Allocation'});

  const option = find(contractActionOptions, {key: get(clickedOption, 'key')});
  if(option && isArray(subItems) && !isEmpty(subItems))
    option.subItems = subItems;

  return contractActionOptions;
};

export const regenerateContractPDF = contract => {
  const contractId = get(contract, 'id');
  if(contractId)
    return regeneratePDF(APIService.contracts(contractId).appendToUrl('regenerate-pdfs/'));
};
