import React from 'react';

import Paper from '@mui/material/Paper';
import Help from '@mui/icons-material/Help';
import {  upperFirst } from 'lodash';

import TitleTransferDetailsActions from './TitleTransferDetailsActions';
import Contract from '../contracts/Contract'
import TitleTransfer from './TitleTransfer'

const renderBasedOnStatus = status => {
    switch (status){
      case 'planned':
      case 'confirmed':
        return (
          <img src="images/confirmed.svg"></img>
        );
      case'open':
        return (
          <img src="images/open.svg"></img>
        );
      case 'void':
        return (
          <img src="images/voided.svg"></img>
        );
      case 'completed':
        return (
          <img src="images/completed.svg"></img>
        );
      case 'rejected':
        return (
          <img src="images/rejected.svg"></img>
        );
      case 'invoiced':
        return (
          <img src="images/invoiced.svg"></img>
        );
      case 'draft':
        return (
          <img src="images/draft.svg"></img>
        );
    }
    return <Help style={{ fill: '#eee200', height: '40px', width: '40px' }} />;
};

export const TitleTransferStatusBar = props => {
  const { selectedTitleTransfer } = props;
  const statusDisplayName =  upperFirst(selectedTitleTransfer?.statusDisplayName);
  const commodityContract = selectedTitleTransfer?.commodityContract;

  return (
    <Paper className='transfer-status new' id="transfer-details-basic-info-card">
      <ul className="transfer-status-bar new">
        <li className="status flex-direction-column new">
            <span style={{display: 'flex', alignItems: 'center'}}>
            {renderBasedOnStatus(selectedTitleTransfer.status)}
            <span className="field-value" style={{marginLeft: '8px'}}>{statusDisplayName}</span>
            </span>
        </li>
        {
            selectedTitleTransfer &&
            <TitleTransfer titleTransfer={selectedTitleTransfer} noLink />
        }
        {
          (commodityContract || selectedTitleTransfer?.contractNumber) &&
          <Contract
          contract={{
            identifier: commodityContract ? (commodityContract?.name || commodityContract?.referenceNumber) : selectedTitleTransfer?.contractNumber,
            id: commodityContract?.id,
            createdAt: commodityContract ? commodityContract?.createdAt : selectedTitleTransfer?.createdAt
          }}
          noLink={!commodityContract}
          />
        }
    </ul>
    <div className="status-actions">
    <TitleTransferDetailsActions {...props} />
    </div>
    </Paper>
  );
};

export default TitleTransferStatusBar;
