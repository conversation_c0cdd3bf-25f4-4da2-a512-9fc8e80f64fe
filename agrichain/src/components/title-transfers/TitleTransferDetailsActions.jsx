import React from 'react';
import { connect } from 'react-redux';
import APIService from '../../services/APIService';
import alertifyjs from 'alertifyjs';
import { required } from '../../common/validators';
import { get, has, keys, without, isEmpty, uniq, compact, pick, values, map, forEach, filter, find, cloneDeep } from 'lodash';
import { RejectionReasonDialog } from '../rejections/RejectionReasonDialog';
import { isLoading } from '../../actions/main';
import {
  canVoidTitleTransfer, receiveTitleTransfer,
  getSelectedTitleTransfer,
  downloadTitleTransferPDF,
  duplicateTitleTransfer,
  setClickedTitleTransferOption,
  showViewTitleTransferSideDrawer,
  setSelectedTitleTransfer,
  showAddTitleTransferSideDrawer,
  updateDuplicateTitleTransferId,
  voidTitleTransferDialog,
  voidAndDuplicateTitleTransfer,
  showTitleTransferEmailDialog,
} from '../../actions/companies/contracts';
import { getSendEmailActionTitle, formatSendEmailAction, currentUserCompany } from '../../common/utils';
import { TITLE_TRANSFER_OPTIONS } from '../../common/constants';
import NestedOptionMenu from "../NestedOptionMenu";
import SideDrawer from '../common/SideDrawer';
import TitleTransferActions from './TitleTransferActions';
import TitleTransferAmendForm from './TitleTransferAmendForm';
import CreateTitleTransfer from '../../containers/CreateTitleTransfer';
import CustomEmailDialog from '../common/CustomEmailDialog';


class TitleTransferDetailsActions extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      requestReason: {
        value: '',
        validators: [required()],
        errors: []
      },
      isSendEmail: false,
      emailPopupParties: [],
      showEmailDialog: false
    };
  }

  handleOptionClick = (event, item, baseEntity) => this.props.handleOptionClick(item?.key, baseEntity?.id, baseEntity);

  closeSideDraw = (reload=false) => {
    this.props.showAddTitleTransferSideDrawer(false);
    this.props.showViewTitleTransferSideDrawer(false);
    this.props.showHideTitleTransferSideDrawer(false);
    this.props.updateDuplicateTitleTransferId(null)
    if (reload)
      window.location.reload();
  }

  componentDidMount() {
    setTimeout(this.setActionButtonBoundaries, 100);
  }

  componentDidUpdate(prevProp) {
    if (get(prevProp, 'showEmailDialog') !== this.props.showEmailDialog && this.props.showEmailDialog && this.props.selectedTitleTransfer) 
      this.setState({ isSendEmail: true, showEmailDialog: true, emailPopupParties: this.getEmailPopupParties() });
  }

  setActionButtonBoundaries() {
    const statusBarEl = document.getElementById('transfer-details-basic-info-card');
    if (statusBarEl) {
      const top = statusBarEl.offsetTop;
      const height = statusBarEl.offsetHeight;
      if (top && height) {
        const actionsContainer = document.getElementById('nested-actions-menu-container');
        if (actionsContainer) {
          actionsContainer.style.top = (top - 1) + 'px';
          actionsContainer.style.height = (height + 1) + 'px';
        }
      }
    }
  }
  openEmailDialog = data => {
    this.setState({ showEmailDialog: true, emailPopupParties: this.getEmailPopupParties() });
    this.payloadData = data;
  };

  handleVoidClick = () => {
    this.setReasonErrors();
    if (this.state.requestReason.errors.length === 0) {
      if (this.props.showVoidDialog) {
        const data = { rejection_reason: this.state.requestReason.value };
        if(this.props.currentUser.company.showEmailPopup === false && !this.isLaterDisabled()) {
          data['communication']= { 'acceptanceRequired': false }
          this.processVoidRequest(data);
        } else {
          this.openEmailDialog(data);
        }
      }
    }
  };

  handleRequestReasonChange = (event) => {
    const value = event.target.value;
    const newState = { ...this.state };
    newState.requestReason.value = value;
    this.setState(newState);
  };

  closeRejectionReasonDialog = () => {
    const newState = { ...this.state };
    newState.requestReason.value = '';
    this.setState(newState);
    this.props.voidTitleTransferDialog(false, [])
  }

  getReasonErrors() {
    const errors = [];
    const value = get(this.state, `requestReason.value`);
    const validators = get(this.state, `requestReason.validators`, []);
    validators.forEach((validator) => {
      if (validator.isInvalid(value))
        errors.push(validator.message);
    });
    return errors;
  }

  setReasonErrors(errors) {
    const newState = { ...this.state };
    newState.requestReason.errors = errors || this.getReasonErrors();
    this.setState({requestReason: { ...this.state.requestReason, errors: errors || this.getReasonErrors() }});
  }

  processVoidRequest = data => {
    this.props.isLoading('alertify');
    APIService.contracts().appendToUrl(`title-transfers/${this.props.titleTransferId}/void/`)
      .put({ data }, this.props.userToken)
      .then(item => {
        if (has(item, 'errors')) {
          alertifyjs.error(item.errors[0]);
        } else {
          alertifyjs.success('Title Transfer is voided', 1, () => {
            if(this.props.copyFrom)
              this.props.showAddTitleTransferSideDrawer(true)
            else
              window.location.reload();
          });
        }
        this.props.voidTitleTransferDialog(false, []);
      });
  }

  getFooterNote = () => 'Title Transfer PDF will be automatically sent as part of the email';

  getEmailSubject = () => {
    const companyName = get(this.props.currentUser, 'company.name', "");
    const titleTransfer = this.props.selectedTitleTransfer;
    let actionType = this.state.isSendEmail ? get(titleTransfer, 'lastEmailAction.actionType') : 'Void'
    actionType = formatSendEmailAction(actionType);
    let displaySubject = actionType? `[${actionType}] `: ''
    displaySubject += `${companyName} Title Transfer #${titleTransfer?.identifier}`;
    return displaySubject
  };

  getEmailPopupParties = () => {
    return ['buyer', 'seller', 'transferSite']
  };

  getEmailPopupPartiesCompanyIds() {
    const parties = this.getEmailPopupParties();
    const ids = {};
    const titleTransfer = this.props.selectedTitleTransfer;
    forEach(parties, party => {
      if (party === 'buyer')
        ids.buyer = get(titleTransfer, 'buyer.companyId') || get(titleTransfer, 'buyerCompanyId');
      if (party === 'seller')
        ids.seller = get(titleTransfer, 'seller.companyId')|| get(titleTransfer, 'sellerCompanyId');
      if (party === 'delivery site' || party == 'transferSite')
        ids.transferSite = get(titleTransfer, 'transferSite.companyId') || get(titleTransfer, 'transferSiteCompanyId') || get(titleTransfer, 'site.companyId');
    });

    return ids;
  }

  async getPartyContacts() {
    if (this.gotOncePartyContacts)
      return;

    this.gotOncePartyContacts = true;
    const parties = this.getEmailPopupPartiesCompanyIds();
    const partiesWithoutContacts = without(keys(parties));
    const contacts = {};
    if (!isEmpty(partiesWithoutContacts)) {
      const companyIds = uniq(compact(values(pick(parties, partiesWithoutContacts))));
      if (isEmpty(companyIds))
        return contacts;
      const companyQueryString = map(companyIds, id => `company_ids=${id}`).join('&');
      const employees = await APIService.profiles().appendToUrl(`employees-signature/?${companyQueryString}`).get(this.props.token);
      forEach(partiesWithoutContacts, party => {
        contacts[party] = filter(employees, { companyId: parties[party] });
      });
    }
    return contacts;
  }

  getPartyEmails = () => {
    const titleTransfer = this.props.selectedTitleTransfer;
    return {
      buyer: get(titleTransfer, 'buyerContact.email', '') || get(titleTransfer, 'buyerEmail', ''),
      seller: get(titleTransfer, 'sellerContact.email', '') || get(titleTransfer, 'sellerEmail', ''),
    };
  };

  closeEmailDialog = async (communicationData, justClose) => {
    if (justClose) {
      this.gotOncePartyContacts = false;
      this.setState({ showEmailDialog: false, isSendEmail: false });
      this.props.showTitleTransferEmailDialog(false)
    }
    else if(this.state.isSendEmail){
      try {
          const emailData = {
              recipients: communicationData.recipients,
              subject: communicationData.subject || `Title Transfer`,
              acceptanceRequired: communicationData.acceptanceRequired,
              request_reason: communicationData.request_reason
          };
          const titleTransfer = this.props.selectedTitleTransfer;
          const actionType = get(titleTransfer, 'lastEmailAction.actionType')
          if (actionType) {
            let isResend = !get(titleTransfer, 'lastEmailAction.hasPendingEmail') && currentUserCompany()?.enableResendingEmail
            let appendUrl = `title-transfers/${titleTransfer.id}/send-email/${actionType.toLowerCase()}/`
            if (isResend)
              appendUrl += '?resend=true'
            await APIService.contracts()
              .appendToUrl(appendUrl)
              .post(emailData, this.props.token)
              .then(response => {
                if(!response || response.error)
                  alertifyjs.error('An Error Occurred!')
                else if(response && response.message){
                  alertifyjs.success('Email Sent Successfully!')
                  this.props.showTitleTransferEmailDialog(false)
                  this.setState({isSendEmail: false})
                  setTimeout(() => {
                    window.location.reload()
                  }, 500);
                }
              })
            this.setState({isSendEmail: false})
          }
        } catch (error) {
          alertifyjs.error('An Error Occurred!')
          console.error('Error sending email:', error);
        }
    }
    else if (this.state.showEmailDialog && !this.state.isSendEmail) {
      const data = this.payloadData;
      if (communicationData) {
        data['communication'] = communicationData;
      }
      this.setState({ showEmailDialog: false}, () => {
        this.props.showTitleTransferEmailDialog(false)
        this.processVoidRequest(data)})
    }
  };

  getTitle = () => {
    const titleTransfer = this.props.selectedTitleTransfer;
    return this.state.isSendEmail? getSendEmailActionTitle(titleTransfer, 'Title Transfer') : null;
  }

  getDisabledPartiesForEmail = () => {
    const entity = this.props.selectedTitleTransfer
    return entity?.isStocksManagement ? ['buyer', 'transferSite'] : []
  };

  isLaterDisabled = () => {
    const titleTransfer = this.props.selectedTitleTransfer;
    let disabledParties = this.getDisabledPartiesForEmail(titleTransfer);
    return disabledParties.length > 0
  }

  getOptions = () => {
    const titleTransfer = this.props.selectedTitleTransfer;
    let options = cloneDeep(TITLE_TRANSFER_OPTIONS())
    
    let sendEmailItem = find(options, item => item.key === 'title_transfer_send_email');
    if (sendEmailItem && titleTransfer?.lastEmailAction?.actionType && !titleTransfer?.lastEmailAction?.hasPendingEmail && currentUserCompany()?.enableResendingEmail)
      sendEmailItem['text'] = 'Resend Email';
    return options
  }

  render() {
    return (
      <div className='status-actions-container'>
       <TitleTransferActions {...this.props}  />
        {
          this.props.showVoidDialog &&
            <div className='status-actions-wrap'>
              <RejectionReasonDialog
                open={this.props.showVoidDialog}
                onClose={this.closeRejectionReasonDialog}
                title={'Void Title Transfer'}
                value={this.state.requestReason.value}
                onChange={this.handleRequestReasonChange}
                helperText={get(this.state, 'requestReason.errors[0]', '')}
                onCancel={this.closeRejectionReasonDialog}
                onReject={this.handleVoidClick}
              />
            </div>
        }
        {
          this.props.selectedTitleTransfer &&
          <div className='status-actions-wrap'>
            <NestedOptionMenu
              optionsItems={this.getOptions}
              item={this.props.selectedTitleTransfer}
              handleOptionClick={this.handleOptionClick}
              currentUser={this.props.currentUser}
              shouldOptionBeDisabled={this.props.shouldOptionBeDisabled}
              useButton={true}
              useIconButton={false}
              buttonContainerStyle={{float: 'none', marginLeft: 'none'}}
            />
          </div>
        }
        {
            (this.props.isAddTitleTransferSideDrawerOpened || this.props.isAddTitleTransferSideDrawerToBeOpened) &&
            <SideDrawer
              isOpen={this.props.isAddTitleTransferSideDrawerOpened || this.props.isAddTitleTransferSideDrawerToBeOpened}
              title="Add Title Transfer"
              onClose={this.closeSideDraw}
              size='xlarge'>
              <CreateTitleTransfer
                closeDrawer={this.closeSideDraw}
                contract={this.props.contract}
                copyFrom={this.props.copyFrom}
              />
            </SideDrawer>
          }
        {
          this.props.isViewTitleTransferSideDrawerOpened && this.props.selectedTitleTransfer &&
          <SideDrawer
            isOpen={this.props.isViewTitleTransferSideDrawerOpened}
            onClose={this.closeSideDraw}
            title={`Amend Title Transfer (${this.props.selectedTitleTransfer?.identifier})`}
            classes={{ paper: 'left-text-align' }}
            size='big'
            >
            <TitleTransferAmendForm onClose={this.closeSideDraw}/>
          </SideDrawer>
        }
        {this.state.showEmailDialog &&
           <CustomEmailDialog
             parties={this.state.emailPopupParties}
             selectedParties={this.isLaterDisabled() ? ['buyer', 'seller', 'transferSite'] : ['buyer', 'seller']}
             title={this.getTitle() || "Email PDF copies to"}
             partyEmails={this.getPartyEmails()}
             partyContacts={this.getPartyContacts()}
             subject={this.getEmailSubject()}
             noBody={true}
             footer={this.getFooterNote()}
             open={this.state.showEmailDialog}
             onClose={this.closeEmailDialog}
             disableAcceptanceRequired={true}
             disabledPartiesForEmail={this.getDisabledPartiesForEmail()}
             disableLater={this.isLaterDisabled()}
             isSendEmail={this.state.isSendEmail}
           />
          }
      </div>
    );
  }
}

const mapStateToProps = state => {
  return {
    clickedOption: state.companies.contracts.clickedTitleTransferOption,
    userToken: state.main.user.token,
    currentUser: state.main.user.user,
    selectedTitleTransfer: state.companies.contracts.selectedTitleTransfer,
    isViewTitleTransferSideDrawerOpened: state.companies.contracts.isViewTitleTransferSideDrawerOpened,
    showVoidDialog: state.companies.contracts.showVoidDialog,
    titleTransferId: state.companies.contracts.titleTransferId,
    copyFrom: state.companies.contracts.copyFrom,
    isAddTitleTransferSideDrawerOpened: state.companies.contracts.isAddTitleTransferSideDrawerOpened,
    isAddTitleTransferSideDrawerToBeOpened: state.companies.contracts.isAddTitleTransferSideDrawerToBeOpened,
    showEmailDialog: state.companies.contracts.showTitleTransferEmailDialog,
  };
};
const mapDispatchToProps = dispatch => {
  return {
    handleOptionClick: (key, id, item) => {
      dispatch(setClickedTitleTransferOption(key));
      if (key === 'amend')
        dispatch(showViewTitleTransferSideDrawer(true, item));
      else if (key === 'void')
        dispatch(canVoidTitleTransfer(item));
      else if(key == 'duplicate')
        dispatch(duplicateTitleTransfer(item, id))
      else if(key == 'void_and_duplicate')
        dispatch(voidAndDuplicateTitleTransfer(item))
      else if(key == 'download_pdf')
        dispatch(downloadTitleTransferPDF(item));
      else if (key == 'assign_to')
        dispatch(getSelectedTitleTransfer(item.id, setSelectedTitleTransfer, false, item.priceUnit, false))
      else if(key == 'title_transfer_send_email'){
        let hasPendingEmail = get(item, 'lastEmailAction.hasPendingEmail', false);
        if(hasPendingEmail || (!hasPendingEmail && currentUserCompany()?.enableResendingEmail)){
          dispatch(setSelectedTitleTransfer(item))
          dispatch(receiveTitleTransfer(item))
          dispatch(showTitleTransferEmailDialog(true))
        }
        else {
          alertifyjs.alert(
            'Email Communication',
            'Looks like emails for this transaction are already sent. You can always view the history of communication in the Audit History Tab.'
          )
        }
      }
    },
    showViewTitleTransferSideDrawer: flag => dispatch(showViewTitleTransferSideDrawer(flag)),
    showAddTitleTransferSideDrawer: flag => dispatch(showAddTitleTransferSideDrawer(flag)),
    showHideTitleTransferSideDrawer: flag => dispatch(showAddTitleTransferSideDrawer(flag)),
    showTitleTransferEmailDialog: flag => dispatch(showTitleTransferEmailDialog(flag)),
    updateDuplicateTitleTransferId: id => dispatch(updateDuplicateTitleTransferId(id)),
    voidTitleTransferDialog: (flag, item) => dispatch(voidTitleTransferDialog(flag, item)),
    isLoading: (loader) => dispatch(isLoading(loader)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(TitleTransferDetailsActions);