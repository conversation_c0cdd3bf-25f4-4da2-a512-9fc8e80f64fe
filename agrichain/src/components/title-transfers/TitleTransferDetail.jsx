import React from 'react';
import {connect} from 'react-redux';

import Paper from '@mui/material/Paper';
import KeyboardArrowDown from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUp from '@mui/icons-material/KeyboardArrowUp';
import {COMMODITIES, EMPTY_VALUE, PRIMARY_COLOR_GREEN, UNKNOWN_COMMODITY_ID, UNKNOWN_GRADE_ID} from '../../common/constants';
import moment from 'moment';
import Tooltip from '../../common/Tooltip';
import {getGradeName, getCountryFormats, getCountryLabel, getCountryCurrency, currentUserCompany} from '../../common/utils';
import {get, isEqual, isEmpty} from "lodash";
import { IconButton } from '@mui/material';
import UpdateLocation from '../locations/UpdateLocation';
import PinDropIcon from '@mui/icons-material/PinDrop';

const renderBasedOnExpandedFlag = expanded => {
  if (expanded) {
    return (
      <KeyboardArrowUp
        style={{ fill: '#112c42', height: '20px', width: '20px' }}
      />
    );
  }
  return (
    <KeyboardArrowDown
      style={{ fill: '#112c42', height: '20px', width: '20px' }}
    />
  );
};

class TitleTransferDetail extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      expanded: true,
      openSiteLocationForm: false,
    };
    this.getContractDetailsDataAsPerGrid = this.getContractDetailsDataAsPerGrid.bind(this);
  }

  toggleExpanded = () => this.setState(prevState => ({ expanded: !prevState.expanded}));

  toggleBookingDialog = (key, value) => this.props.toggleBookingPopup(key, value);

  getContractDetailsDataAsPerGrid = dataLists => {
    return (
      <div>
        <ul className="field-label-list">
          {Object.keys(dataLists).map(function(key, index) {
             return (
               <li key={index}>
                 {<Tooltip
                   className="field-label ellipses"
                   tooltipText={key}
                   textContent={key}
                 />}
                 <Tooltip
                   className="field-value"
                   tooltipText={dataLists[key] || EMPTY_VALUE}
                   textContent={dataLists[key] || EMPTY_VALUE}
                 />
               </li>
             );
           })}
        </ul>
      </div>
    );
  };

  getSiteNameWithLocation = siteName => {
    if (!siteName || siteName === EMPTY_VALUE)
      return siteName;
    return <div style={{ whiteSpace: 'normal' }}>
      <span><>{siteName}</>
      <IconButton 
        onClick={() => this.setState({openSiteLocationForm: true})}
        style={{paddingLeft: '0px'}}
        size="small">
          <PinDropIcon style={{ color: PRIMARY_COLOR_GREEN }} />
      </IconButton>
      </span>
    </div>
  }

  getCommoditySection = () => {
    const { selectedTitleTransfer } = this.props;
    const tonnageLabel = getCountryLabel('tonnage')
    let payload = {
      [tonnageLabel]: get(selectedTitleTransfer, 'tonnageDisplayValue', EMPTY_VALUE),
      'Commodity': (isEqual(selectedTitleTransfer?.commodityId, UNKNOWN_COMMODITY_ID) ? EMPTY_VALUE : get(selectedTitleTransfer, 'commodityName', EMPTY_VALUE)),
      'Variety': get(selectedTitleTransfer, 'varietyName', EMPTY_VALUE),
      'Grade': (isEqual(selectedTitleTransfer?.gradeId, UNKNOWN_GRADE_ID) ? EMPTY_VALUE : getGradeName(selectedTitleTransfer) || EMPTY_VALUE),
      'Season': get(selectedTitleTransfer, 'season', EMPTY_VALUE),
    };
    if (selectedTitleTransfer?.commodityId == COMMODITIES.CANOLA && isEmpty(selectedTitleTransfer?.canolaLoadIds)) {
      payload['COIL'] = get(selectedTitleTransfer, 'coil', EMPTY_VALUE)
      payload['IMPU'] = get(selectedTitleTransfer, 'impu', EMPTY_VALUE)
    };
    return payload;
  };

  getCommodityUnit() {
    return get(find(this.props.commodities, {id: get(this.props.selectedTitleTransfer, 'commodityId')}), 'unit',  this.props.currentUser?.unit)
  }

  getGeneralSection = () => {
    const { selectedTitleTransfer } = this.props;
    const section = {
      'Site': this.getSiteNameWithLocation(get(selectedTitleTransfer, 'siteDisplayName', EMPTY_VALUE)),
      'Transfer Date': selectedTitleTransfer?.processOn ? moment(selectedTitleTransfer.processOn).format(getCountryFormats().date) : EMPTY_VALUE,
    };
    if (get(currentUserCompany(), 'showThroughputLoad')){
      section['Is Throughput Load'] = selectedTitleTransfer?.throughput ? 'Yes' : 'No';
    }
    return section
  }

  getInvoiceDetailsSection = () => {
    const { selectedTitleTransfer } = this.props;
    let payload = {}
    const commodityUnit = this.getCommodityUnit()

    if (get(selectedTitleTransfer, 'pricePerMt'))
      payload[`Price Per ${commodityUnit}`] = get(selectedTitleTransfer, 'pricePerMt');
    if (selectedTitleTransfer?.paymentTerm)
      payload['Payment Term'] = get(selectedTitleTransfer, 'paymentTerm', EMPTY_VALUE);
    if (get(selectedTitleTransfer, 'referenceNumber'))
      payload['Reference Number'] = get(selectedTitleTransfer, 'referenceNumber', EMPTY_VALUE);
    if (get(selectedTitleTransfer, 'saleValue', 0) > 0)
      payload[`Sale Value (${getCountryCurrency()})`] = get(selectedTitleTransfer, 'saleValue');
    return payload;
  }

  render() {
    const { expanded} = this.state;
    const { selectedTitleTransfer } = this.props;
    return (
      <Paper className="contract-details-section-container">
        <h2 onClick={this.toggleExpanded}>
          Title Transfer Details
          <span className="expand-icon">
            {renderBasedOnExpandedFlag(expanded)}
          </span>
        </h2>
        {
          expanded &&
          <div className="section-details-container">
            { this.getContractDetailsDataAsPerGrid(this.getCommoditySection()) }
            { this.getContractDetailsDataAsPerGrid(this.getGeneralSection()) }
            { this.getContractDetailsDataAsPerGrid(this.getInvoiceDetailsSection()) }
          </div>
        }
        {this.state.openSiteLocationForm && get(selectedTitleTransfer, 'site.id') &&
          <UpdateLocation updateEntities={() => window.location.reload()} entityId={get(selectedTitleTransfer, 'site.id')} entity='farm' onCloseDrawer={() => this.setState({openSiteLocationForm: false})}/>
        }
      </Paper>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    currentUser: state.main.user.user,
    commodities: state.master.commodities.items,
  };
};

export default connect(mapStateToProps)(TitleTransferDetail);
