import React from 'react';
import { connect } from 'react-redux';
import APIService from '../../services/APIService';
import get from 'lodash/get';
import { getLoadReferences, getSpecColumns, loadReferencesDisplay } from '../stocks/utils';
import GenericTable from '../GenericTable';
import Paper from '@mui/material/Paper';

class TitleTransferLoads extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
        isLoadingLoads: false,
        load: []
    };
  }

  fetchLoads = () => {
    const { selectedTitleTransfer } = this.props;
    if(selectedTitleTransfer) {
      this.setState(
        {isLoadingLoads: true, loads: []},
        () => APIService.contracts().appendToUrl(`title-transfer/${selectedTitleTransfer.id}/loads/`).get().then(loads => this.setState({isLoadingLoads: false, loads: loads}))
      );
    }
  };

  componentDidMount() {
    this.fetchLoads();
  }

  getColumns = commodity => {
    const { selectedTitleTransfer } = this.props;
    const specColumns = commodity ? getSpecColumns(commodity, 'specs') : [];
    return [
    {header: 'Reference(s)', formatter: loadReferencesDisplay, default: getLoadReferences},
    { key: 'season', header: 'Season' },
    { key: 'varietyName', header: 'Variety' },
    { key: 'gradeName', header: 'Grade' },
    ...specColumns,
    { key: 'remainingTonnage', header: 'Remaining Tonnage' },
    {
        header: 'Transfer Tonnage',
        formatter: load => <span>
                            {
                            parseFloat(parseFloat(get(selectedTitleTransfer.canolaLoadIds, load.id) || 0).toFixed(2))
                            }
                        </span>
    },
    ];
  };

  render() {
    const { loads } = this.state;
    return (
        <div>
          <Paper className="paper-table-paginated">
            <GenericTable
                items={loads}
                columns={this.getColumns(get(loads, '0.commodity'))}
                optionsItems={[]}
                displayIDColumn='season'
            />
          </Paper>
        </div>
    );
  }
}

const mapStateToProps = state => {
  return {
    breadcrumbs: state.main.breadcrumbs,
    selectedTitleTransfer: state.companies.contracts.selectedTitleTransfer,
  };
};
export default connect(mapStateToProps)(TitleTransferLoads);
