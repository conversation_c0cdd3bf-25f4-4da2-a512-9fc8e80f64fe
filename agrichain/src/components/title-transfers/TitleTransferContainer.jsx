import React from 'react';

import { connect } from 'react-redux';
import { getSelectedTitleTransfer, receiveTitleTransfer } from '../../actions/companies/contracts';
import isEmpty from 'lodash/isEmpty';
import { isLoading, loaded } from '../../actions/main';
import TitleTransferDetails from './TitleTransferDetails';

class TitleTransferContainer extends React.Component {

  componentDidMount() {
    this.props.waitForLoader('transferDetail');
    if (isEmpty(this.props.selectedTitleTransfer)) {
      const { title_transfer_id } = this.props.match.params;
      this.props.getSelectedTitleTransfer(title_transfer_id, receiveTitleTransfer, true, false, false, true, false);
    }
    if(this.props.breadcrumbsFunc) {
      this.props.breadcrumbsFunc();
    }
  }

  componentWillUnmount() {
    this.props.receiveTitleTransfer(null);
  }

  componentDidUpdate(){
    this.props.loaded();
    const {title_transfer_id} = this.props.match.params;
    if(this.props.selectedTitleTransfer && this.props.selectedTitleTransfer.id != title_transfer_id){
      this.props.getSelectedTitleTransfer(title_transfer_id, receiveTitleTransfer, true, false, false, true, false);
    }
  }

  render(){
    return <TitleTransferDetails {...this.props}/>;
  }
}

const mapStateToProps = state => {
  return {
    selectedTitleTransfer: state.companies.contracts.selectedTitleTransfer || null,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    getSelectedTitleTransfer: (id, actionCreator, stopLoader, unit) => dispatch(getSelectedTitleTransfer(id, actionCreator, stopLoader, unit, false)),
    waitForLoader: () => dispatch(isLoading('transferDetail')),
    loading: () => dispatch(isLoading('unknown')),
    loaded: () => dispatch(loaded()),
    receiveTitleTransfer: (item) => dispatch(receiveTitleTransfer(item)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(TitleTransferContainer);
