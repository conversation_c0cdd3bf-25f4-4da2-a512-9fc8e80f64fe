import React from 'react';

import { connect } from 'react-redux';
import { required } from '../../common/validators';
import TitleTransfersTable from '../../containers/TitleTransfersTable';
import {
  getTitleTransfers,
  canCreateTitleTransfer, showHideTitleTransferSideDrawer,
  showViewTitleTransferSideDrawer, voidTitleTransferDialog, titleTransfersResponse, receiveTitleTransfer,
  showAddTitleTransferSideDrawer,
  updateDuplicateTitleTransferId,
  showTitleTransferEmailDialog, getSelectedTitleTransfer
} from '../../actions/companies/contracts';
import { setHeaderText, setSubHeaderText, setBreadcrumbs, isLoading, forceStopLoader, isSearchApplied } from '../../actions/main';
import { getContractSubHeaderText, attachCSVEventListener, isAtGlobalTitleTransfer, defaultViewAction, getSendEmailActionTitle, formatSendEmailAction, currentUserCompany } from "../../common/utils";
import SideDrawer from '../common/SideDrawer';
import CreateTitleTransfer from '../../containers/CreateTitleTransfer';
import {
  COMPANY_ADMIN, OFFICE_ADMIN, TITLE_TRANSFER_FILTER_STATUSES, TITLE_TRANSFER_CONTRACT_TYPE_FILTER_STATUSES,
  OBSERVER_TYPE_ID, SYSTEM, TITLE_TRANSFER_HEADERS, TITLE_TRANSFER_TABLE_COLUMN_LIMIT, DEFAULT_TITLE_TRANSFER_TABLE_COLUMN_LIMIT, FILTER_KEYS_TO_EXCLUDE, PREDEFINED_DATE_RANGE_FILTER_KEYS
} from '../../common/constants';
import APIService from "../../services/APIService";
import { Paper } from '@mui/material';
import DownloadDataDialog from '../common/DownloadDataDialog';
import { setDownloadBar } from '../../actions/main';
import TitleTransferAmendForm from './TitleTransferAmendForm';
import { RejectionReasonDialog } from "../../components/rejections/RejectionReasonDialog";
import CustomEmailDialog from '../common/CustomEmailDialog';
import {
  keys, without, forEach, get, includes, map, filter, has,
  isEqual, isEmpty, uniq, compact, pick, values, intersectionBy, find,
} from 'lodash';
import alertifyjs from 'alertifyjs';
import Filters from '../common/Filters';
import CustomHeaderOptions from '../common/CustomHeaderOptions';
import ListingControls from '../common/ListingControls'
import FiltersAppliedChip from '../common/FiltersAppliedChip';

const TITLE_TRANSFER_FILTER_KEYS_MAPPING = {
  'process_on_date_range': ['process_on__gte', 'process_on__lte']
}

class TitleTransfers extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      csvData: [],
      openTitleTransferSideDrawer: false,
      emailPopupParties: [],
      showEmailDialog: false,
      requestReason: {
        value: '',
        validators: [required()],
        errors: []
      },
      applyFilters: false,
      openSideDrawer: false,
      type_options: TITLE_TRANSFER_CONTRACT_TYPE_FILTER_STATUSES,
      filter_statuses: TITLE_TRANSFER_FILTER_STATUSES,
      filters: {},
      filterValues: {
        type__in: [],
        buyer__company__id__in: [],
        seller__company__id__in: [],
        status__in: [],
        commodity__id__in: [],
        grade__id__in: [],
        season__in: [],
        site__id__in: [],
        process_on_date_range: '',
        process_on__gte: '',
        process_on__lte: '',
        created_at__lte: '',
        created_at__gte: '',
      },
      customColumns: true,
      customColumnNames: {},
      customHeaderOptions: false,
      customTableColumnOptions: false,
      customTableColumnNames: {},
      customColumnTitle: undefined,
      isFilteredCsv: false,
      csvPopup: false,
      searchView: false,
      isSendEmail: false
    };
    this.openSideDraw = this.openSideDraw.bind(this);
    this.closeSideDraw = this.closeSideDraw.bind(this);
    this.onDownloadResponse = this.onDownloadResponse.bind(this);
    this.onCloseDownloadResponse = this.onCloseDownloadResponse.bind(this);
    this.closeRejectionReasonDialog = this.closeRejectionReasonDialog.bind(this);
    this.getActionsOptionMapperListItems = this.getActionsOptionMapperListItems.bind(this);
    this.toggleCustomColumnDownloads = this.toggleCustomColumnDownloads.bind(this);
    this.getTitle = this.getTitle.bind(this)
    this.contractId = this.props.contractId || this.props.match.params.contract_id;
  }


  handleFilters = bool => {
    this.setState({
      applyFilters: bool,
      openSideDrawer: bool,
    });
  };

  resetFilters = () => {
    this.setState({filters: {}, applyFilters: false, openSideDrawer: false}, () => this.handleFilterState('applyFilters', false))
  }

  handleFilterState = (key, value) => {
    this.setState({ [key]: value }, () => {
      if (key === 'applyFilters') {
        const { filters } = this.state;
        APIService.profiles()
          .filters()
          .post({ title_transfer: filters }, this.props.token)
          .then(res => {
            this.setState({filters: get(res, 'filters.title_transfer', {})}, () => {
              this.props.isLoading();
              this.props.getTitleTransfers(this.contractId, false, null, true);
            })
          });
      }
    });
  };

  onCloseDownloadResponse() {
    this.props.setDownloadBar(null, false, null);
  }

  onDownloadResponse(message) {
    this.props.setDownloadBar(message, true, this.onCloseDownloadResponse);
  }

  _attachCSVEventListener() {
    attachCSVEventListener(
      'title-transfers-csv-ready', 'Title Transfers', this.onDownloadResponse
    );
  }

  componentDidMount() {
    this._attachCSVEventListener();
    this.props.applySearch(null);
    APIService.profiles()
      .filters('title_transfer')
      .get(this.props.token)
      .then(res => {
        this.setState({
          filters: get(res, 'title_transfer', {}),
        });
      });
    this.props.getTitleTransfers(this.contractId, false);
    this.setHeaderAndBreadcrumbs();
  }

  componentDidUpdate(prevProp, prevState) {
    if (get(prevProp, 'count') !== this.props.count) this.setHeaderAndBreadcrumbs();
    if (
      prevState.openTitleTransferSideDrawer == false &&
      (this.props.isViewTitleTransferSideDrawerOpened || this.props.isAddTitleTransferSideDrawerToBeOpened)
    )
      this.openSideDraw();
      if (get(prevProp, 'showEmailDialog') !== this.props.showEmailDialog && this.props.showEmailDialog && (this.props.titleTransferId || this.props.selectedTitleTransferId)) 
        this.setState({ isSendEmail: true, showEmailDialog: true, emailPopupParties: this.getEmailPopupParties() });
  }

  componentWillUnmount() {
    this.props.titleTransfersResponse();
    this.props.applySearch(null);
    this.props.showHideTitleTransferSideDrawer(false);
    this.props.showViewTitleTransferSideDrawer(false);
    this.props.showAddTitleTransferSideDrawer(false);
  }

  setHeaderAndBreadcrumbs() {
    const { count } = this.props;
    let breadcrumbs = [
      { text: this.contractId ? 'Title Transfers' : `Title Transfers (${count})` },
    ];
    let headerText = 'Title Transfers';
    if (this.contractId) {
      breadcrumbs = [
        { text: 'Contracts', route: '/Contracts' },
        { text: get(this.props.contract, 'referenceNumber', ''), route: '/Contracts/' + this.contractId + '/contract' },
        { text: 'Title Transfers' },
      ];
      headerText = 'Commodity Contract ' + get(this.props.contract, 'referenceNumber', '');
      this.props.setSubHeaderText(getContractSubHeaderText(this.props.contract));
    }
    this.props.setHeaderText(headerText);

    if (!isEqual(this.props.breadcrumbs, breadcrumbs)) {
      this.props.setBreadcrumbs(breadcrumbs);
    }
  }

  openSideDraw() {
    this.props.showHideTitleTransferSideDrawer(!this.props.isViewTitleTransferSideDrawerOpened);
    if (this.props.isViewTitleTransferSideDrawerOpened)
      this.props.showViewTitleTransferSideDrawer(true);
    this.setState({ openTitleTransferSideDrawer: true, });
  }

  closeSideDraw = success => {
    if(success)
      this.props.getTitleTransfers(this.contractId, false)
    this.props.showHideTitleTransferSideDrawer(false);
    this.props.showViewTitleTransferSideDrawer(false);
    this.props.showAddTitleTransferSideDrawer(false);
    this.props.setSelectedTitleTransfer(null);
    this.props.updateDuplicateTitleTransferId(null)
    this.setState({ openTitleTransferSideDrawer: false, });
  }

  fetchCSVData = () => {
    this.props.setDownloadBar('Your Title Transfers CSV is getting prepared. Please visit <a href="/#/downloads">Downloads</a> in few moments.', true, null);
    var param = this.state.isFilteredCsv ? 'show_filters': '';
    if (this.state.searchView && this.props.isSearchApplied)
      param+= param.length == 0 ? `search=${this.props.isSearchApplied}` : `&search=${this.props.isSearchApplied}`;
    if (this.state.customColumns)
      param+= param.length == 0 ? 'custom_csv' : '&custom_csv';
    const queryParams = new URLSearchParams(window.location.hash.split('?')[1]);
    if(queryParams.get('include_void') == 'true')
      param+= param.length == 0 ? 'include_void=true' : '&include_void=true';
    let service = APIService.contracts();
    service.appendToUrl(`title-transfers/csv/?${param}`);
    if (this.props.contractId)
      service.appendToUrl(`commodity_contract_id=${this.contractId}`);
    this.setState({csvPopup: false, searchView: false});
    service.get(
      this.props.token,
      {
        'Content-Type': 'text/csv',
        'Accept': 'text/csv',
      },
    ).then(csvData => {
      csvData = csvData || [];
      this.setState(state => ({ ...state, csvData }));
    });
  };

  canExportCSV() {
    return includes([COMPANY_ADMIN, OFFICE_ADMIN, SYSTEM, OBSERVER_TYPE_ID], get(this.props.currentUser, 'typeId'));
  }

  handleRequestReasonChange = (event) => {
    const value = event.target.value;
    const newState = { ...this.state };
    newState.requestReason.value = value;
    this.setState(newState);
  };

  setReasonErrors(errors) {
    const newState = { ...this.state };
    newState.requestReason.errors = errors || this.getReasonErrors();
    this.setState({
      requestReason: { ...this.state.requestReason, errors: errors || this.getReasonErrors() }
    });
  }

  getReasonErrors() {
    const errors = [];
    const value = get(this.state, `requestReason.value`);
    const validators = get(this.state, `requestReason.validators`, []);
    validators.forEach((validator) => {
      if (validator.isInvalid(value)) {
        errors.push(validator.message);
      }
    });
    return errors;
  }

  getEmailPopupParties = () => {
    return ['buyer', 'seller', 'transferSite'];
  };

  getPartyEmails = () => {
    const titleTransfer = intersectionBy(this.props.titleTransfers, [{ id: this.props.titleTransferId || this.props.selectedTitleTransferId  }], 'id');
    return {
      buyer: get(titleTransfer[0], 'buyerContact.email', '') || get(titleTransfer[0], 'buyerEmail', ''),
      seller: get(titleTransfer[0], 'sellerContact.email', '') || get(titleTransfer[0], 'sellerEmail', ''),
    };
  };

  getEmailPopupPartiesCompanyIds() {
    const parties = this.getEmailPopupParties();
    const ids = {};
    const titleTransfer = intersectionBy(this.props.titleTransfers, [{ id: this.props.titleTransferId|| this.props.selectedTitleTransferId   }], 'id');
    forEach(parties, party => {
      if (party === 'buyer')
        ids.buyer = get(titleTransfer[0], 'buyer.companyId') || get(titleTransfer[0], 'buyerCompanyId');
      if (party === 'seller')
        ids.seller = get(titleTransfer[0], 'seller.companyId')|| get(titleTransfer[0], 'sellerCompanyId');
      if (party === 'delivery site' || party == 'transferSite')
        ids.transferSite = get(titleTransfer[0], 'transferSite.companyId') || get(titleTransfer[0], 'transferSiteCompanyId');
    });

    return ids;
  }

  async getPartyContacts() {
    if (this.gotOncePartyContacts)
      return;

    this.gotOncePartyContacts = true;
    const parties = this.getEmailPopupPartiesCompanyIds();
    const partiesWithoutContacts = without(keys(parties));
    const contacts = {};
    if (!isEmpty(partiesWithoutContacts)) {
      const companyIds = uniq(compact(values(pick(parties, partiesWithoutContacts))));
      if (isEmpty(companyIds))
        return contacts;
      const companyQueryString = map(companyIds, id => `company_ids=${id}`).join('&');
      const employees = await APIService.profiles().appendToUrl(`employees-signature/?${companyQueryString}`).get(this.props.token);
      forEach(partiesWithoutContacts, party => {
        contacts[party] = filter(employees, { companyId: parties[party] });
      });
    }

    return contacts;
  }

  getEmailSubject = () => {
    const companyName = get(this.props.currentUser, 'company.name', "");
    const titleTransfer = intersectionBy(this.props.titleTransfers, [{ id: this.props.titleTransferId || this.props.selectedTitleTransferId  }], 'id')[0];
    let actionType = this.state.isSendEmail ? get(titleTransfer, 'lastEmailAction.actionType') : 'Void'
    actionType = formatSendEmailAction(actionType);
    let displaySubject = actionType? `[${actionType}] `: ''
    displaySubject += `${companyName} Title Transfer #${titleTransfer?.identifier}`;
    return displaySubject
  };

  getFooterNote = () => {
    return 'Title Transfer PDF will be automatically sent as part of the email';
  };

  openEmailDialog = data => {
    this.setState({ showEmailDialog: true, emailPopupParties: this.getEmailPopupParties() });
    this.payloadData = data;
  };

  processVoidRequest = data => {
    if(!data['communication'])
      data['communication'] = {}
    data['communication'].subject ??= null;
    this.props.isLoading('alertify');
    APIService.contracts().appendToUrl(`title-transfers/${this.props.titleTransferId}/void/`)
      .put({ data }, this.props.userToken)
      .then(item => {
        if (has(item, 'errors')) {
          alertifyjs.error(item.errors[0]);
        } else {
          alertifyjs.success('Title Transfer is voided', 1, () => {
            if(this.props.copyFrom){
              this.props.getTitleTransfers(this.contractId, false);
              this.props.showAddTitleTransferSideDrawer(true)
            }
            else
              window.location.reload();
          });
        }
        this.props.voidTitleTransferDialog(false, []);
      });
  }

  closeEmailDialog = async (communicationData, justClose) => {
    if (justClose) {
      this.gotOncePartyContacts = false;
      this.setState({ showEmailDialog: false, isSendEmail: false });
      this.props.showTitleTransferEmailDialog(false)
    }
    else if(this.state.isSendEmail){
      try {
          const emailData = {
              recipients: communicationData.recipients,
              subject: communicationData.subject || `Title Transfer`,
              acceptanceRequired: communicationData.acceptanceRequired,
              request_reason: communicationData.request_reason
          };
          const titleTransfer = intersectionBy(this.props.titleTransfers, [{ id: this.props.titleTransferId || this.props.selectedTitleTransferId  }], 'id')[0];
          const actionType = get(titleTransfer, 'lastEmailAction.actionType')
          if (actionType) {
            let isResend = !get(titleTransfer, 'lastEmailAction.hasPendingEmail') && currentUserCompany()?.enableResendingEmail
            let appendUrl = `title-transfers/${titleTransfer.id}/send-email/${actionType.toLowerCase()}/`
            if (isResend)
              appendUrl += '?resend=true'
            await APIService.contracts()
              .appendToUrl(appendUrl)
              .post(emailData, this.props.token)
              .then(response => {
                if(!response || response.error)
                  alertifyjs.error('An Error Occurred!')
                else if(response && response.message){
                  alertifyjs.success('Email Sent Successfully!')
                  this.props.showTitleTransferEmailDialog(false)
                  this.setState({isSendEmail: false})
                  setTimeout(() => {
                    window.location.reload()
                  }, 500);
                }
              })
            this.setState({isSendEmail: false})
          }
        } catch (error) {
          alertifyjs.error('An Error Occurred!')
          console.error('Error sending email:', error);
        }
    }
    else if (this.state.showEmailDialog && !this.state.isSendEmail) {
      const data = this.payloadData;
      if (communicationData) {
        data['communication'] = communicationData;
      }
      this.setState({ showEmailDialog: false }, () => {
       this.processVoidRequest(data);
      });
    }
  };

  getTitle = () => {
    const titleTransfer = intersectionBy(this.props.titleTransfers, [{ id: this.props.titleTransferId || this.props.selectedTitleTransferId  }], 'id')[0];
    return this.state.isSendEmail? getSendEmailActionTitle(titleTransfer, 'Title Transfer') : null;
  }

  handleVoidClick = () => {
    this.setReasonErrors();
    if (this.state.requestReason.errors.length === 0) {
      if (this.props.showVoidDialog) {
        const data = { rejection_reason: this.state.requestReason.value };
        if(this.props.currentUser.company.showEmailPopup === false && !this.isLaterDisabled()) {
          data['communication']= {
            'acceptanceRequired': false
          }
          this.processVoidRequest(data);
        } else {
          this.openEmailDialog(data);
        }
      }
    }
  };

  closeRejectionReasonDialog = () => {
    const newState = { ...this.state };
    newState.requestReason.value = '';
    this.setState(newState);
    this.props.voidTitleTransferDialog(false, [])
  }

  getActionsOptionMapperListItems() {
    return [
      { name: 'Custom Table Columns', fx: () => this.updateCustomTableColumns() },
      defaultViewAction
    ];
  }

  async updateCustomTableColumns() {
    if (this.props.currentUser.company.enableCustomCsv) {
      const tableColumnNames = await APIService.profiles().appendToUrl(`${this.props.currentUser.id}/table-preferences/title_transfer_table/`).get(this.props.token);
      this.setState({customTableColumnNames: tableColumnNames, customTableColumnOptions: true});
    }
    else {
      alertifyjs.alert(
        'Permission Denied',
        'This feature is not enabled for your company. Please contact AgriChain support',
        () => { },
      );
    }
  }

  getColumnsMapping() {
    const contractColumns = TITLE_TRANSFER_HEADERS();
    return contractColumns.reduce((obj, objectKey) => ({ ...obj, [objectKey.key]: objectKey.header }), {});
  }

  updateColumnCount(count) {
    this.setState({customColumnTitle: `Edit Columns (${count})`});
  }

  customFilterValueExist = filterKeys => filterKeys.some(key => Boolean(get(this.state.filters, key)))

  filterCriteria = (key, value) => includes(FILTER_KEYS_TO_EXCLUDE, key) ? false : includes(PREDEFINED_DATE_RANGE_FILTER_KEYS, key) && value === 'custom' ? this.customFilterValueExist(get(TITLE_TRANSFER_FILTER_KEYS_MAPPING, key)) : value.length !== 0;

  customCsvEnabled(isFilteredCsv) {
    const newState = {...this.state};

    newState.isFilteredCsv = isFilteredCsv;
    if (this.props.currentUser.company.enableCustomCsv || this.props.isSearchApplied) {
      newState.csvPopup = true;
      this.setState(newState);
    }
    else {
      newState.customColumns = false;
      this.setState(newState, this.fetchCSVData);
    }
  }

  toggleCustomColumnDownloads = () => {
    this.setState({customColumns: !this.state.customColumns})
  }

  getDisabledPartiesForEmail = titleTransfer => {
    const entity = titleTransfer || find(this.props.titleTransfers, { id: this.props.titleTransferId })
    return entity?.isStocksManagement ? ['buyer', 'transferSite'] : []
  };

  isLaterDisabled = () => {
    const titleTransfer = find(this.props.titleTransfers, { id: this.props.titleTransferId });
    let disabledParties = this.getDisabledPartiesForEmail(titleTransfer);
    return disabledParties.length > 0
  }

  render() {
    const canExportCSV = this.canExportCSV()
    const globalListing = isAtGlobalTitleTransfer()
    return (
      <Paper className='paper-table-paginated'>
        <div style={{ position: 'relative'}}>
          <div style={{ position: 'absolute', right: '0px', top: '0px' }}>
            <ListingControls
              controls={[
                {
                  type: 'new',
                  order: 1,
                  hidden: !(this.props.contract || globalListing),
                  props: {
                    tooltipTitle: 'Create a new Title Transfer',
                    label: 'Title Transfer',
                    onClick: this.openSideDraw
                  }
                },
                {
                  type: 'filter',
                  hidden: !globalListing,
                  props: globalListing ? {
                    value: this.state.applyFilters,
                    onClick: () => this.handleFilters(true),
                    applied: Object.entries(this.state.filters).filter(val => this.filterCriteria(val[0], val[1]))?.length || 0
                  } : {}
                },
                {
                  type: 'report',
                  hidden: !canExportCSV,
                  props: canExportCSV ? {
                    defaultHandler: () => this.customCsvEnabled(false),
                    showMenus: globalListing && !isEmpty(Object.entries(this.state.filters).filter(val => val[1].length !== 0)),
                    optionMapper: [
                      { name: 'Complete List', description: 'List of all title transfers present', fx: () => this.customCsvEnabled(false) },
                      { name: 'Filtered List', description: 'List of title transfers matching current filter selection', fx: () => this.customCsvEnabled(true) },
                    ]
                  } : {}
                },
                {
                  type: 'action',
                  props: {
                    showMenus: true,
                    optionMapper: this.getActionsOptionMapperListItems()
                  }
                },
              ]}
            />
          </div>
          <DownloadDataDialog
            open={this.state.csvPopup}
            onClose={() => this.setState({csvPopup: false, searchView: false})}
            title='Title Transfers Report'
            enableCustomCsv={this.props.currentUser.company.enableCustomCsv}
            isSearchApplied={this.props.isSearchApplied}
            searchView={this.state.searchView}
            onSearchViewChange={() => this.setState({searchView: !this.state.searchView})}
            isFilteredCsv={this.state.isFilteredCsv}
            onDownload={this.fetchCSVData}
            customColumnTitle={this.state.customColumnTitle}
            user={this.props.currentUser}
            token={this.props.token}
            csvType='title_transfer_csv'
            updateColumnCount={(count) => this.updateColumnCount(count)}
            toggleCustomColumnDownloads={this.toggleCustomColumnDownloads}
          />
          {
            this.state.applyFilters &&
             <SideDrawer isOpen={this.state.openSideDrawer} title='Filters' size='big' onClose={() => this.handleFilters(false)} app='filters'>
               <Filters
                 isLoading={this.props.isLoading}
                 forceStopLoader={this.props.forceStopLoader}
                 handleFilterState={this.handleFilterState}
                 filters={this.state.filters}
                 statusTemp={this.state.filter_statuses}
                 type_options={this.state.type_options}
                 filterValues={this.state.filterValues}
                 isTitleTransferFilters
               />
             </SideDrawer>
         }
          {
            (this.props.isAddTitleTransferSideDrawerOpened || this.props.isAddTitleTransferSideDrawerToBeOpened) &&
            <SideDrawer
              isOpen={this.state.openTitleTransferSideDrawer}
              title="Add Title Transfer"
              onClose={this.closeSideDraw}
              size='xlarge'>
              <CreateTitleTransfer
                closeDrawer={this.closeSideDraw}
                contract={this.props.contract}
                copyFrom={this.props.copyFrom}
              />
            </SideDrawer>
          }
          {
            this.props.showVoidDialog && isAtGlobalTitleTransfer() &&
            <RejectionReasonDialog
              open={this.props.showVoidDialog}
              onClose={this.closeRejectionReasonDialog}
              title={'Void Title Transfer'}
              value={this.state.requestReason.value}
              onChange={this.handleRequestReasonChange}
              helperText={get(this.state, 'requestReason.errors[0]', '')}
              onCancel={this.closeRejectionReasonDialog}
              onReject={this.handleVoidClick}
              placeholder='Enter you reason for void request'
              submitText='Submit'
            />
          }
          {this.state.showEmailDialog && isAtGlobalTitleTransfer() &&
           <CustomEmailDialog
             parties={this.state.emailPopupParties}
             selectedParties={this.isLaterDisabled() ? ['buyer', 'seller', 'transferSite'] : ['buyer', 'seller']}
             title={this.getTitle() || "Email PDF copies to"}
             partyEmails={this.getPartyEmails()}
             partyContacts={this.getPartyContacts()}
             subject={this.getEmailSubject()}
             noBody={true}
             footer={this.getFooterNote()}
             open={this.state.showEmailDialog}
             onClose={this.closeEmailDialog}
             disableAcceptanceRequired={true}
             disabledPartiesForEmail={this.getDisabledPartiesForEmail()}
             disableLater={this.isLaterDisabled()}
             isSendEmail={this.state.isSendEmail}
           />
          }
          {
            this.props.isViewTitleTransferSideDrawerOpened &&
            <SideDrawer
              isOpen={this.state.openTitleTransferSideDrawer}
              title="Amend Title Transfer"
              onClose={this.closeSideDraw}
              size='xlarge'>
              <TitleTransferAmendForm onClose={this.closeSideDraw} />
            </SideDrawer>
          }
            <SideDrawer
              isOpen={this.state.customTableColumnOptions}
              title={this.state.customColumnTitle}
              onClose={() => this.setState({customTableColumnOptions: false})}
              size="small"
            >
              <CustomHeaderOptions
                customColumns={this.state.customTableColumnNames}
                closeDrawer={() => this.setState({customTableColumnOptions: false})}
                user={this.props.currentUser}
                token={this.props.token}
                table_type="title_transfer_table"
                columnsMapping={this.getColumnsMapping()}
                maxColumnLimit={TITLE_TRANSFER_TABLE_COLUMN_LIMIT}
                updateColumnCount={(count) => this.updateColumnCount(count)}
                defaultColumnLimit={DEFAULT_TITLE_TRANSFER_TABLE_COLUMN_LIMIT}
              />
            </SideDrawer>
          <FiltersAppliedChip filters={this.state.filters} show={isAtGlobalTitleTransfer()} style={{paddingRight: '45%'}} onClear={this.resetFilters} />
          <TitleTransfersTable contractId={this.props.contractId} nested={this.props.nested} />
        </div>
      </Paper>
    );
  }
}

const mapStateToProps = state => {
  return {
    currentUser: state.main.user.user,
    token: state.main.user.token,
    titleTransfers: state.companies.contracts.titleTransfers,
    isAddTitleTransferSideDrawerOpened: state.companies.contracts.isAddTitleTransferSideDrawerOpened,
    isAddTitleTransferSideDrawerToBeOpened: state.companies.contracts.isAddTitleTransferSideDrawerToBeOpened,
    breadcrumbs: state.main.breadcrumbs,
    count: get(state.companies.contracts, 'titleTransferPaginatedData.count') || 0,
    isViewTitleTransferSideDrawerOpened: state.companies.contracts.isViewTitleTransferSideDrawerOpened,
    showVoidDialog: state.companies.contracts.showVoidDialog,
    copyFrom: state.companies.contracts.copyFrom,
    titleTransferId: state.companies.contracts.titleTransferId,
    isSearchApplied: state.main.isSearchApplied,
    showEmailDialog: state.companies.contracts.showTitleTransferEmailDialog,
    selectedTitleTransfer: state.companies.contracts.selectedTitleTransfer,
    selectedTitleTransferId: state.companies.contracts.selectedTitleTransferId
  };
};

const mapDispatchToProps = dispatch => ({
  showHideTitleTransferSideDrawer: (flag) => dispatch(showHideTitleTransferSideDrawer(flag)),
  showViewTitleTransferSideDrawer: (flag) => dispatch(showViewTitleTransferSideDrawer(flag)),
  canCreateTitleTransfer: (id, callback) => dispatch(canCreateTitleTransfer(id, callback)),
  setBreadcrumbs: (breadcrumbs) => dispatch(setBreadcrumbs(breadcrumbs)),
  voidTitleTransferDialog: (flag, item) => dispatch(voidTitleTransferDialog(flag, item)),
  setSubHeaderText: (item) => dispatch(setSubHeaderText(item)),
  getTitleTransfers: (id, flag, url, loaderFlag) => dispatch(getTitleTransfers(id, flag, url, loaderFlag)),
  setHeaderText: (text) => dispatch(setHeaderText(text)),
  isLoading: (component) => dispatch(isLoading(component)),
  forceStopLoader: () => dispatch(forceStopLoader()),
  setDownloadBar: (message, flag, onClose) => dispatch(setDownloadBar(message, flag, onClose)),
  titleTransfersResponse: () => dispatch(titleTransfersResponse([])),
  setSelectedTitleTransfer: (value) => dispatch(receiveTitleTransfer(value)),
  applySearch: searchStr => dispatch(isSearchApplied(searchStr)),
  showAddTitleTransferSideDrawer: flag => dispatch(showAddTitleTransferSideDrawer(flag)),
  updateDuplicateTitleTransferId: id => dispatch(updateDuplicateTitleTransferId(id)),
  showTitleTransferEmailDialog: flag => dispatch(showTitleTransferEmailDialog(flag)),
  getSelectedTitleTransfer: (id, actionCreator, stopLoader, unit) => dispatch(getSelectedTitleTransfer(id, actionCreator, stopLoader, unit, false))
});

export default connect(mapStateToProps, mapDispatchToProps)(TitleTransfers);
