import React from 'react';
import TitleTransferIcon from '../common/icons/TitleTransfer';
import TransactionBase from '../common/TransactionBase'

const TitleTransfer = ({ titleTransfer, noLink }) => {
  return (
    <TransactionBase
      icon={<TitleTransferIcon fill='#000' width="34" height="34" noStyle />}
      sx={{
        '.MuiListItemIcon-root': {minWidth: 'auto', marginRight: '4px'},
      }}
      transaction={titleTransfer}
      url={`#/title-transfers/${titleTransfer.id}/details`}
      noLink={noLink}
      identifier={titleTransfer?.identifier}
    />
  )
}

export default TitleTransfer
