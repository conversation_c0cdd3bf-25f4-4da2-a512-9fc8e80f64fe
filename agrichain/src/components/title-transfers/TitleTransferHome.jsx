import React from 'react';

import { connect } from 'react-redux';
import TitleTransferDetails from './TitleTransferDetails';
import isEmpty from 'lodash/isEmpty';
import get from "lodash/get";
import find from 'lodash/find';
import { isLoading, forceStopLoader } from '../../actions/main';
import Notes from '../notes/Notes';
import CommonTabs from '../common/CommonTabs';
import { getSelectedTitleTransfer, receiveTitleTransfer } from '../../actions/companies/contracts';
import { TitleTransferStatusBar } from './TitleTransferStatusBar';
import TitleTransferLoads  from './TitleTransferLoads';
import { AppContext } from '../main/LayoutContext';

class TitleTransferHome extends React.Component {
  static contextType = AppContext
  constructor(props) {
    super(props);
    this.state = {
      activeTab: this.props.location.pathname,
    };
  }

  baseBreadcrumbs() {
    const { selectedTitleTransfer } = this.props;
    console.log(selectedTitleTransfer)
    return [
      {text: 'Title Transfer', route: `/title-transfers/`},
      {text: get(selectedTitleTransfer, 'identifier', ''), route: `/title-transfers/${get(selectedTitleTransfer, 'id')}/details`}
    ];
  }

  baseHeaderText() {
    const { selectedTitleTransfer } = this.props;
    return `Title Transfer ${get(selectedTitleTransfer, 'identifier', '')}`;
  }

  componentDidMount() {
    this.props.waitForLoader('transferDetail');
    if (isEmpty(this.props.selectedTitleTransfer)) {
      const { title_transfer_id } = this.props.match.params;
      this.props.getSelectedTitleTransfer(title_transfer_id, receiveTitleTransfer, true, this.props.currentUser?.unit);
    }
    if(this.props.breadcrumbsFunc)
      this.props.breadcrumbsFunc();
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    nextProps.forceStopLoader();
    return nextProps.location.pathname !== prevState.activeTab ?
           { ...prevState, activeTab: nextProps.location.pathname } :
           prevState;
  }

  componentDidUpdate() {
    if (isEmpty(this.props.selectedTitleTransfer)) {
      const { title_transfer_id } = this.props.match.params;
      this.props.getSelectedTitleTransfer(title_transfer_id, receiveTitleTransfer, true, this.props.currentUser?.unit, false);
    }
  }

  render() {
    const { isMobileDevice } = this.context
    const PARENT_URL = this.props.match.url.split('/details')[0];
    const {title_transfer_id} = this.props.match.params;
    const { selectedTitleTransfer } = this.props;

    let tabs = [
      {
        label: 'Title Transfer',
        key: 'details',
        url: `${PARENT_URL}/details`,
        component: () => <TitleTransferDetails {...this.props} />
      }
    ];

    if(!isMobileDevice) {
      if (!isEmpty(selectedTitleTransfer?.canolaLoadIds)) {
        tabs.push(
          {
            label: 'Loads',
            key: 'loads',
            url: `${PARENT_URL}/loads`,
            component: () => <TitleTransferLoads {...this.props} titleTransferId={title_transfer_id} />
          }
        );
      }
      tabs.push({
        label: 'Audit History',
        key: 'notes',
        url: `${PARENT_URL}/notes`,
        component: () => <Notes {...this.props} objectId={this.props.selectedTitleTransfer?.id} objectType='titletransfer' companyId={this.props.companyId} />
      });
    }
    const baseBreadcrumbs = this.baseBreadcrumbs()
    const baseHeaderText = this.baseHeaderText()

    return (
      <div className="order-details-container">
        <div className="tab">
          <div className="tab-header">
            <CommonTabs value={this.state.activeTab} tabs={tabs} />
          </div>
          {
            !isEmpty(this.props.selectedTitleTransfer) &&
            this.state.activeTab !== find(tabs, {key: 'details'})?.url &&
              <div className="contract-details-container" style={{marginBottom: '10px'}}>
                <TitleTransferStatusBar {...this.props} />
            </div>
          }
          <div className="tab-content">
            {
              this.state.activeTab == find(tabs, {key: 'details'})?.url &&
                <TitleTransferDetails
                  {...this.props}
                  baseBreadcrumbs={baseBreadcrumbs}
                  baseHeaderText={baseHeaderText}
                />
            }
            {
              this.state.activeTab == find(tabs, { key: 'notes' })?.url &&
              <div className="contract-details-container">
                <div style={{marginTop: '10px'}}>
                  <Notes
                    {...this.props}
                    objectId={title_transfer_id}
                    objectType='titletransfer'
                    companyId={this.props.currentUser?.companyId}
                    titleTransfer={this.props.selectedTitleTransfer}
                    baseBreadcrumbs={baseBreadcrumbs}
                    baseHeaderText={baseHeaderText}
                  />
                </div>
              </div>
            }
            {
              this.state.activeTab == find(tabs, {key: 'loads'})?.url && this.props.selectedTitleTransfer?.canolaLoadIds &&
                <TitleTransferLoads
                  {...this.props}
                  titleTransferId={title_transfer_id}
                  baseBreadcrumbs={baseBreadcrumbs}
                  baseHeaderText={baseHeaderText}
                />
            }
          </div>
        </div>
      </div>
    );
  }
}

TitleTransferHome.defaultProps = {
  contract_id: null,
};

const mapStateToProps = state => {
  return {
     movement: state.companies.freights.selectedFreight || null,
     currentUser: state.main.user.user,
     selectedTitleTransfer: state.companies.contracts.selectedTitleTransfer,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    getSelectedTitleTransfer: (id, actionCreator, stopLoader, unit) => dispatch(getSelectedTitleTransfer(id, actionCreator, stopLoader, unit, false)),
    waitForLoader: () => dispatch(isLoading('transferDetail')),
    loading: () => dispatch(isLoading('unknown')),
    receiveTitleTransfer: (item) => dispatch(receiveTitleTransfer(item)),
    forceStopLoader: () => dispatch(forceStopLoader())
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(TitleTransferHome);
