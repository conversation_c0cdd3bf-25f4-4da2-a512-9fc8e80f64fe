import React, { Component } from 'react';
import { connect } from 'react-redux';
import { get, isEqual } from 'lodash';
import { setHeaderText, setBreadcrumbs } from '../../actions/main';
import TitleTransferDetail from './TitleTransferDetail';
import TitleTransferStatusBar from './TitleTransferStatusBar';
import TitleTransferMinifiedCounterParties from './TitleTransferMinifiedCounterParties';
import OrderNotes from '../freights/order-details/OrderNotes';
import { RejectionDetails } from '../rejections/RejectionDetails';

class TitleTransferDetails extends Component {
  constructor(props) {
    super(props);
    this.state = {
      note: {
        description: '',
        attachments: [],
        attachmentUrls: [],
        companyId: this.props.currentUser.companyId,
        errors : []
      },
    };
  }

  componentDidMount() {
    this.setLayout()
    if(this.props.selectedTitleTransfer?.note) {
      const newState = {...this.state}
      newState.note.description = this.props.selectedTitleTransfer.note.description || ''
      newState.note.attachments = this.props.selectedTitleTransfer.note.attachments || []
      newState.note.attachmentUrls = this.props.selectedTitleTransfer.note.attachmentUrls || []
      this.setState(newState)
    }
  }

  componentDidUpdate() {
    if(this.props.selectedTitleTransfer?.id)
      this.setLayout()
  }

  setLayout() {
    const { selectedTitleTransfer } = this.props
    if (selectedTitleTransfer) {
      const headerText = 'Title Transfer ' + get(selectedTitleTransfer, 'identifier', '');
      this.props.dispatch(setHeaderText(headerText));

      const breadcrumbs = [{ text: 'Title Transfers', route: '/title-transfers' }, { text: get(selectedTitleTransfer, 'identifier', '') }];
      if (!isEqual(this.props.breadcrumbs, breadcrumbs)) {
        this.props.dispatch(setBreadcrumbs(breadcrumbs));
      }
    }
  }


  render() {
    return (
      <div className='contract-details-container'>
        <div style={{marginBottom: '10px'}}>
          <TitleTransferStatusBar {...this.props} />
          {get(this.props.selectedTitleTransfer, 'rejectionReason') && <RejectionDetails rejectionReasonObject={{"action": "Void", "rejectionReason": get(this.props.selectedTitleTransfer, 'rejectionReason')} }/>}
        </div>
        <TitleTransferMinifiedCounterParties {...this.props} />
        <TitleTransferDetail {...this.props} />
        {this.state.note && <OrderNotes {...this.props} entity={get(this.props, 'selectedTitleTransfer')}/>}
      </div>
    );
  }
}

const mapStateToProps = state => {
  return {
    currentUser: state.main.user.user,
    selectedTitleTransfer: state.companies.contracts.selectedTitleTransfer,
  };
};

export default connect(mapStateToProps)(TitleTransferDetails);
