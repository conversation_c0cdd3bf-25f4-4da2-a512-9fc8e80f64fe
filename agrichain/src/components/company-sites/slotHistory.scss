div.changeset-container {
    border-bottom: 1px solid lightgray;
    margin-top: -8px;
    .changeset-item-text {
        padding: 0 16px !important;
        margin-left: -35px;
    }
    div.changeset-items {
        padding: 0 16px 11px 16px;
        margin-left: 35px;
        margin-top: -8px;
        span.changeset-item {
            display: inline-block;
            margin: 8px 5px;
            font-size: 10px;
            margin-right: 5px;
            span.changeset-label {
                padding: 5px;
                border-top-left-radius: 15px;
                border-bottom-left-radius: 15px;
                border: 1px solid;
            }
            span.changeset-value {
                background-color: rgb(0, 25, 43);
                border: 1px solid rgb(0, 25, 43);
                color: white;
                padding: 5px;
                border-top-right-radius: 15px;
                border-bottom-right-radius: 15px;
            }
        }
    }
    p.changeset-date {
        display: block;
        color: rgba(0, 0, 0, 0.54);
        font-size: 0.875rem;
        font-weight: 400;
        line-height: 1.43;
        letter-spacing: 0.01071em;
        margin: 0;
    }
}

div.changeset-container:last-child {
    border-bottom: none;
}

.clickable:hover {
    text-decoration: underline;
}
