.action-centre-group-container {
    padding: 0 !important;
    .action-centre-group {
        tr:hover td {
            background-color: #fff !important;
        }
        .table-container {
            height: auto;
            max-height: 370px;
            overflow: scroll;
            width: 100%
        }
        .table-heading {
            max-height: 48px;
            min-height: 48px !important;
	          background-color: #F7F7F7;
	          box-shadow: inset 0 -1px 0 0 #E0E0E0, inset 0 1px 0 0 #E0E0E0;
            padding: 13.5px 24px;
            font-weight: 500;
        }
        table>thead>tr>th:first-child {
            padding-left: 24px !important;
        }
        table>tbody>tr>td:first-child {
            padding-left: 24px !important;
        }
        table>thead>tr>th:last-child {
            padding-right: 24px !important;
        }
        .btn-red-outlined {
            color: #f44336;
            border: 1px solid rgba(244, 67, 54, 0.5);

        }
        .btn-red-outlined:hover {
            background-color: rgba(244, 67, 54, 0.08);
        }
    }
}
