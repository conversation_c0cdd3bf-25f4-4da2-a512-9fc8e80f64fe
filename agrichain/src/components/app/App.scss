@import '../../common/variables';

body {
    background-color: rgb(240,243,246);
}

::backdrop
{
    background-color: #ffffff;
}

.App {
    width: 75%;
    float: right;
}

.App-header {
  background-color: #222;
  height: 150px;
  padding: 20px;
  color: #fff;
}

.App-title {
    font-size: 0.5em;
    text-align: center;
    margin-top: 1px;
}

.footer-version {
    font-size: 0.5em;
    text-align: right;
    margin-right: 10px;
}

@keyframes App-logo-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.Select--single > .Select-control .Select-value {
    color: $colorBlack!important;
}

.width-20 {
    width: 20% !important;
}
.width-15 {
    width: 15% !important;
}
.width-10 {
    width: 10% !important;
}
.width-temp {
    width: 10.5% !important;
}
.width-12 {
    width: 12% !important;
}
.blue-background {
    background: rgb(0, 25, 43) !important;
}
.text-gray {
    color: #fff !important;
    padding-right: 0 !important;
}
.font-weight-100 {
    font-weight: 100;
}

.padding-reset {
    padding-left: 0;
    padding-right: 0;
}

.margin-reset {
    margin: 0;
}

.file-upload:first-child {
    justify-content: flex-start;
    min-width: 130px;
}

.input-file {
    width: 1%;
    padding-left: 5px;
}

.form-center {
    text-align: center;
    width: 75%;
    margin-left: 10%;
    margin-bottom: 40%;
}

.label {
    width: 50% !important;
    input {
        color: black !important;
    }
    hr {
        width: 90% !important;
    }
}
div.fixed-space {
    min-height: 100px;
    min-width: 40%;
}

td, th {
    padding-left: 4px !important;
    padding-right: 4px !important;
    &.padding-reset {
        padding-left: 0px !important;
        padding-right: 0px !important;
    }
}

.no-record-column{
    text-align: center !important;
}

.disabled-section{
    pointer-events: none;
    opacity: 0.5;
    hr {
        border-bottom: 2px dotted rgba(0, 0, 0, 0.3) !important;
    }
}

.role-wrapper label{
    left: 0;
}
.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.ellipsis-line-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
td {
    overflow: hidden;
    text-overflow: ellipsis;
}
.left {
    text-align: left !important;
}
.right {
    text-align: right !important;
}
.center {
    text-align: center !important;
}
.pac-container{
    z-index: 10000 !important;
}

span#edit, .cursor-pointer{
    cursor: pointer;
}

.border-bottom-green {
    border-bottom: 1px solid rgb(224,224,224);
}

.error {
    color:rgb(244, 67, 54);
}
.form-sub-section {
    background-color: rgb(249,249,249);
    box-shadow: 3px 3px rgb(243, 243, 243);
}
.common-autocomplete {
    float: inherit;
}
.common-datetimepicker div:first-child {
    float: inherit;
}
.capitalize {
    text-transform: capitalize;
}
.thumb {
    height: 75px;
    width: 53px;
    border: 1px solid #000;
    margin: 10px 5px 0 0;
}
.thumb-pdf {
    margin: 10px 5px 0 0;
    max-width: 85%;
    max-height: 100px;
    min-width: 50px;
    min-height: 50px;
    width: auto;
    height: auto;
}
.preview-wrapper {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    row-gap: 5px;
}
.thumbnail-text {
    font-size: 12px;
    max-width: 80px;
    width: auto;
    padding: 0 !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
}
.file-upload-preview-files {
    display: flex;
    align-items: center;
    padding: 0px;
    span {
        display: flex;
        align-items: center;
        padding: 10px;
    }
}
button:focus { outline: 0; }
a:focus { outline: 0; }
.paper-table-paginated {
    padding: 10px 10px 0;
}
.paper-table {
    padding: 10px;
}
.table-search-box {
    width: 288px !important;
    height: 36.5px;
    border-radius: 4px;
    background-color: #ffffff !important;
    border: solid 1px #999999 !important;
    padding: 10px !important;
}
.loader-inline {
    text-align: center;
    background-color: #fcfcfc;
}
.loader {
    &.hide {
        display: none;
    }

    &.show {
        text-align: center;
        z-index: 10000;
        position: fixed;
        left: 0;
        right: 0;
        top:0;
        bottom: 0;
        background-color: #fcfcfc;
        overflow: hidden;
        opacity: 0.8;
        justify-content: center;
        align-items: center;
    }
}

.no-link {
    color: black;
}
.no-link:hover {
    text-decoration: none;
    color: black;
}
.no-click {
    pointer-events: none;
}
.no-svg {
    svg {
        display: none !important;
    }
}
.freight-movement-base-entity span.Select-clear-zone {
    display: none;
}

.key-contact {
    background-color: rgba(1,25,42,0.1) !important;
    border-left: 5px solid rgb(108,172,46) !important;
}
.delhi-blue {
    background: rgba(195,219,254,0.4);
}
.void-table-row {
    background-color: rgba(211, 47, 47, 0.05);
}

.yellow {
    background: yellow;
}
.yellow {
    background: yellow;
}
.skyblue {
    background: lightskyblue;
}
.seagreen {
    background: lightseagreen;
}
.lightgray {
    background: lightgray;
}
tr.row-with-data:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.04);
}
tr.row-with-data.delhi-blue:hover td {
    background-color: rgba(195,219,254,0.1) !important;
}

tr.row-with-data.void-table-row:hover td {
    background-color: rgba(211, 47, 47, 0.05) !important;
}

tr.row-with-data:hover {
    box-shadow: 0.1px 0.1px 4px 0px rgba(0, 0, 0, 0.75);
    &.void-table-row {
        box-shadow: none;
    }
}
.step-connector span {
    border-top-width: 1.5px;
}
.step-label-completed {
    color: rgba(0, 0, 0, 0.54) !important;
    fontWeight: 400 !important;
}
.step-label-active {
    color: rgb(0,25,43) !important;
    fontWeight: 500 !important;
}

.tabs.fixed-action-panel {
    border-top: 1px solid rgb(224, 224, 224);
    position: fixed;
    bottom: 0;
    z-index: 1;
    background-color: #fff;
    padding: 15px;
    &.open {
        left: 220px;
        width: calc(100% - 220px);
    }
    &.collapsed {
        left: 65px;
        width: calc(100% - 65px);
    }
}

.link-icon {
    -webkit-transform: rotate(130deg);
    -moz-transform: rotate(130deg);
    -ms-transform: rotate(130deg);
    -o-transform: rotate(130deg);
    transform: rotate(130deg);

    &.red {
        color: #ef6030;
    }

    &.green {
        color: rgb(106,174,32);
    }
}

.add-new-right {
    color: rgb(106,174,32);
    top: 17px;
    position: absolute;
    right: -40px;
}

.add-new-right:hover {
    cursor: pointer;
}

.dialog-title-white-text {
    h2,span {
        color: #fff;
    }
}
.tooltip-inner {
    max-width: 350px;
    width: 350px;
}

.condition-section {
    white-space: pre-wrap;
}

input#identifier, input#contractIdentifier, input#loadIdentifier, input#outload\.loadIdentifier ,input#inload\.loadIdentifier {
    text-transform: uppercase;
}

input#identifier::placeholder,input#contractIdentifier::placeholder {
    text-transform: capitalize;
}

div.week-day {
    align-items: center;
    display: inline-flex;
    justify-content: center;
    -webkit-box-flex: 1;
    flex-grow: 1;
    border-radius: 50%;
    color: #757575;
    background-color: #f1f3f4;
    font-size: 10px;
    font-weight: 500;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    cursor: pointer;

    &.selected {
        background-color: #4285f4;
        color: #fff;
    }
}

.no-side-padding {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.no-left-padding {
    padding-left: 0 !important
}

.no-right-padding {
    padding-right: 0 !important
}

div.freight-slot-dialog > div > div {
    width: 960px;
    max-width: 960px;
}

div.freight-slot-dialog.with-comments > div > div {
    left: -10%;
}

div.freight-slot-form-content {
    padding-right: 0;
    overflow-x: hidden;
}

div.select-down {
    width: 100%;
}

div.select-up {
    width: 100%;

    .Select-menu-outer {
        top: auto !important;
        bottom: calc(100% - 1px) !important;
        border-bottom-left-radius: 0px !important;
        border-bottom-right-radius: 0px !important;
        border-top-left-radius: 5px !important;
        border-top-right-radius: 5px !important;
    }

    .is-open .Select-control {
        border-top-right-radius: 0 !important;
        border-top-left-radius: 0 !important;
        border-bottom-right-radius: 5px !important;
        border-bottom-left-radius: 5px !important;
    }
}
.slot-stats {
    div {
        font-size: 13px;
        margin: 0 2px;
        color: #FFF;
        &.disabled {
            opacity: 0.2;
            box-shadow: none;
            span {
                padding-left: 10px;
                padding-right: 5px;
            }
        }
        &.enabled {
            opacity: 1;
            box-shadow: 0px 1px 5px 0px rgba(0,0,0,0.2),
            0px 2px 2px 0px rgba(0,0,0,0.14),
            0px 3px 1px -2px rgba(0,0,0,0.12);

            span {
                padding-left: 10px !important;
                padding-right: 5px !important;
            }
        }
    }


    div.booked, div.inProgress, div.completed, div.cancelled {
        span {
            padding-left: 10px !important;
        }
    }
    div.delayed {
        span {
            padding-left: 6px;
        }
    }

    div.planned {
        background-color: #4AB9D8;
    }
    div.booked {
        background-color: #F7C718;
    }
    div.inProgress {
        background-color: #FF8500;
    }
    div.completed {
        background-color: #3BB820;
    }
    div.delayed {
        background-color: #D41BD4;
    }
    div.cancelled {
        background-color: #CB0000;
    }
}

.sm-settings-form {
    label {
        margin: 0 0 10px 0;
    }
    th.status-name {
        width: 26%;
    }
    th.status-label {
        width: 46%;
        padding-right: 15px !important;
    }
    th.color {
        cursor: pointer;
    }

    div.color-container {
        padding: 5px;
        background: rgb(255, 255, 255);
        border-radius: 1px;
        box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px;
        display: inline-block;
        cursor: pointer;
        div {
            cursor: pointer;
        }
    }
}
.slot-comments-dialog {
    left: 73% !important;
    div[role="document"] > div:first-child {
        height: calc(100% - 96px);
        margin: 15px;
        width: 360px;
    }
}

.slot-comments-unselected {
    background-color: #fff !important;
    color: rgb(0, 25, 43) !important;
}

.slot-comments-selected {
    background-color: rgb(0, 25, 43) !important;
    color: #fff !important;
}

.alertify-notifier div {
    text-shadow: none !important;
    font-size: 14px;
}


.slot-urgent {
    animation: blinker 1s linear infinite;
}

@keyframes blinker {
    50% {
        background-color: darkgray;
    }
}
.clickable {
    cursor: pointer;
}
div.autosuggest-error>div>div>div>div:after {
    transform: scaleX(1);
    border-bottom-color: #f44336;
}
div.autosuggest-warning>div>div>div>div:after {
    transform: scaleX(1);
    border-bottom-color: #f0ad4e;
}
p.error-message {
    color: #f44336;
    margin: 0;
    font-size: 0.75rem;
    margin-top: 8px;
    min-height: 1em;
    line-height: 1em;
}
p.warning-message {
    color: #f0ad4e;
    margin: 0;
    font-size: 0.75rem;
    margin-top: 8px;
    min-height: 1em;
    line-height: 1em;
}
.disabled-text-field input {
    color: #000;
}
.label-font-14 label {
    font-size: 14px;
}
.gray-italic-subheading {
    margin-left: 10px;
    font-size: 13px;
    font-weight: normal;
    color: gray;
    font-style: italic;
}
.padding-0 {
    padding: 0;
}
.full-width {
    width: 100%;
}
.chip-label {
    text-align: center;
    span {
        white-space: normal;
    }
}
div.freight-slot-load-details {
    margin-top: -10px;
    border-bottom: 1px solid lightgray;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    div.item {
        text-align: center;
        border-right: 1px solid lightgray;
        padding: 10px;
    }
    div.item-last {
        border-right: none;
    }
    div.label {
        color: #000;
        font-weight: normal;
        width: auto !important;
    }
}

.padding-left-20 {
    padding-left: 20px !important;
}
.padding-right-20 {
    padding-right: 20px !important;
}
.inline-loader-container {
    display: flex;
    flex-direction: column;
    height: 220px;
    width: 100%;
    img {
        width: 10%;
        margin: auto;
    }
}
.unselected-subtab {
    font-weight: 400 !important;
    background-color: rgb(240,243,246) !important;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.26), 0 2px 10px 0 rgba(0,0,0,0.16);
}
.timezones.dropdown {
    width: 150px;
}
select:focus {
    outline: none;
}
div#nested-actions-menu-container {
    right: 8px;
    button {
        border-top-left-radius: 0px !important;
        border-bottom-left-radius: 0px !important;
        border-left: 1px solid rgb(224, 224, 224);
        border-top: none;
        border-right: none;
        border-bottom: none;

    }
    position: absolute;
}

span.slot-stats {
    ul {
        z-index: 100;
    }
    li.slot-status-checkbox {
        cursor: pointer;
        svg {
            width: 20px;
        }
        input {
            margin: 2px 0 0;
        }
        span:last-child {
            margin-left: -5px;
        }
    }
    li.slot-status-checkbox > span {
        padding: 6px 0 6px 10px;
    }
}

li.timezone-item-container {
    &.selected {
        border-left: 4px solid #3BB820;
    }
}

.negative-number {
    color: #ef6030;
}
.postive-number {
    color: rgb(106,174,32);
}
div.chip-items {
    .chip-item {
        width: 100%;
        display: inline-block;
        margin: 8px 5px;
        font-size: 10px;
        margin-right: 5px;
        display: flex;
        max-width: 200px;
        span.chip-label {
            padding: 5px;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            border: 1px solid;
        }
        span.chip-value {
            border: 1px solid;
            border-left: none;
            padding: 5px;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            text-wrap: nowrap;
        }
    }
}
input#calendar-search-input {
    padding: 10px;
}
div:focus {
    outline: unset !important;
}
div#calendar-menu-list {
    input#calendar-search-input {
        margin-bottom: 10px;
        &:focus {
            outline-color: $colorGreen;
        }
    }
    ul {
        padding: 8px 10px !important;
    }
}
.grade-distribution-container {
    min-height: 150px;
    max-height: 250px;
    overflow: auto;
    font-size: 14px;
    display: flex;
    border-top: 1px solid lightgray;
    border-bottom: 1px solid lightgray;
    margin-top: 15px;
    margin-bottom: 20px;
    margin-right: -20px;
    border-right: 1px solid lightgray;
    margin-left: 5px;
    div.grade-container {
        width: 14.3%;
    }
}
.stocks-filters {
    display: flex !important;
    padding: 5px 15px;
    align-items: center;
    span.control {
        padding: 5px;
        display: inline-block;
        width: 30%;
    }
    .stock-main-filters {
        width: 70%;
        display: inline-block;
        padding-right: 20px;
    }
    .stock-extra-filters {
        width: 90%;
        display: inline-block;
        padding-right: 20px;
        span.control {
            padding: 5px;
            display: inline-block;
            width: 30%;
        }
    }
    .stock-actions {
        width: 15%;
        display: inline-flex;
        flex-direction: row;
        .control {
            padding: 5px;
            width: auto;
        }
    }
}
div#map-search-box {
    min-height: 500px;
}
input.input-dense {
    padding: 9px;
}
.mui-select {
    div:first-child {
        color: $colorBlack;
        -webkit-text-fill-color: $colorBlack;
    }
}
.black-text {
    color: $colorBlack !important;
    -webkit-text-fill-color: $colorBlack !important;
}
.height-100 {
    height: 100%;
}
.months-selector-table-header {
    display: inline-flex;
    min-width: 300px;
    height: 40px;
    margin-top: -15px;
    padding: 10px 10px 10px 0px;
}

.list-height-restricted {
    max-height: 250px;
}
.no-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.order-tonnage-distribution {
    display: inline-block;
    max-width: 500px;
    max-height: 400px;
    text-align: left;
    padding-left: 15px;
    div.tonnage-info {
        color: #fff;
        span.tonnage-label {
            padding-top: 2px;
            font-size: 12px;
            display: inherit;
        }
    }
}
#launcher-frame {
    left: 40px !important;
    right: auto !important;
    z-index: 9999;
    bottom: -8px !important;
    max-width: 100px !important;
}

#freshworks-frame-wrapper {
    left: 60px !important;
    right: auto !important;
    bottom: 50px !important;
}
