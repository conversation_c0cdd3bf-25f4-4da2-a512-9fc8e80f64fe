import React from 'react';

import { connect } from 'react-redux';
import isEqual from 'lodash/isEqual';
import CurrentLevelsTable from '../../containers/CurrentLevelsTable';
import {setBreadcrumbs} from '../../actions/main';

class CurrentLevels extends React.Component {
  componentDidMount() {
    const breadcrumbs = this.props.originalBreadcrumbs;
    if(breadcrumbs && !isEqual(this.props.breadcrumbs, breadcrumbs)) {
      this.props.setBreadcrumbs(breadcrumbs);
    }
  }

  render() {
    return (
      <CurrentLevelsTable ignoreOrder={true} />
    );
  }
}

const mapStateToProps = state => {
  return {
    breadcrumbs: state.main.breadcrumbs,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    setBreadcrumbs: (breadcrumbs) => dispatch(setBreadcrumbs(breadcrumbs)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(CurrentLevels);
