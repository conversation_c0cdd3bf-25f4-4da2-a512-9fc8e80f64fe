import React from 'react'
import Button from '@mui/material/Button'
import ButtonGroup from '@mui/material/ButtonGroup'
import Tooltip from '@mui/material/Tooltip'
import Badge from '@mui/material/Badge'
import Menu from '@mui/material/Menu'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemText from '@mui/material/ListItemText'
import ActionIcon from '@mui/icons-material/MoreVert';
import FilterIcon from '@mui/icons-material/FilterAlt';
import ReportIcon from '@mui/icons-material/CloudDownload';
import UploadIcon from '@mui/icons-material/CloudUpload';
import DiffIcon from '@mui/icons-material/Difference';
import HistoryIcon from '@mui/icons-material/History';
import orderBy from 'lodash/orderBy'
import map from 'lodash/map'
import get from 'lodash/get'
import omit from 'lodash/omit'
import AddButton from './AddButton'

const GROUP_TYPES = ['filter', 'report', 'upload', 'action', 'history', 'difference']

const getOrderedControlsByType = (controls, type) => orderBy(controls.filter(control => control.type === type), 'order')


const ButtonGroupControl = ({control, title, icon, size}) => {
  const [showOptions, setShowOptions] = React.useState(false)
  const handleOptionClick = val => {
    val.fx()
    setShowOptions(false)
  }
  const buttonProps = omit(control.props, ['title'])

  return (
    <>
      {
        control?.props.component ? control.props.component : (
          <Tooltip title={title || control.props.title}>
            <Button
              size={size}
              {...buttonProps}
              onClick={control.props?.showMenus ? event => setShowOptions(event.currentTarget) : control.props.defaultHandler || control.props.onClick}
              sx={{'.MuiButton-startIcon': {marginRight: '-4px'}, ...control.props.sx}}
              startIcon={icon}
            />
          </Tooltip>
        )
      }
      {
        Boolean(showOptions) &&
          <Menu
            id="actions-menu"
            anchorEl={showOptions}
            open={Boolean(showOptions)}
            onClose={() => setShowOptions(false)}
          >
            {
              control.props.optionMapper?.map((val, index) => (
                get(val, 'enableOption', true) &&
                  get(val, 'name') &&
                  <ListItemButton key={index} onClick={() => handleOptionClick(val)} sx={{padding: '4px 12px'}}>
                    <ListItemText
                      sx={{
                        marginTop: '2px',
                        marginBottom: '2px',
                        '.MuiListItemText-primary': {fontSize: '14px'},
                        '.MuiListItemText-secondary': {fontSize: '12px'}
                      }}
                      primary={val.name}
                      secondary={val.description}
                    />
                  </ListItemButton>
              ))
            }
          </Menu>
      }
    </>
  )
}

const ListingControls = ({ controls, buttonGroupSx, sx, size }) => {
  const visibleControls = controls?.filter(control => !control.hidden) || []
  const newControls = getOrderedControlsByType(visibleControls, 'new')
  const customControls = getOrderedControlsByType(visibleControls, 'custom')
  const filterControls = getOrderedControlsByType(visibleControls, 'filter')
  const historyControls = getOrderedControlsByType(visibleControls, 'history')
  const differenceControls = getOrderedControlsByType(visibleControls, 'difference')
  const reportControls = getOrderedControlsByType(visibleControls, 'report')
  const actionControls = getOrderedControlsByType(visibleControls, 'action')
  const uploadControls = getOrderedControlsByType(visibleControls, 'upload')
  const groupControls = visibleControls.filter(control => GROUP_TYPES.includes(control.type))

  const getIcon = control => {
    const { type } = control
    if(type === 'filter')
      return <Badge badgeContent={control.props.applied || 0} color="secondary">
               <FilterIcon fontSize='inherit' />
             </Badge>
    if(type === 'difference')
      return <DiffIcon fontSize='inherit' />
    if(type === 'history')
      return <HistoryIcon fontSize='inherit' />
    if(type === 'report')
      return <ReportIcon fontSize='inherit' />
    if(type === 'upload')
      return <UploadIcon fontSize='inherit' />
    if(type === 'action')
      return <ActionIcon fontSize='inherit' />

  }

  return (
    <span style={{display: 'flex', ...sx}}>
      {
        newControls.map(control => {
          return control?.component ? control.component : (
            <AddButton size={size} key={control.props.label} sx={{ margin: '0 4px', textTransform: 'none' }} {...control.props} />
          )
        })
      }
      {
        customControls.map(control => {
          return control?.component ? control.component : (
            <Button size={size} key={control.props.label} sx={{ margin: '0 4px', textTransform: 'none' }} {...control.props}>
              {control.props.label}
            </Button>
          )
        })
      }
      {
        groupControls.length > 0 &&
          <ButtonGroup size={size} variant='outlined' color='secondary' sx={{marginLeft: '4px', ...buttonGroupSx}}>
            <>
              {
                map(filterControls, (control, index) => (
                  <ButtonGroupControl
                    size={size}
                    key={index}
                    control={control}
                    title="Apply Filters"
                    icon={getIcon(control)}
                  />
                ))
              }
              {
                map(differenceControls, (control, index) => (
                  <ButtonGroupControl
                    size={size}
                    key={index}
                    control={control}
                    icon={getIcon(control)}
                  />
                ))
              }
              {
                map(historyControls, (control, index) => (
                  <ButtonGroupControl
                    size={size}
                    key={index}
                    control={control}
                    icon={getIcon(control)}
                  />
                ))
              }
              {
                map(uploadControls, (control, index) => (
                  <ButtonGroupControl
                    size={size}
                    key={index}
                    control={control}
                    title={control.props.title}
                    icon={getIcon(control)}
                  />
                ))
              }
              {
                map(reportControls, (control, index) => (
                  <ButtonGroupControl
                    size={size}
                    key={index}
                    control={control}
                    title="Download contents of this table in a CSV"
                    icon={getIcon(control)}
                  />
                ))
              }
              {
                map(actionControls, (control, index) => (
                  <ButtonGroupControl
                    size={size}
                    key={index}
                    control={control}
                    title="More Actions"
                    icon={getIcon(control)}
                  />
                ))
              }
            </>
          </ButtonGroup>
      }
    </span>
  )
}

export default ListingControls;
