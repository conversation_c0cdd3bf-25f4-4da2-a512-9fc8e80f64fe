.party-row {
    margin: 5px 0;
    display: inline-flex;
    justify-content: center;
    vertical-align: middle;
    .contact-selector-container input {
        min-width: 70px;
    }
    .contact-selector-container div div div {
        padding-right: 10px !important;
    }
    .chip-inline-ellipsis {
        margin: 1px;
        font-size: 0.75rem !important;
        height: 20px !important;
        max-width: 335px;
        &.error {
            background: rgb(227,71,123);
            color: #fff;
            svg {
                color: #fff;
            }
        }
        span {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: inline-block;

        }
        svg {
            width: 16px;
        }
    }
    span.more-label {
        background-color: #e0e0e0;
        padding: 0 4px;
        border-radius: 3px;
        font-size: 0.75rem;
        &.error {
            border: 1px solid rgb(227,71,123);
            color: rgb(227,71,123);
        }

    }
}
.contact-list-item {
    padding: 0 !important;
    .contact-avatar {
        padding-left: 8px;
        margin-right: 10px;
        margin-top: -10px;
    }
    div.list-item-text {
        margin: 6px 0;
        flex: 1 1 auto;
        text-align: left;
        span.primary {
            display: block;
            margin: 3px 0 0 0;
            font-size: 1rem;
            line-height: 0.5;
            letter-spacing: 0.00938em;
        }
        p.secondary {
            display: block;
            margin: 0;
            color: rgba(0, 0, 0, 0.54);
            font-size: 0.875rem;
            line-height: 0.43;
            letter-spacing: 0.01071em;
            margin-block-start: 1em;
            margin-block-end: 1em;
            margin-inline-start: 0px;
            margin-inline-end: 0px;
        }
    }
}
