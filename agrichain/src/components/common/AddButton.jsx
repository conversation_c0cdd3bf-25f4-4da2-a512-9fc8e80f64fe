import React from 'react';
import { connect } from 'react-redux';

import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import { canCreateOrUpdate } from '../../common/utils';
import { AppContext } from '../main/LayoutContext';

class AddButton extends React.Component {
  static contextType = AppContext

  render() {
    const {
      app, user, id, variant, onClick, color, disabled, style, label, containerStyle,
      tooltipTitle, tooltipPlacement, href, sx
    } = this.props;
    const { isMobileDevice } = this.context

    let canCreate = true;
    if(app && user)
      canCreate = canCreateOrUpdate(user, app);

    const button = (
        <Button
          id={id}
          variant={variant || "contained"}
          type="button"
          onClick={onClick}
          href={href}
          color={color || "primary"}
          disabled={disabled}
          className='common-add-button'
          sx={{float: 'right', marginTop: '0px', marginBottom: '0px', ...style, ...sx}} >
          <AddCircleIcon style={{paddingRight: '5px'}} />
          { label }
        </Button>
    );

    if (!canCreate || isMobileDevice) return null;
      const content = tooltipTitle
        ? (
          <Tooltip title={tooltipTitle} placement={tooltipPlacement || "left"} arrow>
            {button}
          </Tooltip>
        )
        : button;
      return (
        <span style={containerStyle || {}}>
          {content}
        </span>
      );
  }
}

const mapStateToProps = state => ({
  user: state.main.user.user,
});

export default connect(mapStateToProps)(AddButton);
