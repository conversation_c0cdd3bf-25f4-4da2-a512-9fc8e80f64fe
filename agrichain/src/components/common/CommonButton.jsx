import React from 'react';

import { Button, Tooltip } from '@mui/material';
import { merge, has, omit, includes } from 'lodash';

const CommonButton = props => {
  let color = props.color || 'secondary';
  if(has(props, 'primary'))
    color = 'primary';
  let variant = includes(['flat', 'raised'], props.variant) ? 'contained' : props.variant;
  const button = (
    <Button
      className='common-button'
      type={props.type || "button"}
      onClick={props.onClick}
      disabled={props.disabled}
      style={merge({margin: '4px', minWidth: '100px', textTransform: 'none'}, props.style || {})}
      {...omit(props, ['primary', 'secondary', 'variant'])}
      variant={variant || 'contained'}
      color={color}
      startIcon={props.startIcon}
      endIcon={props.endIcon}
    >
      {props.label}
    </Button>)
  return props.tooltipTitle ? (
        <Tooltip title={props.tooltipTitle} placement={props.tooltipPlacement || 'top'} arrow>
            {button}
          </Tooltip>
      ) : button;
};

export default CommonButton;
