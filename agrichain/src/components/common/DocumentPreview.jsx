import React from 'react';
import connect from "react-redux/es/connect/connect";
import {withRouter} from "react-router-dom";
import get from 'lodash/get';

class DocumentPreview extends React.Component {
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    if (!this.props.previewHtml && this.props.previewData)
      this.props.dispatch(this.props.fetchAction(this.props.previewData));
  }

  render() {
    const { previewHtml, className } = this.props;
    const separatorStyle = `
      <style>
        .${className} hr {
          border: 2px solid #8a8b8a !important;
          margin-top: 10px !important;
          border-top: unset !important;
          margin-bottom: 10px !important;
        }
      </style>
    `;

    const htmlWithStyle = separatorStyle + (previewHtml || '');

    return (
      <div className={className} dangerouslySetInnerHTML={{__html: htmlWithStyle}}/>
    );
  }
}

DocumentPreview.defaultProps = {
  className: "document-preview",
};


const mapStateToProps = (state, ownProps) => {
  const previewHtml = get(state, ownProps.statePath);

  return {
    previewHtml,
    ...ownProps
  };
};

export default withRouter(connect(mapStateToProps)(DocumentPreview));
