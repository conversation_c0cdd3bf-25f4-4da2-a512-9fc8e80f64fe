import React, {Component} from 'react';

import SvgIcon from '@mui/material/SvgIcon';

class InProgress extends Component {
  render() {
    const color = this.props.color || '#000';
    return (
      <SvgIcon {...this.props}>
          <path fill={color} d="M12.667 3.333L10 6h2c0 2.207-1.793 4-4 4a3.914 3.914 0 0 1-1.867-.467l-.973.974c.82.52 1.793.826 2.84.826A5.332 5.332 0 0 0 13.333 6h2l-2.666-2.667zM4 6c0-2.207 1.793-4 4-4 .673 0 1.313.167 1.867.467l.973-.974A5.287 5.287 0 0 0 8 .667 5.332 5.332 0 0 0 2.667 6h-2l2.666 2.667L6 6H4z"/>
      </SvgIcon>
    );
  }
}

export default InProgress;
