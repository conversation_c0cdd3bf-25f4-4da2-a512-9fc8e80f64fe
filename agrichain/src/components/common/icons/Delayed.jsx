import React, {Component} from 'react';

import SvgIcon from '@mui/material/SvgIcon';

class Delayed extends Component {
  render() {
    const color = this.props.color || '#000';
    return (
      <SvgIcon {...this.props}>
        <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g transform="translate(-333.000000, -573.000000)">
            <g id="ic_rotate_right-copy" transform="translate(331.000000, 573.000000)">
              <g id="Icon-24px">
                <polygon id="Shape" points="0 0 16 0 16 16 0 16"></polygon>
                <path d="M10.3666667,3.7 L7.33333333,0.666666667 L7.33333333,2.71333333 C4.70666667,3.04 2.66666667,5.28 2.66666667,8 C2.66666667,10.72 4.7,12.96 7.33333333,13.2866667 L7.33333333,11.94 C5.44,11.62 4,9.98 4,8 C4,6.02 5.44,4.38 7.33333333,4.06 L7.33333333,6.66666667 L10.3666667,3.7 L10.3666667,3.7 Z M13.2866667,7.33333333 C13.1733333,6.40666667 12.8066667,5.51333333 12.2066667,4.74 L11.26,5.68666667 C11.62,6.18666667 11.8466667,6.75333333 11.94,7.33333333 L13.2866667,7.33333333 L13.2866667,7.33333333 Z M8.66666667,11.9333333 L8.66666667,13.28 C9.59333333,13.1666667 10.4933333,12.8066667 11.2666667,12.2066667 L10.3066667,11.2466667 C9.80666667,11.6066667 9.24666667,11.84 8.66666667,11.9333333 L8.66666667,11.9333333 Z M11.26,10.32 L12.2066667,11.26 C12.8066667,10.4866667 13.1733333,9.59333333 13.2866667,8.66666667 L11.94,8.66666667 C11.8466667,9.24666667 11.62,9.81333333 11.26,10.32 L11.26,10.32 Z" id="Shape" fill={color}></path>
              </g>
            </g>
          </g>
        </g>
      </SvgIcon>
    );
  }
}

export default Delayed;
