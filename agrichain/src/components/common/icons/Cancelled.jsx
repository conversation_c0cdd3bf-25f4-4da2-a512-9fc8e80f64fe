import React, {Component} from 'react';

import SvgIcon from '@mui/material/SvgIcon';

class Cancelled extends Component {
  render() {
    const color = this.props.color || '#000';
    return (
      <SvgIcon {...this.props}>
        <g fill="none" fillRule="evenodd"> <path d="M-1-1h16v16H-1z"/> <path fill={color} d="M8.727 4.333L7 6.06 5.273 4.333l-.94.94L6.06 7 4.333 8.727l.94.94L7 7.94l1.727 1.727.94-.94L7.94 7l1.727-1.727-.94-.94zM7 .333A6.66 6.66 0 0 0 .333 7 6.66 6.66 0 0 0 7 13.667 6.66 6.66 0 0 0 13.667 7 6.66 6.66 0 0 0 7 .333zm0 12A5.34 5.34 0 0 1 1.667 7 5.34 5.34 0 0 1 7 1.667 5.34 5.34 0 0 1 12.333 7 5.34 5.34 0 0 1 7 12.333z"/> </g>
      </SvgIcon>
    );
  }
}

export default Cancelled;
