import React, {Component} from 'react';

import SvgIcon from '@mui/material/SvgIcon';

class Booked extends Component {
  render() {
    const color = this.props.color || '#000';
    return (
      <SvgIcon {...this.props}>
        <g fill="none" fillRule="evenodd"> <path fill={color} d="M6.993.333A6.663 6.663 0 0 0 .333 7c0 3.68 2.98 6.667 6.66 6.667A6.67 6.67 0 0 0 13.667 7 6.67 6.67 0 0 0 6.993.333zm.007 12A5.332 5.332 0 0 1 1.667 7 5.332 5.332 0 0 1 7 1.667 5.332 5.332 0 0 1 12.333 7 5.332 5.332 0 0 1 7 12.333z"/> <path d="M-1-1h16v16H-1z"/> <path fill={color} d="M7.333 3.667h-1v4l3.5 2.1.5-.82-3-1.78z"/> </g>
      </SvgIcon>
    );
  }
}

export default Booked;
