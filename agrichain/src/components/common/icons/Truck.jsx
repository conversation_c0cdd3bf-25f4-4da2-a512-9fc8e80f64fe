import React from 'react';
import Tooltip from '@mui/material/Tooltip';

const Truck = ({ fill, width, height }) => (
  <Tooltip title="Trucks" placement="right">
    <svg width={width || "56.7568px"} height={height || "54.5867px"} viewBox="0 0 56.7568 54.5867" version="1.1">
    <defs>
        <polygon id="path-1" points="0.15655 0.413 27.0763 0.413 27.0763 26.35 0.15655 26.35"></polygon>
        <polygon id="path-3" points="0.0002 0.1077 56.757 0.1077 56.757 36.9997 0.0002 36.9997"></polygon>
        <polygon id="path-5" points="0 55.5873 56.757 55.5873 56.757 1.0003 0 1.0003"></polygon>
    </defs>
    <g id="-Main" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Truck_delivery_icon" transform="translate(-0.0002, -1.0003)">
            <g id="Group-3" transform="translate(5, 0.5873)">
                <g id="Clip-2"></g>
              <path d="M26.4238,15.33 L25.8368,15.915 C24.9638,16.785 23.5468,16.785 22.6738,15.915 L16.2678,9.507 L16.2678,24.114 C16.2678,25.347 15.2638,26.35 14.0308,26.35 L13.2018,26.35 C11.9698,26.35 10.9658,25.347 10.9658,24.114 L10.9658,9.507 L4.5598,15.915 C3.6848,16.785 2.2658,16.786 1.3948,15.913 L0.8098,15.328 C-0.0612,14.455 -0.0612,13.038 0.8098,12.165 L11.9088,1.065 C12.3718,0.602 13.0028,0.371 13.5658,0.419 C13.6268,0.414 13.6868,0.412776205 13.7468,0.412776205 C14.3378,0.412776205 14.9038,0.645 15.3238,1.065 L26.4238,12.165 C27.2938,13.036 27.2938,14.455 26.4238,15.33" id="Fill-1" fill={fill}></path>
            </g>
          <path d="M3.7842,24.3704 L11.5912,24.3704 C11.8962,24.3704 12.1442,24.1224 12.1442,23.8164 L12.1442,23.0334 C12.1442,22.7264 11.8962,22.4784 11.5902,22.4784 L3.7842,22.4784 L3.7842,24.3704 Z" id="Fill-4" fill={fill}></path>
            <g id="Group-8" transform="translate(0, 18.5873)">
                <g id="Clip-7"></g>
              <path d="M54.8652,26.5947 L52.0272,26.5947 L52.0272,28.4867 L54.8652,28.4867 L54.8652,30.3777 L51.0012,30.3777 C50.5502,27.6977 48.2142,25.6487 45.4062,25.6487 C42.5972,25.6487 40.2612,27.6977 39.8102,30.3777 L37.8382,30.3777 L37.8382,8.6207 L46.7812,8.6207 L47.9972,10.5137 L40.6762,10.5137 C40.1532,10.5137 39.7302,10.9377 39.7302,11.4587 L39.7302,19.9727 C39.7302,20.4957 40.1532,20.9187 40.6762,20.9187 L54.6862,20.9187 L54.8652,21.1977 L54.8652,26.5947 Z M45.4062,35.1087 C43.3192,35.1087 41.6222,33.4107 41.6222,31.3237 C41.6222,29.2377 43.3192,27.5397 45.4062,27.5397 C47.4922,27.5397 49.1892,29.2377 49.1892,31.3237 C49.1892,33.4107 47.4922,35.1087 45.4062,35.1087 L45.4062,35.1087 Z M49.1892,12.4057 L49.1892,12.3677 L53.4702,19.0277 L41.6222,19.0277 L41.6222,12.4057 L49.1892,12.4057 Z M13.2432,35.1087 C11.1562,35.1087 9.4602,33.4107 9.4602,31.3237 C9.4602,29.2377 11.1562,27.5397 13.2432,27.5397 C15.3302,27.5397 17.0272,29.2377 17.0272,31.3237 C17.0272,33.4107 15.3302,35.1087 13.2432,35.1087 L13.2432,35.1087 Z M56.6072,20.4077 L48.0942,7.1637 C47.9192,6.8937 47.6192,6.7297 47.2982,6.7297 L37.8382,6.7297 L37.8382,1.0537 C37.8382,0.5317 37.4152,0.1077 36.8922,0.1077 L25.6622,0.1077 C25.3522,0.1077 25.1022,0.3587 25.1022,0.6667 L25.1022,1.4457 C25.1022,1.7517 25.3502,1.9997 25.6562,1.9997 L35.9462,1.9997 L35.9462,3.8917 L25.6612,3.8917 C25.3502,3.8917 25.0992,4.1447 25.1022,4.4547 L25.1092,5.2307 C25.1122,5.5357 25.3612,5.7827 25.6682,5.7827 L35.9462,5.7827 L35.9462,7.6757 L35.9462,26.5947 L19.8652,26.5947 L19.8652,28.4867 L35.9462,28.4867 L35.9462,30.3777 L18.8392,30.3777 C18.3862,27.6977 16.0512,25.6487 13.2432,25.6487 C10.4352,25.6487 8.0992,27.6977 7.6472,30.3777 L1.8912,30.3777 L1.8912,28.4867 L6.6222,28.4867 L6.6222,26.5947 L1.8912,26.5947 L1.8912,1.9997 L11.5832,1.9997 C11.8862,1.9997 12.1352,1.7547 12.1372,1.4497 L12.1442,0.6677 C12.1472,0.3597 11.8982,0.1077 11.5912,0.1077 L0.9462,0.1077 C0.4242,0.1077 0.0002,0.5317 0.0002,1.0537 L0.0002,31.3237 C0.0002,31.8467 0.4242,32.2707 0.9462,32.2707 L7.6472,32.2707 C8.0992,34.9507 10.4352,36.9997 13.2432,36.9997 C16.0512,36.9997 18.3862,34.9507 18.8392,32.2707 L36.8922,32.2707 L39.8102,32.2707 C40.2612,34.9507 42.5972,36.9997 45.4062,36.9997 C48.2142,36.9997 50.5502,34.9507 51.0012,32.2707 L55.8112,32.2707 C56.3352,32.2707 56.7582,31.8477 56.7582,31.3237 L56.7582,20.9187 C56.7582,20.7387 56.7052,20.5597 56.6072,20.4077 L56.6072,20.4077 Z" id="Fill-6" fill={fill}></path>
            </g>
            <g id="Clip-10"></g>
          <polygon id="Fill-9" fill={fill} points="12.298 50.8573 14.19 50.8573 14.19 48.9643 12.298 48.9643"></polygon>
          <polygon id="Fill-11" fill={fill} points="44.46 50.8573 46.352 50.8573 46.352 48.9643 44.46 48.9643"></polygon>
        </g>
    </g>
</svg>
  </Tooltip>
)

export default Truck;
