import React, {Component} from 'react';

import get from 'lodash/get';
import {BLACK} from '../../../common/constants';

class Calendar extends Component {
  render() {
    const width = get(this.props, 'width', '24');
    const fill = get(this.props, 'fill', BLACK);

    return <svg fill={fill} xmlns="http://www.w3.org/2000/svg" width={width} height={width} viewBox="0 0 24 24">
      <path d="M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"/>
      <path fill="none" d="M0 0h24v24H0z"/>
    </svg>;
  }
}

export default Calendar;
