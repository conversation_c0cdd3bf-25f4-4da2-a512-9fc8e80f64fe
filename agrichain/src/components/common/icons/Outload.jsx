import React from 'react';
import Tooltip from '@mui/material/Tooltip';

const Outload = ({ fill, height, width, title }) => (
  <Tooltip title={title || "Outloads"} placement="right">
    <svg width={width || "37.9764611px"} height={height || "47.8503607px"} viewBox="0 0 37.9764611 47.8503607" version="1.1">
      <defs>
        <polygon id="path-1" points="5.27777778e-05 0.239003279 37.9765139 0.239003279 37.9765139 35.147541 5.27777778e-05 35.147541"></polygon>
      </defs>
      <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Outload_icon" transform="translate(-0.0001, -0.0001)">
          <path d="M13.6505236,6.78963934 C14.0693153,7.07029508 14.6435375,7.00472131 15.0164125,6.63383607 L17.9455792,3.72236066 L17.9455792,14.024 C17.9455792,14.3411148 18.0870236,14.639082 18.3342875,14.8410492 C18.5269264,14.9989508 18.7660097,15.0828852 19.0127458,15.0828852 C19.0821486,15.082623 19.1520792,15.0763279 19.2214819,15.0626885 C19.7170653,14.9669508 20.0767458,14.5168525 20.0767458,13.9925246 L20.0767458,3.72445902 L22.9826903,6.62859016 C23.3980514,7.04170492 24.0741347,7.04118033 24.4894958,6.62859016 C24.9048569,6.2157377 24.9048569,5.5437377 24.4897597,5.13114754 L19.5761486,0.233311475 C19.4249403,0.0830163934 19.2241208,0.000131147541 19.0108986,0.000131147541 L19.0103708,0.000131147541 C18.7971486,0.000393442623 18.5960653,0.0832786885 18.4448569,0.233836066 L13.4871764,5.15842623 C13.2615514,5.38268852 13.1499264,5.69245902 13.1805375,6.00904918 C13.2111486,6.32537705 13.3824125,6.60970492 13.6505236,6.78963934" id="Fill-1" fill={fill}></path>
          <g id="Group-5" transform="translate(0, 12.703)">
            <g id="Clip-4"></g>
            <path d="M17.8634833,33.0295607 L17.8634833,9.21133115 L20.8208861,9.21133115 L34.1216778,9.21133115 L34.1216778,33.0295607 L22.9525806,33.0295607 L17.8634833,33.0295607 Z M15.7328444,33.0295607 L15.0235111,33.0295607 L3.90508056,33.0295607 L3.90508056,9.21133115 L15.7328444,9.21133115 L15.7328444,33.0295607 Z M16.9636222,6.5629377 C16.7736222,6.70405246 16.6200389,6.88582295 16.5073583,7.09303607 L5.84466389,7.09303607 L10.7200111,3.4592 C11.0759972,3.19349508 11.3076917,2.79165902 11.3628444,2.35703607 L12.705775,2.35703607 C12.7609278,2.79165902 12.9926222,3.19349508 13.3488722,3.4592 L17.2380667,6.35808525 L16.9636222,6.5629377 Z M24.6778861,3.4592 C25.0341361,3.19375738 25.2658306,2.79218361 25.3212472,2.35729836 L26.6641778,2.35729836 C26.7193306,2.79218361 26.951025,3.19375738 27.3070111,3.4592 L32.1823583,7.09329836 L20.823525,7.09329836 C20.8227333,7.09329836 20.8219417,7.09303607 20.8208861,7.09303607 L19.8035944,7.09303607 L24.6778861,3.4592 Z M36.9109833,33.0295607 L36.2525806,33.0295607 L36.2525806,8.15218361 C36.2525806,7.68136393 35.9459417,7.27716721 35.5055111,7.14156066 C35.391775,6.91336393 35.2263167,6.71506885 35.0218028,6.5629377 L28.7818861,1.91087213 L28.7818861,1.86942951 C28.7818861,0.970544262 28.0461639,0.239003279 27.1418167,0.239003279 L24.8433444,0.239003279 C24.4060806,0.239003279 23.99415,0.408970492 23.6840806,0.717167213 C23.3737472,1.02536393 23.2030111,1.43480656 23.203275,1.86942951 L23.203275,1.91113443 L19.013775,5.03454426 L14.8234833,1.91060984 L14.8234833,1.86916721 C14.8234833,0.970281967 14.0877611,0.239003279 13.1836778,0.239003279 L10.8852056,0.239003279 C10.4474139,0.239003279 10.0357472,0.408970492 9.72567778,0.717167213 C9.41534444,1.02536393 9.24487222,1.43454426 9.24513611,1.86916721 L9.24513611,1.91113443 L3.00495556,6.56267541 C2.80044167,6.71559344 2.63498333,6.91388852 2.52124722,7.14129836 C2.08081667,7.27690492 1.77444167,7.68110164 1.77444167,8.15192131 L1.77444167,33.0295607 L1.06537222,33.0295607 C0.477691667,33.0295607 5.27777778e-05,33.504577 5.27777778e-05,34.0884459 C5.27777778e-05,34.6723148 0.477691667,35.1475934 1.06537222,35.1475934 L15.0211361,35.1475934 C15.0219278,35.1475934 15.0227194,35.1475934 15.0235111,35.1475934 L36.9109833,35.1475934 C37.4984,35.1475934 37.9765667,34.672577 37.9765667,34.0887082 C37.9765667,33.5048393 37.4984,33.0295607 36.9109833,33.0295607 L36.9109833,33.0295607 Z" id="Fill-3" fill={fill}></path>
          </g>
          <path d="M31.8769069,23.0921443 C31.2892264,23.0921443 30.8113236,23.567423 30.8113236,24.1510295 L30.8113236,25.2988328 C30.8113236,25.8829639 31.2892264,26.3579803 31.8769069,26.3579803 C32.4643236,26.3579803 32.9422264,25.8829639 32.9422264,25.2988328 L32.9422264,24.1510295 C32.9422264,23.567423 32.4643236,23.0921443 31.8769069,23.0921443" id="Fill-6" fill={fill}></path>
          <path d="M31.8769069,26.8739148 C31.2892264,26.8739148 30.8113236,27.3491934 30.8113236,27.9328 L30.8113236,33.7153574 C30.8113236,34.2994885 31.2892264,34.7745049 31.8769069,34.7745049 C32.4643236,34.7745049 32.9422264,34.2994885 32.9422264,33.7153574 L32.9422264,27.9328 C32.9422264,27.3491934 32.4643236,26.8739148 31.8769069,26.8739148" id="Fill-8" fill={fill}></path>
        </g>
      </g>
    </svg>
  </Tooltip>
)

export default Outload;
