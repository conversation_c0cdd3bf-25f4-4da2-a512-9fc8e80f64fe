import React, {Component} from 'react';

import Tooltip from '@mui/material/Tooltip';

class Help extends Component {
  render() {
    return <Tooltip title="Help" placement="right">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
        <g fill="none" fillRule="evenodd">
          <path d="M0 0h24v24H0z"/>
          <circle cx="12" cy="18" r="1" fill="#FFF"/>
          <path fill="#FFF" d="M12 6C9.794 6 8 7.709 8 9.81c0 .526.448.952 1 .952s1-.426 1-.952c0-1.05.897-1.905 2-1.905s2 .854 2 1.905c0 1.05-.897 1.904-2 1.904-.552 0-1 .427-1 .953v2.38c0 .527.448.953 1 .953s1-.426 1-.952v-1.55c1.723-.423 3-1.917 3-3.688C16 7.709 14.206 6 12 6z"/>
          <circle cx="12" cy="12" r="11" stroke="#FFF" strokeWidth="2"/>
        </g>
      </svg>
    </Tooltip>;
  }
}

export default Help;
