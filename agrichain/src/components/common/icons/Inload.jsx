import React from 'react';
import Tooltip from '@mui/material/Tooltip';

const Inload = ({ fill, height, width, title }) => (
  <Tooltip title={title || "Inloads"} placement="right">
    <svg width={width || "37.5003039px"} height={height || "47.8730973px"} viewBox="0 0 37.5003039 47.8730973" version="1.1">
      <defs>
        <polygon id="path-1" points="0.000130290368 0.239003279 37.5004342 0.239003279 37.5004342 35.147541 0.000130290368 35.147541"></polygon>
      </defs>
      <g id="Page-1" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Inload_icon" transform="translate(-0.0001, -0.0002)">
          <path d="M18.1699559,14.8493377 C18.3190081,14.9998951 18.5170494,15.0830426 18.7278593,15.0830426 L18.728641,15.0830426 C18.9389296,15.0827803 19.1372316,14.9998951 19.2870655,14.8493377 L24.1823352,9.92474754 C24.4051317,9.70048525 24.515618,9.39045246 24.48513,9.0736 C24.4549026,8.75753443 24.2857857,8.47294426 24.0212963,8.29353443 C23.6077547,8.01314098 23.0409916,8.07845246 22.6725304,8.4493377 L19.7798237,11.3608131 L19.7798237,1.05917377 C19.7798237,0.742059016 19.6398918,0.444091803 19.3959883,0.241862295 C19.1526059,0.040157377 18.8339156,-0.0414163934 18.5201764,0.0204852459 C18.0308058,0.116222951 17.6756342,0.566059016 17.6756342,1.09038689 L17.6756342,11.3581902 L14.8061192,8.45458361 C14.3957045,8.04120656 13.7280967,8.04173115 13.3182032,8.45458361 C13.1196407,8.65445246 13.0101968,8.92041967 13.0101968,9.20343607 C13.0101968,9.48671475 13.1196407,9.75241967 13.3179426,9.95176393 C13.3179426,9.95176393 16.9741509,13.6430426 18.1699559,14.8493377" id="Fill-1" fill={fill}></path>
          <g id="Group-5" transform="translate(0, 12.7258)">
            <g id="Clip-4"></g>
            <path d="M17.639622,33.0295607 L17.639622,9.21133115 L20.5599503,9.21133115 L33.6940011,9.21133115 L33.6940011,33.0295607 L22.6649215,33.0295607 L17.639622,33.0295607 Z M15.5356931,33.0295607 L14.8352521,33.0295607 L3.85620401,33.0295607 L3.85620401,9.21133115 L15.5356931,9.21133115 L15.5356931,33.0295607 Z M16.7510417,6.5629377 C16.5634236,6.70405246 16.411505,6.88582295 16.3004976,7.09303607 L5.77147241,7.09303607 L10.5857015,3.4592 C10.9372249,3.19349508 11.1660148,2.79165902 11.2204762,2.35703607 L12.5465715,2.35703607 C12.6010329,2.79165902 12.8298228,3.19349508 13.1816068,3.4592 L17.0220456,6.35808525 L16.7510417,6.5629377 Z M24.3685983,3.4592 C24.7203823,3.19375738 24.9491722,2.79218361 25.0038941,2.35729836 L26.3299895,2.35729836 C26.3844509,2.79218361 26.6132408,3.19375738 26.9647642,3.4592 L31.7789932,7.09329836 L20.5625561,7.09329836 C20.5615138,7.09329836 20.560732,7.09303607 20.5599503,7.09303607 L19.555151,7.09303607 L24.3685983,3.4592 Z M36.4483394,33.0295607 L35.7981905,33.0295607 L35.7981905,8.15218361 C35.7981905,7.68136393 35.4953957,7.27716721 35.0604864,7.14156066 C34.9481761,6.91336393 34.784792,6.71506885 34.582842,6.5629377 L28.4211499,1.91087213 L28.4211499,1.86942951 C28.4211499,0.970544262 27.6946508,0.239003279 26.8016406,0.239003279 L24.5319824,0.239003279 C24.1002002,0.239003279 23.6934336,0.408970492 23.3872513,0.717167213 C23.0808083,1.02536393 22.9122126,1.43480656 22.9124732,1.86942951 L22.9124732,1.91113443 L18.7754934,5.03454426 L14.6377319,1.91060984 L14.6377319,1.86916721 C14.6377319,0.970281967 13.9112328,0.239003279 13.0184832,0.239003279 L10.748825,0.239003279 C10.3165216,0.239003279 9.91001564,0.408970492 9.60383328,0.717167213 C9.29739034,1.02536393 9.12905518,1.43454426 9.12931576,1.86916721 L9.12931576,1.91113443 L2.96736312,6.56267541 C2.76541305,6.71559344 2.60202893,6.91388852 2.48971863,7.14129836 C2.05480939,7.27690492 1.75227515,7.68110164 1.75227515,8.15192131 L1.75227515,33.0295607 L1.05209472,33.0295607 C0.471781421,33.0295607 0.000130290368,33.504577 0.000130290368,34.0884459 C0.000130290368,34.6723148 0.471781421,35.1475934 1.05209472,35.1475934 L14.8329069,35.1475934 C14.8334281,35.1475934 14.8342098,35.1475934 14.8352521,35.1475934 L36.4483394,35.1475934 C37.0283922,35.1475934 37.5005644,34.672577 37.5005644,34.0887082 C37.5005644,33.5048393 37.0283922,33.0295607 36.4483394,33.0295607 L36.4483394,33.0295607 Z" id="Fill-3" fill={fill}></path>
          </g>
          <path d="M31.477371,23.1149639 C30.8970578,23.1149639 30.425146,23.5902426 30.425146,24.1738492 L30.425146,25.3216525 C30.425146,25.9057836 30.8970578,26.3808 31.477371,26.3808 C32.0574238,26.3808 32.5293355,25.9057836 32.5293355,25.3216525 L32.5293355,24.1738492 C32.5293355,23.5902426 32.0574238,23.1149639 31.477371,23.1149639" id="Fill-6" fill={fill}></path>
          <path d="M31.477371,26.8967344 C30.8970578,26.8967344 30.425146,27.3720131 30.425146,27.9556197 L30.425146,33.738177 C30.425146,34.3223082 30.8970578,34.7973246 31.477371,34.7973246 C32.0574238,34.7973246 32.5293355,34.3223082 32.5293355,33.738177 L32.5293355,27.9556197 C32.5293355,27.3720131 32.0574238,26.8967344 31.477371,26.8967344" id="Fill-8" fill={fill}></path>
        </g>
      </g>
    </svg>
  </Tooltip>
)

export default Inload;
