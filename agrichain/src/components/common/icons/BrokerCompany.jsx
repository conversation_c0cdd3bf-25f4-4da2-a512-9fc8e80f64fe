import React, {Component} from 'react';


class BrokerCompany extends Component {
  render() {
    return (
      <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90">
        <g fillRule="evenodd">
          <path fill={this.props.fill} d="M44.956 62.75a1.77 1.77 0 0 0 1.242-.513c.326-.326.514-.778.514-1.238 0-.461-.188-.913-.514-1.239a1.77 1.77 0 0 0-1.242-.513 1.77 1.77 0 0 0-1.241.513 1.763 1.763 0 0 0-.515 1.239c0 .46.188.912.515 1.238.326.326.78.513 1.241.513"/>
          <g transform="translate(86.268 24.434)">
            <mask id="b" fill="#fff">
              <path id="a" d="M.132.055h3.512v3.503H.132z"/>
            </mask>
            <path fill={this.props.fill} d="M1.888.055A1.77 1.77 0 0 0 .646.568a1.763 1.763 0 0 0-.514 1.238c0 .461.188.913.514 1.239.327.326.78.513 1.242.513a1.77 1.77 0 0 0 1.241-.513c.327-.326.515-.778.515-1.239 0-.46-.188-.912-.515-1.238A1.772 1.772 0 0 0 1.888.055" mask="url(#b)"/>
          </g>
          <g transform="translate(0 .127)">
            <mask id="d" fill="#fff">
              <path id="c" d="M0 .092h89.912V89.78H0z"/>
            </mask>
            <path fill={this.props.fill} d="M86.4 56.329v7.348c0 .96-.788 1.742-1.756 1.742H5.268a1.751 1.751 0 0 1-1.756-1.742V56.33H86.4zM39.015 78.394V69.11h11.882v9.284H39.015zm20.5 7.884H30.397a6.156 6.156 0 0 1 5.89-4.38h17.335c2.78 0 5.133 1.85 5.891 4.38zM84.643.092H5.268C2.363.092 0 2.445 0 5.337v58.34c0 2.892 2.363 5.245 5.268 5.245h30.235v9.507c-4.96.4-8.873 4.552-8.873 9.6 0 .968.786 1.752 1.756 1.752h33.14c.97 0 1.756-.784 1.756-1.752 0-5.048-3.914-9.2-8.873-9.6v-9.507h30.235c2.905 0 5.268-2.353 5.268-5.245V33.435c0-.967-.786-1.752-1.756-1.752s-1.756.785-1.756 1.752v19.39H3.512V5.337c0-.96.788-1.742 1.756-1.742h79.376A1.75 1.75 0 0 1 86.4 5.337v11.741c0 .967.786 1.752 1.756 1.752s1.756-.785 1.756-1.752V5.337c0-2.892-2.363-5.245-5.268-5.245z" mask="url(#d)"/>
          </g>
          <path fill={this.props.fill} d="M41.13 39.584c.97 0 1.756-.784 1.756-1.752v-4.98c0-.967-.787-1.752-1.756-1.752h-4.993c-.97 0-1.756.785-1.756 1.752 0 .968.786 1.752 1.756 1.752h.753l-7.9 7.881-6.485-6.468a1.76 1.76 0 0 0-2.484 0l-10.024 10a1.753 1.753 0 1 0 1.242 2.99c.45 0 .899-.17 1.242-.513l8.782-8.76 6.485 6.468a1.758 1.758 0 0 0 2.483 0l9.142-9.12v.75c0 .968.787 1.752 1.757 1.752M65.385 39.547c0-.967-.786-1.751-1.756-1.751H51.981c-.97 0-1.757.784-1.757 1.751 0 .968.787 1.752 1.757 1.752h11.648c.97 0 1.756-.784 1.756-1.752M78.673 37.796h-6.497c-.97 0-1.756.784-1.756 1.751 0 .968.786 1.752 1.756 1.752h6.497c.97 0 1.756-.784 1.756-1.752 0-.967-.786-1.751-1.756-1.751M50.739 46.017a1.762 1.762 0 0 0-.515 1.238c0 .461.188.913.515 1.239.327.326.78.513 1.242.513a1.77 1.77 0 0 0 1.241-.513c.327-.326.515-.778.515-1.239 0-.46-.188-.912-.515-1.238a1.772 1.772 0 0 0-1.241-.514c-.462 0-.915.188-1.242.514M59.532 49.007h19.141c.97 0 1.756-.784 1.756-1.752 0-.967-.786-1.752-1.756-1.752H59.532c-.97 0-1.756.785-1.756 1.752 0 .968.786 1.752 1.756 1.752M60.958 13.359v8.582c0 .968.786 1.752 1.756 1.752h8.603a8.79 8.79 0 0 1-8.603 7.008c-4.842 0-8.78-3.93-8.78-8.76 0-4.23 3.02-7.768 7.024-8.582zm3.512 0a8.796 8.796 0 0 1 6.847 6.83H64.47v-6.83zm-1.756 20.845c6.778 0 12.292-5.5 12.292-12.263 0-6.761-5.514-12.262-12.292-12.262S50.42 15.179 50.42 21.94c0 6.762 5.515 12.263 12.293 12.263zM23.83 23.984c-1.8.203-5.459.192-8.221-2.564-2.763-2.756-2.747-6.387-2.534-8.16 1.776-.21 5.432-.223 8.185 2.523 1.977 1.973 2.544 4.403 2.633 6.283-.01.287-.013.563-.009.827v.46c-.013.229-.032.441-.054.631m6.201-8.2c2.765-2.76 6.408-2.742 8.184-2.528.215 1.776.231 5.407-2.532 8.164-2.761 2.754-6.417 2.766-8.219 2.565-.027-.25-.05-.535-.063-.849.01-.309.011-.636 0-.98.077-1.895.63-4.377 2.63-6.373m-4.39 14.83c.97 0 1.756-.785 1.756-1.752v-1.368c.419.036.892.062 1.41.062 2.624 0 6.34-.645 9.36-3.657 5.17-5.159 3.338-12.3 3.257-12.6a1.753 1.753 0 0 0-1.245-1.242c-.302-.081-7.46-1.91-12.631 3.25a12.068 12.068 0 0 0-1.902 2.456 12.074 12.074 0 0 0-1.903-2.456c-5.171-5.159-12.33-3.331-12.631-3.25a1.753 1.753 0 0 0-1.245 1.241c-.08.302-1.913 7.442 3.258 12.6 3.02 3.014 6.735 3.658 9.36 3.658.513 0 .983-.025 1.4-.062v1.368c0 .968.785 1.752 1.755 1.752"/>
        </g>
      </svg>
    );
  }
}

export default BrokerCompany;
