import React from 'react'
import isNumber from 'lodash/isNumber'
import Number from '../../services/Number'
import HTMLTooltip from './HTMLTooltip'
import { PRIMARY_COLOR_GREEN } from '../../common/constants'

const Description = ({label, value}) => {
  return (isNumber(value?._rawValue) || value) ? (
    <div style={{margin: '4px 8px'}}>
      <span style={{fontSize: '11px', color: 'rgba(0, 0, 0, 0.6)'}}>{label}</span>
      <br/>
      <span style={{fontFamily: 'monospace'}}>{(value?.formatted || value)}</span>
    </div>
  ) : null
}


const TonnageBar = ({ tonnages, unit, tolerance }) => {
  const { total, delivered, outstanding, unallocated, remaining, unplanned } = tonnages
  let _total = total ? total.toString()?.split('(')[1]?.replace(')', '') || total : total
  let totalTonnage = new Number(_total, null, unit, ' ')
  let deliveredTonnage = new Number(delivered, null, unit, ' ')
  let range = tolerance?.range || [0, 0]
  range = [new Number(range[0]), new Number(range[1])]

  const deliveredPercentage = Math.min((deliveredTonnage.number / range[1].number) * 100, 100)
  const tonnagePercentage = Math.min((totalTonnage.number / range[1].number) * 100, 100)
  const tolerancePercentage = Math.min(((range[1].number - range[0].number)/range[1].number) * 100, 100)
  let bgColor = PRIMARY_COLOR_GREEN
  if(deliveredTonnage.number < range[0].number)
    bgColor = 'orange'
  const isFullyDelivered = totalTonnage.number === deliveredTonnage.number
  let toleranceName = tolerance?.id ? tolerance?.name.replace('(whichever is lesser)', '') : tolerance
  let totalTonnageLabel = total ? total.toString()?.includes('(') ? total : totalTonnage.formatted : ''
  let marginOffset = 100 - (tonnagePercentage + tolerancePercentage)
  let toleranceLabel = 'Tolerance' + (range[0].number === range[1].number ? '' : ` (${tolerance.name})`)

  return total === undefined ? null : (
    <HTMLTooltip
      title={
        <div style={{display: 'flex', fontWeight: 'normal', flexDirection: 'column'}}>
          <Description label='Delivered' value={isNumber(delivered) ? deliveredTonnage : false}/>
          <Description label='Unallocated' value={isNumber(unallocated) ? new Number(unallocated, null, unit, ' ') : false}/>
          <Description label='Unplanned' value={isNumber(unplanned) ? new Number(unplanned, null, unit, ' ') : false}/>
          <Description label='Remaining' value={isNumber(remaining) ? new Number(remaining, null, unit, ' ') : false}/>
          <Description label='Outstanding' value={isNumber(outstanding) ? new Number(outstanding, null, unit, ' ') : false}/>
          <Description label='Total' value={totalTonnageLabel}/>
          {
            range[1].number > 0 &&
              <Description label={toleranceLabel} value={range[0].number === range[1].number ? tolerance.name : `${range[0].formatted} to ${range[1].formatted} ${unit}`}/>
          }
        </div>
      }
      sx={{'.MuiTooltip-tooltip': {minWidth: 'auto', width: 'auto', padding: '4px'}}}>
      <span style={{ display: 'inline-flex',flexDirection: 'column', fontSize: '10px' }}>
        <div style={{width: '150px', minHeight: '24px', position: 'relative', overflow: 'hidden', textAlign: 'center', display: 'flex', border: '1px solid rgba(0, 0, 0, 0.3)', backgroundColor: 'rgba(0, 0, 0, 0.3)'}}>
          <div
            style={{
              width: `${tonnagePercentage + marginOffset}%`,
            }}
          >
            <div
              style={{
                backgroundColor: bgColor,
                width: `${deliveredPercentage}%`,
                height: '100%',
                position: 'absolute',
                left: 0,
                top: 0,
              }}
            />
            <div
              style={{
                position: 'absolute',
                lineHeight: '24px',
                color: '#000',
                fontFamily: 'monospace',
                paddingLeft: '4px'
              }}
            >
              {
                (isFullyDelivered || deliveredTonnage.number === 0) ?
                  totalTonnageLabel :
                  <>{deliveredTonnage.number} of {totalTonnageLabel}</>
              }
            </div>
          </div>
          {
            tolerancePercentage > 0 &&
              <div style={{border: '1px dotted white', borderTop: 'none', borderBottom: 'none', width: `${tolerancePercentage}%`, zIndex: 1}} />
          }
        </div>
        {
          Boolean(tolerance?.id) &&
            <span style={{fontSize: '11px', color: 'rgba(0, 0, 0, 0.6)', textAlign: 'right'}}>{toleranceName}</span>
        }
      </span>
    </HTMLTooltip>
  );
};

export default TonnageBar;
