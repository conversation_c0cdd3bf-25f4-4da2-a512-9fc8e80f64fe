import React, { useRef, useState } from "react";
import { useDispatch } from "react-redux";

import { models, service, factories } from "powerbi-client";
import { setHeaderText, setBreadcrumbs } from '../../actions/main';
import { currentUser } from '../../common/utils';
import APIService from '../../services/APIService'

const PowerBIReport = () => {
  const reportRef = useRef();
  const [report, setReport] = useState()

  const dispatch = useDispatch();
  dispatch(setHeaderText('Dashboard'))
  dispatch(setBreadcrumbs([{text: 'Dashboard'}]))


  const renderReport = () => {
    APIService.companies(currentUser()?.companyId).appendToUrl('power-bi/report/').get().then(res => {
      if(res?.report?.embedUrl) {
        const data = res?.report
        setReport(res?.report)
        const embedConfig = {
          type: "report",
          id: data?.reportId,
          embedUrl: data?.embedUrl,
          accessToken: data?.embedToken,
          tokenType: models.TokenType.Embed,
          settings: {
            panes: {
              filters: { visible: false },
              pageNavigation: { visible: false }
            }
          }
        };
        const powerbi = new service.Service(
          factories.hpmFactory,
          factories.wpmpFactory,
          factories.routerFactory
        );

        setTimeout(() => powerbi.embed(reportRef.current, embedConfig), 100);
      }
    })
  }

  React.useEffect(() => {
    if(currentUser()?.companyId)
      renderReport()
  }, [])

  return report?.embedUrl ? <div id="power-bi-report" ref={reportRef} style={{ height: "calc(100vh - 150px)" }} /> : null;
};

export default PowerBIReport;
