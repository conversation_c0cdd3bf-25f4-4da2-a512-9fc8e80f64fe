import React from 'react';
import { styled } from '@mui/material/styles';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';

const HTMLTooltip = styled(({ className, ...props }) => (
  <Tooltip arrow={false} {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: '#FFF',
    color: '#000',
    minWidth: 285,
    width: 'auto',
    maxWidth: 285,
    fontSize: theme.typography.pxToRem(12),
    border: '1px solid',
    borderColor: 'rgba(0, 0, 0, 0.02)',
    borderRadius: '5px',
    padding: '12px',
    boxShadow: '0 2px 3px 0 rgba(0, 0, 0, 0.3), 0 6px 10px 4px rgba(0, 0, 0, 0.15);'
  },
}));


export default HTMLTooltip;
