import React from 'react';
import { connect } from 'react-redux';
import withStyles from '@mui/styles/withStyles';
import Paper from '@mui/material/Paper';
import Slide from '@mui/material/Slide';
import Cancel from '@mui/icons-material/Close';
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography';
import { canCreateOrUpdate } from '../../common/utils';
import { BLACK } from '../../common/constants';
import Alert from '@mui/material/Alert';
import { AppContext } from '../main/LayoutContext';


let styles = () => ({
  paper: {
    zIndex: 11,
    position: 'fixed',
    right: 0,
    top: '88px',
    height: 'calc(100vh - 88px)',
    padding: '24px',
    overflowY: 'auto',
    borderRadius: 0,
  },
  small: {
    width: '400px',
  },
  medium: {
    width: '660px',
  },
  big: {
    width: '720px',
  },
  xlarge: {
    width: '1040px',
  },
  cancel: {
    position: 'absolute',
    right: 25,
    top: 25,
    cursor: 'pointer',
  },
  title: {
    fontWeight: 500,
    color: '#112c42',
    fontSize: 20,
  },
});

class SideDrawer extends React.Component {
  static contextType = AppContext
  render() {
    const { classes, isOpen, children, title, onClose, size, subHeading } = this.props;

    let canCreate = true;
    if(!this.props.canCreate && this.props.app && this.props.user){
      canCreate = canCreateOrUpdate(this.props.user, this.props.app);
    }

    const getClasses = () => {
      const { isMobileDevice } = this.context;
      if(isMobileDevice)
        return classes.paper + ' side-drawer-paper'
      if(!size)
        return `${classes.paper} ${classes.small}`;
      return `${classes.paper} ${classes[size]}`;
    };
    if(canCreate) {
      return(
        <Slide direction="left" in={isOpen} mountOnEnter unmountOnExit>
          <Paper
            elevation={4}
            sx={{
              overflowX: 'hidden',
              color: BLACK,
              padding: '16px 0px !important',
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              '.cardForm-action': {
                position: 'absolute !important',
                top: 'calc(100vh - 145px) !important',
                background: '#FFF !important',
                right: '8px !important',
                zIndex: 12,
                '.MuiButton-root': {
                  margin: '12px 6px !important'
                }
              },
            }}
            className={getClasses()}>
            <div className='col-xs-12 no-side-padding' style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexShrink: 0, margin: '0 16px'}}>
              <Typography component='span' variant="title" className={classes.title}>
                {title}
              </Typography>
              <IconButton size='small' onClick={() => onClose()} sx={{marginRight: '30px'}}>
                <Cancel />
              </IconButton>
            </div>
            {
              subHeading &&
                <Alert severity="warning" style={{marginTop: '10px', flexShrink: 0, padding: '0 16px'}}>
                  {subHeading}
                </Alert>
            }
            <div style={{ overflowY: 'auto', flexGrow: 1, padding: '0 16px', marginBottom: '48px', overflowX: 'hidden' }}>
              {children}
            </div>
          </Paper>
        </Slide>
      );
    } else {
      return null;
    }
  }
}

const mapStateToProps = state => ({
  user: state.main.user.user,
});

export default withStyles(styles)(connect(mapStateToProps)(SideDrawer));
