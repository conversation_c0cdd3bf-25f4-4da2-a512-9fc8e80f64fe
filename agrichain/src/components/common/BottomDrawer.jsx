import React from 'react';

import { connect } from 'react-redux';
import withStyles from '@mui/styles/withStyles';
import Paper from '@mui/material/Paper';
import Slide from '@mui/material/Slide';
import Cancel from '@mui/icons-material/Close';
import Typography from '@mui/material/Typography';
import { canCreateOrUpdate } from '../../common/utils';
import { BLACK } from '../../common/constants';
import {merge} from 'lodash';

const getPaperWidth = () => {
  return `calc(100% - 145px)`;
};

const styles = () => ({
  paper: {
    zIndex: 2,
    position: 'fixed',
    bottom: 0,
    height: 'auto',
    padding: '24px',
    overflow: 'auto',
    borderRadius: 0,
    width: getPaperWidth(),
    right: 0,
  },
  cancel: {
    position: 'absolute',
    right: 25,
    top: 25,
    cursor: 'pointer',
  },
  title: {
    fontWeight: 500,
    color: '#112c42',
    fontSize: 20,
  },
});

class BottomDrawer extends React.Component {
  render() {
    const { classes, isOpen, children, title, onClose, subHeading } = this.props;

    let canCreate = true;
    if(!this.props.canCreate && this.props.app && this.props.user){
      canCreate = canCreateOrUpdate(this.props.user, this.props.app);
    }
    if(canCreate) {
      return(
        <Slide direction="up" in={isOpen} mountOnEnter unmountOnExit>
          <Paper elevation={4} style={merge({color: BLACK}, this.props.paperStyle || {})} className={classes.paper}>
            <Cancel onClick={() => onClose()} className={classes.cancel} />
            <Typography variant="title" className={classes.title}>
              {title}
            </Typography>
            <Typography variant="title" className={classes.title} style={{color: 'red'}}>
              {subHeading}
            </Typography>
            {children}
          </Paper>
        </Slide>
      );
    } else {
      return null;
    }
  }
}

const mapStateToProps = state => ({
  user: state.main.user.user,
});

export default withStyles(styles)(connect(mapStateToProps)(BottomDrawer));
