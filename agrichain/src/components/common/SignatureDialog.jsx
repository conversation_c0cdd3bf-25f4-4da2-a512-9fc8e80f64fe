import React, { Component } from 'react';
import APIService from '../../services/APIService';
import { isEmpty, map, startsWith } from 'lodash';
import { Dialog, DialogActions, DialogContent, Typography, Button } from '@mui/material';
import { DialogTitleWithCloseIcon } from '../common/DialogTitleWithCloseIcon';
import CommonButton from '../common/CommonButton';
import { currentUser } from '../../common/utils';
import SignaturePad from 'signature_pad';

class SignatureDialog extends Component {
    constructor(props) {
        super(props);
        this.canvasRef = React.createRef();
        this.pad = React.createRef();
        this.state = {
            signatureUrlOptions: null,
            selectedSignature: {
                base64: '',
                name: '',
            },
            isCustom: false,
            url: null
        }
        this.handleCustomSignature = this.handleCustomSignature.bind(this)
        this.handleSignatureSelect = this.handleSignatureSelect.bind(this);
    }
    
    componentDidMount() {
        if(this.props.name)
            APIService.profiles(currentUser()?.id).appendToUrl(`signatures/`).post({'name': this.props.name}).then(urls => this.setState({signatureUrlOptions: urls}))
    }

    handleCanvasBegin = () => {
        this.setState({isCustom: true})
        if (this.state.selectedSignature?.name || this.state.selectedSignature?.base64)
            this.setState({selectedSignature: { name: '', base64: '' }, url: null });
    };

    componentDidUpdate() {
        if (this.canvasRef.current && !this.pad?.current) {
            this.pad.current = new SignaturePad(this.canvasRef.current);
            this.canvasRef.current.addEventListener('pointerdown', this.handleCanvasBegin);
        }
        if (!this.state.isCustom && this.state.selectedSignature?.name && !startsWith(this.state.selectedSignature?.name, 'custom') && this.pad.current)
            this.pad.current.clear();
    }

    componentWillUnmount() {
        if (this.canvasRef.current)
            this.canvasRef.current.removeEventListener('pointerdown', this.handleCanvasBegin);
    }

    handleSignatureSelect = (signature, url) => this.setState((prevState) => ({selectedSignature: {'name': (prevState.selectedSignature?.name == signature ? null : signature), 'base64': ''}, url: prevState.url == url ? null : url, isCustom: false}));

    handleSave = event => {
        event.preventDefault();
        this.props.handleSignatureSelect(this.state.selectedSignature, this.state.url);
        this.props.onClose()
    }

    handleCustomSignature = (event, isClear=false) => {
        if (this.pad.current) {
            let base64 = this.pad.current.toDataURL();
            this.setState({selectedSignature: {name: (!isClear ? `custom${Math.random()}` : ''), base64: !isClear ? base64 : '', isCustom: Boolean(isClear)}}, () => {
                if (!isClear)
                    this.handleSave(event)
            })
        }
    }

    handleClear = event => {
        if (this.pad.current) {
          this.pad.current.clear();
          this.handleCustomSignature(event, true);
        }
    };

    render() {
        return (
            <Dialog
                open={this.props.open}
                onClose={this.onClose}
                aria-labelledby="form-dialog-title"
                fullWidth
            >
            <DialogTitleWithCloseIcon onClose={() => this.props.onClose()} id="signature-dialog-title">
                {this.props.selectedEmployee?.signatureUrl ? 'Edit' : 'Add'} Signature
            </DialogTitleWithCloseIcon>
            <DialogContent>
            {!isEmpty(this.state.signatureUrlOptions) &&
            <div style={{display: 'flex', flexWrap: 'wrap', justifyContent: 'left', gap: '10px'}}>
                {map(this.state.signatureUrlOptions, (signatureUrl, fileName) => (
                    <Button
                        key={fileName}
                        onClick={() => this.handleSignatureSelect(fileName, signatureUrl)}
                        style={{ border: this.state.selectedSignature.name === fileName ? '2px solid rgb(106,174,32)' : '1px solid #ccc', borderRadius: '4px', background: 'none', cursor: 'pointer', marginRight: '10px', padding: '10px', width: "260px"}}
                    >
                    <img src={signatureUrl} alt={fileName} style={{ maxWidth: '300px', cursor: 'pointer' }}/>
                    </Button>
                ))}
            </div>
            }
            <Typography variant="body2" color="textSecondary" style={{ marginBottom: '10px', marginTop: '10px' }}>
                Custom
            </Typography>
            <canvas ref={this.canvasRef} width={550} height={150} style={{ border: '1px solid #000' }} />
            </DialogContent>
            <DialogActions>
                <CommonButton onClick={this.handleClear} key='clear' label='Clear' secondary variant="outlined" />
                <CommonButton onClick={() => this.props.onClose()} key='cancel' label='Cancel' secondary variant="contained" />
                <CommonButton onClick={(event) => this.state.isCustom ? this.handleCustomSignature(event): this.handleSave(event)} key='signature' label='Save' primary variant="contained" disabled={!this.state.isCustom && !this.state.selectedSignature?.name && !this.state.selectedSignature?.base64}/>
            </DialogActions>
            </Dialog>
        )
    }
}
export default SignatureDialog;
