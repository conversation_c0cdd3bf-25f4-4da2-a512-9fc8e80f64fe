import React, { useState, useEffect } from 'react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import { connect } from 'react-redux';
import alertifyjs from 'alertifyjs';
import APIService from '../../services/APIService';

const fetchRefreshedURL = async (data, token, callback, failureCallback, alertify) =>
  await APIService.s3()
    .post(data, token)
    .then(response => {
      const url = get(response, 'url');
      if (!isEmpty(url) && typeof callback === 'function') {
        callback(url);
      } else if (typeof failureCallback === 'function') {
        failureCallback(data);
      }
      if (alertify) alertify.dismiss();
    });

const PDFLink = props => {
  const [refreshUrl, setRefreshUrl] = useState(undefined);
  const [error, setError] = useState(undefined);
  useEffect(() => {
    if (refreshUrl || error) {
      if (typeof props.callback === 'function') props.callback();
      if (refreshUrl) {
        window.open(refreshUrl);
        setRefreshUrl(undefined);
      } else if (error) {
        setError(undefined);
      }
    }
  });

  const clickHandler = e => {
    e.preventDefault();
    const alertify = alertifyjs.notify('PDF will be downloaded soon...', 'message', 0);
    fetchRefreshedURL({ refresh: props.url }, props.token, setRefreshUrl, setError, alertify);
  };

  return (
    <a rel='noopener noreferrer' className='no-link' style={{ width: '100%' }} target='_blank' href='' onClick={clickHandler}>
      {props.text || 'PDF'}
    </a>
  );
};

const mapStateToProps = state => ({
  token: state.main.user.token,
  user: state.main.user.user,
  serverEnv: state.main.serverEnv,
});

export default connect(mapStateToProps)(PDFLink);
