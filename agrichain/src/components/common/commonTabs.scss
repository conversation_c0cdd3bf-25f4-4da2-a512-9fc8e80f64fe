@import "../../common/variables.scss";

.tab-header div:last-child {
    overflow-x: hidden;
}

.tab {
  & .tab-header, & .tab-header-simple {
    background: $colorWhite;
    margin: -10px -15px 10px -12px;

    a {
      font-family: Roboto;
      font-size: 0.675rem;
      font-weight: 500;
      letter-spacing: 1px;
      color: $colorBlack;
      opacity: 1;

      @media screen and (min-width: 1024px){
        font-size: 0.875rem;
      }
      max-width: 100% !important;
    }

    a[aria-selected='true'] {
      color: $colorGreen;
      text-decoration: none;
    }
  }

  & span[class^="TabIndicator-root"] {
    height: 3px !important;
  }
}

.tabs.fixed.open {
    left: 230px;
    width: calc(100% - 230px);
}
.tabs.fixed.collapsed{
    left: 75px;
    width: calc(100% - 75px);
}

.tabs.action-right > div {
    width: calc(100% - 160px);
}
