import React from 'react';
import { connect } from 'react-redux';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import { map, isEmpty } from 'lodash';
import { getSeasons } from '../../../actions/main';


class SeasonSelect extends React.Component {
  constructor(props) {
    super (props);

    this.state = {
      season: '',
      seasons: []
    };

    this.handleChange = this.handleChange.bind(this);
  }

  componentDidMount() {
    this.props.getSeasons();
    if (this.props.season && this.state.seasons) {
      this.setSeasonAndPropagate(this.props.season);
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.season !== prevProps.season && (this.state.season === 'N/A' ? this.props.season : true)) {
      this.setSeasonAndPropagate(this.props.season || '');
    }
    if (!isEmpty(this.props.seasons) && isEmpty(this.state.seasons)) {
      this.setState({seasons: this.props.seasons}, () => {
        if (this.props.season)
          this.setSeasonAndPropagate(this.props.season);
      });
    }
  }

  setSeasonAndPropagate(season) {
    this.setState(
      { season },
      () => this.props.onChange(this.state.season, this.props.id)
    );
  }

  handleChange(event) {
    this.setState(
      { season: event.target.value },
      () => {
        if (this.props.onChange) this.props.onChange(event.target.value, this.props.id);
      }
    );
  }

  render() {
    return (
      <FormControl className='mui-select' error={!!this.props.errorText} sx={{width: '100%', textAlign: 'left'}} variant="standard">
        <InputLabel htmlFor={this.props.id}>{this.props.floatingLabelText || 'Season'}</InputLabel>
        {!isEmpty(this.state.seasons) && 
          <Select
          id={this.props.id}
          value={this.state.season}
          onChange={this.handleChange}
          style={this.props.style}
          disabled={this.props.disabled}
          inputProps={{
            ref: this.props.setRef,
          }}
          variant="standard">
          {
            this.props.includeEmptyOption ?
              <MenuItem value='' /> :
              ''
          }
          {
          map(this.state.seasons, season => {
              return (
                <MenuItem
                  key={season}
                  value={season}
                  >
                  {season}
                </MenuItem>
              );
            })
          }
        </Select>
        }
        {this.props.disabled && this.state.season ? <i className="icon-lock"></i>: ''}
        <FormHelperText>{this.props.errorText}</FormHelperText>
      </FormControl>
    );
  }
}
const mapStateToProps = state => {
  return {
    seasons: state.main.seasons,
  };
};

const mapDispatchToProps = dispatch => ({
  getSeasons: () => dispatch(getSeasons()),
});


export default connect(mapStateToProps, mapDispatchToProps)(SeasonSelect);
