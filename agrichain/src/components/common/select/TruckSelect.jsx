import React from 'react';

import isEqual from 'lodash/isEqual';
import size from 'lodash/size';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import get from 'lodash/get';
import find from 'lodash/find';
import isEmpty from 'lodash/isEmpty';

class TruckSelect extends React.Component {
  constructor(props) {
    super (props);

    this.state = {
      truckId: '',
    };

    this.handleChange = this.handleChange.bind(this);
  }

  componentDidUpdate(prevProps) {
    if(!isEmpty(this.props.trucks) && this.props.truckId && !this.state.truckId)
      this.setState({truckId: this.props.truckId});
    if (!isEqual(this.props.trucks, prevProps.trucks)) {
      if (size(this.props.trucks) === 1 && !this.props.dontAutoselectSingleItem) {
        this.setTruckIdAndPropagate(this.props.trucks[0].id);
      } else if (this.state.truckId) {
        this.setTruckIdAndPropagate('', false);
      } else if (this.props.truckId){
        const item = find(this.props.trucks, { id: this.props.truckId });
        this.setTruckIdAndPropagate(get(item, 'id', ''),false);
      }
    }
  }

  handleChange(event) {
    this.setTruckIdAndPropagate(event.target.value);
  }

  setTruckIdAndPropagate(value, validateAfterSet = true) {
    this.setState(
      { truckId: value },
      () => {
        if (this.props.onChange) this.props.onChange(value, this.props.id, validateAfterSet);
      }
    );
  }

  render() {
    return (
      <FormControl
        error={!!this.props.errorText}
        disabled={this.props.dependsOnFreightProvider && !this.props.freightProviderId || false}
        style={{width: '100%'}}
        variant="standard">
        <InputLabel htmlFor={this.props.id}>{this.props.floatingLabelText || 'Truck'}</InputLabel>
        <Select
          id={this.props.id}
          value={this.state.truckId}
          onChange={this.handleChange}
          disabled={this.props.disabled}
          variant="standard">
          {
            this.props.trucks.map(truck => {
              return (
                <MenuItem
                  key={truck.id}
                  value={truck.id}
                  >
                  {truck.rego}
                </MenuItem>
              );
            })
          }
        </Select>
        <FormHelperText>{this.props.errorText}</FormHelperText>
      </FormControl>
    );
  }
}

export default TruckSelect;
