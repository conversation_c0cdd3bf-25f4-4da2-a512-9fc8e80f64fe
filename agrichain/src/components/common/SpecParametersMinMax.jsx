import React from 'react';

import isEmpty from 'lodash/isEmpty';
import forEach from 'lodash/forEach';
import some from 'lodash/some';
import values from 'lodash/values';
import cloneDeep from 'lodash/cloneDeep';
import find from 'lodash/find';
import CommonTextField from '../common/CommonTextField';

class SpecParametersMinMax extends React.Component {
  constructor(props) {
    super (props);
    const specs = this.props.specs || [];
    var originalSpecs = [];
    var errors = {};
    if(!isEmpty(specs)) {
      originalSpecs = cloneDeep(specs);
      forEach(specs, (s) => {
        errors[s.code] = {};
        errors[s.code]['min'] = '';
        errors[s.code]['max'] = '';
      });
    }
    this.state = {
      specs: specs,
      errors: errors,
      originalSpecs: originalSpecs
    };

    this.handleSpecParameterValueChange = this.handleSpecParameterValueChange.bind(this);
    this.validate = this.validate.bind(this);
  }

  handleSpecParameterValueChange(event) {
    var spec = find(this.state.specs, {code: event.target.id});
    spec[event.target.name] = event.target.value;
    this.setState(this.state);
    this.validate(event);
    const valid = some(this.state.errors, (e) => {
      return isEmpty(values(e)['min']) && isEmpty(values(e)['max']);
    });
    const errors = valid ? [] : ['Please enter valid spec parameters'];
    this.props.onChange(this.state.specs, errors, this.props.id);
  }

  validate(event) {
    const newState = {...this.state};
    var bound = event.target.name;
    var originalSpec = find(newState.originalSpecs, {code: event.target.id});
    var currentSpec = find(newState.specs, {code: event.target.id});
    var value = event.target.value;
    if(bound == 'min') {
      if (isEmpty(value)) {
        newState.errors[currentSpec.code][bound] = "This field can not be blank";
      } else if(parseFloat(value) < parseFloat(originalSpec.min) || parseFloat(value) > parseFloat(originalSpec.max)) {
        newState.errors[currentSpec.code][bound] = "Allowed value for this grade " + originalSpec.min.toString() + '-' + originalSpec.max.toString();
      } else if(parseFloat(value) > parseFloat(currentSpec.max)) {
        newState.errors[currentSpec.code][bound] = "Can't be greater than Max";
      } else {
        newState.errors[currentSpec.code][bound] = "";
      }
    }
    else if(bound == 'max') {
      if (isEmpty(value)) {
        newState.errors[currentSpec.code][bound] = "this field can not be blank";
      } else if(parseFloat(value) > parseFloat(originalSpec.max) || parseFloat(value) < parseFloat(originalSpec.min)) {
        newState.errors[currentSpec.code][bound] = "Allowed value for this grade " + originalSpec.min.toString() + '-' + originalSpec.max.toString();
      } else if(parseFloat(value) < parseFloat(currentSpec.min)) {
        newState.errors[currentSpec.code][bound] = "Can't be less than Min";
      } else {
        newState.errors[currentSpec.code][bound] = "";
      }
    }
    this.setState(newState);
  }

  render() {
    const specs = this.state.specs || [];
    return (
      <div className="col-md-12">
      {isEmpty(specs) ? null : (<h4>Spec Parameters</h4>)}
      {
        specs.map(spec => {
          return (
            <div key={spec.code} className="col-md-12" style={{marginBottom: '15px', marginTop: '15px'}}>
            {spec.name}<br/>
            <CommonTextField
              id={spec.code}
              name="min"
              label="Min"
              type="number"
              value={spec.min}
              onChange={this.handleSpecParameterValueChange}
              onBlur={this.validate}
              helperText={this.state.errors[spec.code]['min']}
            />
            <CommonTextField
              id={spec.code}
              name="max"
              label="Max"
              value={spec.max}
              helperText={this.state.errors[spec.code]['max']}
              onBlur={this.validate}
              onChange={this.handleSpecParameterValueChange}
            />
          </div>
        );
        })}
      </div>
    );
  }

}

export default SpecParametersMinMax;
