import React from 'react';
import { useState } from 'react';
import CloudDownload from '@mui/icons-material/CloudDownload';
import { Button, Menu, Tooltip, ListItemButton, ListItemText } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import get from 'lodash/get';
import { AppContext } from '../main/LayoutContext';

const CommonListingButton = props => {
  const [showOptions, setShowOptions] = useState(false);
  const { isMobileDevice } = React.useContext(AppContext)

  const handleOptionClick = val => {
    val.fx();
    setShowOptions(false);
  };

  return isMobileDevice ? <React.Fragment /> : (
    <>
      <Tooltip title={props.title} placement="top">
        <Button
          variant={props.variant || "contained"}
          type="button"
          onClick={props.showMenus ? (e) => setShowOptions(e.currentTarget): props.defaultHandler }
          color={props.color || "primary"}
          className='add-button common-listing-button'
          style= {get(props, 'style', { float: 'right', marginLeft: '10px' })}
          size={props.size || 'medium'}
          startIcon={props.startIcon}
          endIcon={props.endIcon}
        >
          {
            get(props, 'showDownLoadIcon', true) &&
              <CloudDownload style={{ paddingRight: '5px', ...props.downloadIconStyle }} />
          }
          { props.name }
          { props.showMenus && !props.hideVertIcon && <MoreVertIcon fontSize='inherit' /> }
        </Button>
      </Tooltip>
      {
        Boolean(showOptions) &&
          <Menu
            id="actions-menu"
            anchorEl={showOptions}
            open={Boolean(showOptions)}
            onClose={() => setShowOptions(false)}
            sx={{'.MuiList-root': {minWidth: '250px', maxWidth: '350px', padding: '8px 2px'}}}
          >
            {
              get(props, 'optionMapper', []).map((val, index) => (
                get(val, 'enableOption', true) &&
                  get(val, 'name') &&
                  <ListItemButton key={index} onClick={() => handleOptionClick(val)} sx={{padding: '4px 12px'}}>
                    <ListItemText
                      sx={{
                        marginTop: '2px',
                        marginBottom: '2px',
                        '.MuiListItemText-primary': {fontSize: '14px'},
                        '.MuiListItemText-secondary': {fontSize: '12px'}
                      }}
                      primary={val.name}
                      secondary={val.description}
                    />
                  </ListItemButton>
              ))
            }
          </Menu>
      }
    </>
  );
};

export default CommonListingButton;
