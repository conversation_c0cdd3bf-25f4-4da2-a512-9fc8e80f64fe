import React from 'react';

import { connect } from 'react-redux';
import AutoComplete from './AutoComplete';
import isEmpty from 'lodash/isEmpty';
import find from 'lodash/find';
import { getContracts } from '../../../actions/companies/contracts';

class FarmContractAutoComplete extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      searchText: '',
    };
  }

  componentDidMount() {
    if (isEmpty(this.props.contracts)) {
      this.props.getContracts();
    }
  }

  componentDidUpdate(prevProps) {
    if (
      (this.props.contractId || prevProps.contractId) &&
      !isEmpty(this.props.contracts) &&
      isEmpty(this.state.searchText)
    ) {
      const contract = find(this.props.contracts, {id: this.props.contractId || prevProps.contractId});

      if (contract) {
        this.setState({ searchText: contract.name });
      }
    }
  }

  handleChange = name => value => {
    const chosenRequest = value ? find(this.props.contracts, {id: value}) : {id: '', name: ''};
    this.setState({
      [name]: value,
    });
    this.props.onChange(chosenRequest, value);
  };

  render() {
    const items = this.props.contracts.map(e => ({label: e.name, value: e.id}));
    return (
      <AutoComplete
        id={this.props.id}
        label={this.props.floatingLabelText || "Contract No"}
        placeholder={this.props.floatingLabelText || "Contract No"}
        options={items}
        value={this.state.searchText}
        onChange={this.handleChange("searchText")}
        onBlur={this.props.onBlur}
        errorText={this.props.errorText}
      />
    );
  }
}

const mapStateToProps = (state) => {
  const contracts = state.companies.contracts.items;
  return {
    contracts: contracts.map(function(contract) {
      return {
        id: contract.id,
        name: contract.referenceNumber,
      };
    }),
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    getContracts: () => dispatch(getContracts())
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(FarmContractAutoComplete);
