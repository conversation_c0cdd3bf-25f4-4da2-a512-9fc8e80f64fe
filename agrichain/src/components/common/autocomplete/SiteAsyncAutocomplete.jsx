import React from 'react';

import { TextField, CircularProgress } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box'
import {get, debounce, last, isArray, uniqBy, map, omit, filter, reject} from 'lodash';
import APIService from '../../../services/APIService';
import { currentUser } from '../../../common/utils';
import FarmListItem from '../../farms/FarmListItem'

const SiteAsyncAutocomplete = ({ id, minLength, selected, onChange, size, variant, disabled, label, multiple, limitTags, errorText, farmId, ref, activeSitesOnly, includeSelectedSiteId, addSiteOption, popupIcon, classes, helperText, addLabel, override, options, isAddNewSiteOptionEnabled = true, fetchOnlySitesOfCompany = false, canClearSiteOptions = false, unsetClearSiteOption, fetchTopSitesOnClear=false }) => {
  const user = currentUser()
  const [input, setInput] = React.useState('')
  const [items, setItems] = React.useState([])
  const [isLoading, setIsLoading] = React.useState(false)
  const isSearchable = input.length >= minLength;

  const _onChange = (event, items) => {
    const item = isArray(items) ? last(items) : items
    const text = get(item, 'name', '');
    if(item && item.inputValue){
      onChange(item, id)
    }
    if(!item || item.id !== get(selected, 'id') || !selected?.companyId || override) {
      setInput(text)
      setIsLoading(item ? isLoading : false)
      onChange(multiple ? items : item, id)
    }
  }
  const _onInputChange = debounce((event, value, reason) => {
    if(reason == 'input' && event){
      setInput(value)
      setIsLoading(Boolean(value))
      if(value && value.length >= minLength)
        fetchItems(value)
    }
    if(value == '' && fetchTopSitesOnClear)
      fetchTopItems()
  }, 300);

  const fetchItems = (searchStr, isPrefetch=false) => {
    if(searchStr || isPrefetch) {
      let _str = searchStr
      if(_str && _str.includes('(') && _str.includes(')')) {
        _str = get(_str.match(/\(([^()]*)\)[^(]*$/), '1')
      }

      if(user?.id) {
        let queryParams = {
          search: _str,
          is_active: activeSitesOnly,
          include_site: includeSelectedSiteId,
          handler_ids: farmId,
          company_ids: fetchOnlySitesOfCompany ? user?.companyId : null,
          with_ranking: true,
          rank_limit: 25
        }

        APIService
          .contracts()
          .handlers()
          .get(null, null, queryParams)
          .then(res => {
            setItems(res?.handlers || [])
            setIsLoading(false)
            if (selected?.id && (!selected?.companyId || override) && res?.handlers)
              _onChange(null, res?.handlers)
          });
      }
      else {
        APIService
          .farms()
          .appendToUrl('all/')
          .get(null, null, {search_txt: _str})
          .then(res => {
            setItems(res)
            setIsLoading(false)
          });

      }
    }
  };

  const fetchTopItems = () => {
    let queryParams = {
      company_ids: fetchOnlySitesOfCompany ? user?.companyId : null,
      with_ranking: true,
      rank_limit: 25
    }
    APIService
      .contracts()
      .handlers()
      .get(null, null, queryParams)
      .then(res => {
        setItems(res?.handlers || [])
        setIsLoading(false)
      });
  }

  const fetchById = () => {
    if(farmId) {
      setIsLoading(true)
      APIService.contracts().handlers().get(null, null, {handler_ids: farmId}).then(res => {
        let _items = res?.handlers || []
        setItems(_items)
        _onChange(null, _items)
        setIsLoading(false)
      })
    }
  }

  const clearItems = () => {
    setItems([])
    setInput('')
    unsetClearSiteOption()
    fetchItems('', true)
  }

  React.useEffect(() => {
    if(farmId && !selected?.id && !isLoading && !items?.length) {
      fetchById()
    }
  }, [farmId])

  React.useEffect(() => {
    if(canClearSiteOptions)
      clearItems()
  }, [canClearSiteOptions])

  React.useEffect(() => {
    if(!canClearSiteOptions && !includeSelectedSiteId)
      fetchTopItems()
  }, [])

  const getGroup = site => {
    if(site.__group)
      return site.__group
    if(site.companyId === user.companyId)
      return 'Self'
    if ((site.score || 0) > 0)
      return 'Frequently Used'
    return 'All'
  }

  const bestMatchFilterOptions = (options, state) => {
    let _options = [...options]

    if(!state?.inputValue && options?.length > 0){
      _options = map(options, option => omit(option, ['__group']))
    } else {
      if(state?.inputValue && options?.length > 0) {
        const bestMatches = filter(options, option => {
          let name = option.displayName || option.name
          const isMatch = name ? name.toLowerCase().includes(state.inputValue.toLowerCase()) : false
          if(isMatch)
            option.__group = 'Best Match'
          return isMatch
        })
        const bestMatchIds = map(bestMatches, 'id')
        _options = [...bestMatches, ...reject(options, option => bestMatchIds.includes(option.id))]
      }
    }

    if (addSiteOption && !isLoading && isSearchable) {
      if (isAddNewSiteOptionEnabled && state.inputValue && state.inputValue.length >= 3 ) {
        _options.push({
          inputValue: state.inputValue,
          name: `Add "${state.inputValue}" as a new site`,
        });
      }
    }
    return _options
  }


  return (
    <Box sx={{'.MuiAutocomplete-groupLabel': {lineHeight: '28px', fontSize: '12px', padding: '0 12px'}, '.MuiListItem-root': {padding: '6px 12px !important'}}}>
      <Autocomplete
      ref={ref}
      limitTags={limitTags || -1}
      multiple={Boolean(multiple)}
      id={id || "site"}
      size={size || 'medium'}
        blurOnSelect
        disablePortal
      options={options ? uniqBy([...options, ...items], 'id') : items}
      getOptionLabel={option => {
        if (addSiteOption) {
          if (typeof option === 'string')
            return option;
          if (option.inputValue && option.length >= 3)
            return get(option, 'displayName', get(option, 'name'));
          return get(option, 'displayName', get(option, 'name'));
        }
        else
          return option.name
      }}
      isOptionEqualToValue={(option, value) => option?.id === value?.id && value?.id}
      popupIcon={popupIcon}
      loading={isLoading}
      loadingText={isSearchable ? 'Loading...' : `Type at least ${minLength} characters to search`}
      noOptionsText={input ? "No results" : 'Start typing...'}
      filterOptions={bestMatchFilterOptions}
      renderInput={params => (
        <TextField
          {...params}
          variant={variant || 'outlined'}
          label={addLabel ? label : null}
          placeholder={`${label} (Type at least ${minLength} characters to search)`}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <React.Fragment>
                {isLoading && !disabled ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
                {disabled && <i style={{top: '5px'}} className="icon-lock"></i>}
              </React.Fragment>
            ),
          }}
          inputProps={{
            ...params.inputProps,
            className: params.inputProps.className + ' black-text'
          }}
          fullWidth
          error={Boolean(errorText)}
          helperText={errorText || helperText}
        />
        )}
        classes={classes}
        value={selected || null}
        onChange={_onChange}
        onInputChange={(event, value, reason) => _onInputChange(event, value, reason)}
        disabled={Boolean(disabled)}
        disableCloseOnSelect
        groupBy={getGroup}
        renderOption={(props, option) => <FarmListItem {...props} farm={option}/>}
      />
    </Box>

  )
}

export default SiteAsyncAutocomplete;
