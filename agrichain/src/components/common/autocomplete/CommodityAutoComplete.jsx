import React from 'react';

import { connect } from 'react-redux';
import { isEmpty, find, map , filter, includes} from 'lodash';
import Box from '@mui/material/Box'
import { createFilterOptions } from '@mui/material';
import { getCommoditiesWithVarietiesAndGrades, getCommoditiesWithGrades } from '../../../actions/api/commodities';
import AutoComplete from './AutoComplete';
import { AppContext } from '../../main/LayoutContext';

const autocompleteFilters = createFilterOptions();

class CommodityAutoComplete extends React.Component {
  static contextType = AppContext
  constructor(props) {
    super (props);

    this.state = {
      commodityId: ''
    };

    this.handleChange = this.handleChange.bind(this);
  }

  componentDidMount() {
    if (isEmpty(this.props.commodities)) {
      if(this.props.noVarieties)
        this.props.getCommoditiesWithGrades(this.props.includeUnknown);
      else
        this.props.getCommoditiesWithVarietiesAndGrades(this.props.includeUnknown);
    }

    if (this.props.commodityId) {
      this.setCommodityIdAndPropagate(this.props.commodityId);
    }
  }

  componentDidUpdate(prevProps) {
    if (this.props.commodityId !== prevProps.commodityId) {
      this.setCommodityIdAndPropagate(this.props.commodityId || '');
    }
  }

  setCommodityIdAndPropagate(commodityId) {
    this.setState(
      { commodityId },
      () => {
        if(this.props.onChange)
          this.props.onChange(this.state.commodityId, this.props.id, this.getSelectedCommodity());
      }
    );
  }

  getSelectedCommodity() {
    if(this.state.commodityId) {
      return find(this.props.commodities, {id: this.state.commodityId});
    }
  }

  handleChange(value) {
    this.setCommodityIdAndPropagate(value);
  }

  shouldShowMostUsedOnly = () => {
    const { mostUsedProducts } = this.context
    return mostUsedProducts?.commodityIds?.length > 0
  }

  getItems = () => {
    const { commodities, itemFilterFunc } = this.props
    const { mostUsedProducts } = this.context
    let items = commodities
    if(itemFilterFunc)
      items = itemFilterFunc(items)
    items = map(items, commodity => ({label: commodity.displayName, value: commodity.id, __group: includes(mostUsedProducts.commodityIds, commodity.id) ? 'Frequently Used' : 'All'}));
    return items
  }

  filterOptions = (options, params) => {
    const { mostUsedProducts } = this.context
    const { commodityId } = this .state
    let filtered = autocompleteFilters(options, params);
    if(this.shouldShowMostUsedOnly() && !params.inputValue && (!commodityId || includes(mostUsedProducts.commodityIds, commodityId)))
      filtered = filter(filtered, item => includes(mostUsedProducts.commodityIds, item.value))
    return isEmpty(filtered) ? options : filtered;
  }

  render() {
    const floatingLabelText = this.props.floatingLabelText || 'Commodity';
    const placeholder = this.props.placeholder || floatingLabelText;
    const items = this.getItems();
    return (
      <Box sx={{'.MuiAutocomplete-groupLabel': {lineHeight: '28px', fontSize: '12px', padding: '0 12px'}}}>
        <div className="relative-pos" ref={this.props.setRef}>
          <AutoComplete
            blurOnSelect
            disablePortal
            sx={{...this.props.style}}
            id={this.props.id}
            placeholder={placeholder}
            options={items}
            value={this.state.commodityId}
            label={floatingLabelText}
            onBlur={this.props.onBlur}
            errorText={this.props.errorText}
            disabled={this.props.disabled}
            onChange={this.handleChange}
            filterOptions={this.filterOptions}
            noListbox
            variant={this.props.variant}
            groupBy={option => option.__group}
          />
          {this.props.disabled && this.state.commodityId ? <i style={this.props.style} className="icon-lock"></i> : ''}
        </div>
      </Box>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    commodities: state.master.commodities.items,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    getCommoditiesWithVarietiesAndGrades: includeUnknown => dispatch(getCommoditiesWithVarietiesAndGrades(includeUnknown)),
    getCommoditiesWithGrades: includeUnknown => dispatch(getCommoditiesWithGrades(includeUnknown))
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(CommodityAutoComplete);
