import React from 'react';

import AutoComplete from './AutoComplete';
import isArray from 'lodash/isArray';

class OperatorAutoComplete extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      operators: [],
    };
  }

  render() {
    const items = isArray(this.props.items) && this.props.items.map(e => ({label: e.name, value: e.id}));
    return (
        <AutoComplete
            id={this.props.id}
            value={this.props.value}
            options={items}
            label="Operator"
            placeholder="Operator"
            onChange={this.props.onChange(this.props.items, "operator")}
            errorText={this.props.errorText}
            onBlur={this.props.onBlur}
            disabled={this.props.disabled}
        />
    );
  }
}

export default OperatorAutoComplete;
