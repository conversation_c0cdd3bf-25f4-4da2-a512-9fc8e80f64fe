import React from 'react';

import AutoComplete from './AutoComplete';
import filter from 'lodash/filter';

class SiteAutoComplete extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {

    const dataSource = filter(this.props.items, (item) => {
      return (this.props.companyId === item.companyId && this.props.track == item.track);
    });

    const items = dataSource.map(e => ({label: e.name, value: e.id}));

    return (
        <AutoComplete
            id={this.props.id}
            options={items}
            value={this.props.value}
            label="Site"
            placeholder="Site"
            errorText={this.props.errorText}
            onBlur={this.props.onBlur}
            disabled={this.props.disabled}
            onChange={this.props.onChange(dataSource, "site")}
        />
    );
  }
}

export default SiteAutoComplete;
