import React from 'react';

import AutoComplete from './AutoComplete';
import isArray from 'lodash/isArray';

class TrackAutoComplete extends React.Component {
  constructor(props) {
    super(props);
  }

  render() {
    const items = isArray(this.props.items) && this.props.items.map((e) => {
      return {label: e.name, value: e.id};
    });

    return (
        <AutoComplete
            id={this.props.id}
            value={this.props.value}
            options={items}
            label="Track"
            placeholder="Track"
            onChange={this.props.onChange(this.props.items, "track")}
            errorText={this.props.errorText}
            onBlur={this.props.onBlur}
        />
    );
  }
}

export default TrackAutoComplete;
