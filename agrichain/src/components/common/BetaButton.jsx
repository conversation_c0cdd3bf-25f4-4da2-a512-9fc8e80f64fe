import React, {Component} from 'react';

import Button from "@mui/material/Button/Button";
import withStyles from '@mui/styles/withStyles';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';
import { PRIMARY_COLOR_GREEN } from '../../common/constants';

const styles = () => ({
  settingBtn:{
    minHeight: '20px',
    padding: '0 5px',
    margin:'-5px 0px 0px 5px'
  },
  settingBtnText:{
    textTransform: 'capitalize',
    padding: '0 5px',
    fontSize: '12px !important',
  }
});



class BetaButton extends Component {
  render() {
    const { classes, beta, href, onClick, size, label } = this.props;
    return <Button
             variant="outlined"
             onClick={onClick}
             href={href}
             size={size || "small"}
             className={classes.settingBtn}
             color="black">
             {
               beta ?
                 <ToggleOnIcon color='primary' size={size || 'small'} /> :
               <ToggleOffIcon color='default' size={size || 'small'} />

             }
             <span className={classes.settingBtnText} style={beta ? {color: PRIMARY_COLOR_GREEN} : {}}>
               {label}
             </span>
           </Button>;
  }
}

export default withStyles(styles)(BetaButton);
