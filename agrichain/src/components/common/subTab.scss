@import "../../common/variables.scss";

.subTab {

  span[class^="TabIndicator-root"] {
    display: none;
  }

  &-header {
    margin-left: -1px;

    button,a {
      background: $colorWhite;
      opacity: 1;
      margin-right: 8px;
      border-radius: 6px 6px 0 0;
      text-transform: capitalize;
      border: 1px solid $colorGrey;
      span {
        font-size: 1.150rem;
      }
    }

    a[aria-selected="true"],button[aria-selected="true"]{
      border-bottom: 1px solid $colorWhite;

      & span {
        font-weight: bold;
      }
    }
    a:hover,a:focus {
        text-decoration: none !important;
        color: inherit !important;
    }
  }

  &-container {
    background: $colorWhite;
    padding: 10px;
    border-radius: 0 0 4px 4px;
    border-top: 1px solid $colorGrey;
    .paper-table {
        padding: 0;
        box-shadow: none;
    }
  }
}
