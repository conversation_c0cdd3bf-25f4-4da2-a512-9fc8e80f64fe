import React from 'react';
import { connect } from 'react-redux';
import { sendFeedback } from '../actions/main';
import ReactSuggestionBox from 'react-suggestion-box';

class Feedback extends React.Component {
  sendFeedback = data => {
    if(data)
      this.props.dispatch(sendFeedback(data));
  };

  render() {
    return (
      <ReactSuggestionBox
        containerClassName='left-bar-control'
        buttonTooltipText='Feedback'
        mainButtonLabel='Feedback'
        title='Send Feedback'
        onSubmit={this.sendFeedback}
      />
    );
  }
}

export default connect()(Feedback);
