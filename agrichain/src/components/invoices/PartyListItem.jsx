import React from 'react';
import { useState } from 'react'
import ListItem from '@mui/material/ListItem'
import ListItemText from '@mui/material/ListItemText'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import Create from '@mui/icons-material/Create';

const PartyListItem = ({ party, isExpanded, isUsingPaymentRun, onClick, controls, onRecipientEdit, ...rest }) => {
  const { recipientDisplay, companyId, payeeName, payerName } = party
  const name = payeeName || payerName

   const [showExpandCollapseTooltip, setShowExpandCollapseTooltip] = useState(false);

  return (
    <span className='col-xs-12 padding-reset' style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
      <Tooltip
        title={isExpanded ? 'Click to collapse' : 'Click to expand and view items'}
        open={showExpandCollapseTooltip}
        onMouseEnter={() => setShowExpandCollapseTooltip(true)}
        onMouseLeave={() => setShowExpandCollapseTooltip(false)}
      >
        <ListItem
          sx={{cursor: 'pointer', padding: '4px 12px', width: 'calc(100% - 400px)'}}
          {...rest}
          onClick={event => onClick(event, companyId, name)}
        >
          <ListItemText
            sx={{marginTop: '2px', marginBottom: 0}}
            primary={name}
            secondary={
              <span style={{display: 'flex', alignItems: 'center'}}>
                { recipientDisplay || (isUsingPaymentRun ? <i>No Recipients</i> : '') }
                {
                  isUsingPaymentRun && isExpanded &&
                    <Tooltip title='Edit Recipients'>
                      <span>
                        <IconButton
                          size='small'
                          onClick={event => onRecipientEdit(event, companyId)}
                          onMouseEnter={() => setShowExpandCollapseTooltip(false)}
                          onMouseLeave={() => setShowExpandCollapseTooltip(true)}
                          sx={{marginLeft: '8px'}}
                        >
                          <Create fontSize="small" />
                        </IconButton>
                      </span>
                    </Tooltip>
                }
              </span>
            }
          />
        </ListItem>
      </Tooltip>
      {controls}
    </span>
  )
}

export default PartyListItem
