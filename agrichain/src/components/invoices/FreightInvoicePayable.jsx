import React from 'react';
import { connect } from 'react-redux';
import { <PERSON><PERSON>, Divider, Paper, Dialog, DialogActions, DialogContent, Checkbox, Table, TableHead, TableRow, TableCell, TableBody, FormControlLabel, Badge, Chip, Skeleton } from '@mui/material';
import ExpandIcon from '@mui/icons-material/Expand';
import CollapseIcon from '@mui/icons-material/UnfoldLess';
import {
  get, isEmpty, find, reject, set, map, filter,
  includes, remove, uniq, uniqBy, has,
  isEqual, isUndefined, sum, forEach, compact, orderBy,
  intersection, without, flatten, sumBy
} from 'lodash';
import APIService from '../../services/APIService';
import { currentUserCompany, getCountryFormats, attachCSVEventListener, defaultViewAction, getCountryLabel, generateIdentifier, getCountryCurrency } from '../../common/utils';
import { forceStopLoader, isLoading, setDownloadBar, setLoadingText } from '../../actions/main';
import InvoicePayableTable from '../../containers/InvoicePayableTable';
import { FILTER_KEYS_TO_EXCLUDE, FREIGHT_PAYABLE_ITEM_COLUMNS, INVOICE_FILTER_KEYS_MAPPING, PREDEFINED_DATE_RANGE_FILTER_KEYS } from '../../common/constants';
import moment from 'moment';
import CommonDatePicker from '../common/CommonDatePicker';
import alertifyjs from 'alertifyjs';
import { DialogTitleWithCloseIcon } from '../common/DialogTitleWithCloseIcon';
import EmailAutocomplete from '../common/autocomplete/EmailAutocomplete';
import SideDrawer from '../common/SideDrawer';
import CustomHeaderOptions from '../common/CustomHeaderOptions';
import InvoiceFilters from '../common/InvoiceFilters';
import { Virtuoso } from 'react-virtuoso';
import PartyListItem from './PartyListItem'
import ListingControls from '../common/ListingControls'

const CUSTOM_HEADER_EDIT = {
  fontSize: '12px',
  textDecoration: 'underline',
  cursor: 'pointer',
  marginLeft: '10px'
};

const DEFAULT_FILTERS = {
  'payee__company_id__in': [],
  'contract__commodity__id__in': '',
  'payment_due_date_range': '',
  'payment_due_date__gte': '',
  'payment_due_date__lte': ''
}

class FreightInvoicePayable extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      company: currentUserCompany(),
      selectedPayeeCompanyIds: [],
      expanded: [],
      loadingItemsFor: [],
      invoiceItems: [],
      isFetchingInvoices: false,
      companyDirectory: [],
      invoiceItemsGroupedByPayee: [],
      isFetchingCompanyDirectory: false,
      selectedInvoiceItems: [],
      openDialog: false,
      selectedRecipients: undefined,
      recipientsDialogOpen: false,
      sellerEmployeeData: undefined,
      showRecipientsError: false,
      csvPopup: false,
      isFilteredCsv: false,
      customColumns: true,
      customColumnNames: {},
      customHeaderOptions: false,
      customColumnTitle: undefined,
      applyFilters: false,
      openSideDrawer: false,
      expandAllLoading: false,
      emailRecipientsCompanyId: false,
      filters: {},
      bankProcessingDate: {
        value: undefined,
        error: ''
      },
    }

    this.handleGeneratePaymentRunClick = this.handleGeneratePaymentRunClick.bind(this);
    this.generatePaymentRun = this.generatePaymentRun.bind(this);
    this.handleBankProcessingDateChange = this.handleBankProcessingDateChange.bind(this);
    this.fetchInvoiceDetail = this.fetchInvoiceDetail.bind(this);
    this.getInvoiceItemsFrom = this.getInvoiceItemsFrom.bind(this);
  }

  componentDidMount() {
    this._attachCSVEventListener();
    APIService.companies(
      this.state.company?.id
    ).get().then(
      response => this.setState(
        {company: response}, () => this.fetchInvoices(true)
      )
    )
    APIService.profiles()
      .filters('freight_invoice_payable')
      .get()
      .then(res => {
        this.setState({
          filters: get(res, 'freight_invoice_payable', {}) || DEFAULT_FILTERS,
        });
      });
  }

  isUsingPaymentRun = () => this.state.company?.paymentRun === true

  onDownloadResponse(message) {
    this.props.dispatch(setDownloadBar(message, true, this.onCloseDownloadResponse));
  }

  _attachCSVEventListener() {
    attachCSVEventListener('freight_payables-csv-ready', 'Freight Invoice Payables', this.onDownloadResponse);
  }

  fetchInvoices(isInitialRequest=false) {
    this.props.dispatch(isLoading('LoadingPayables'))
    if (!this.state.isFetchingInvoices) {
      this.setState({ isFetchingInvoices: true }, async () => {
        const company = currentUserCompany();
        APIService.invoices().appendToUrl(`freight-payable-items/?company_id=${company.id}&items=false&verbose=${!isInitialRequest}`).get().then(response =>
          this.setState({
            invoiceItems: response,
            isFetchingInvoices: false,
            expanded: response.length == 1  ? [response[0].companyId]: []
          }, () => {
            if (isEmpty(this.state.companyDirectory))
              this.getCompanyDirectory()
            else
              this.setPayableItems();

            if(response.length === 1)
              this._fetchItems(response[0].companyId)
          }));
      })
    }
  }

  getInvoiceItemsFrom = items => {
    remove(items, item => item.isChemicalItem);
    return items.reduce((obj, objectKey) => ({ ...obj, [objectKey.id]: {'levies': objectKey?.levyItems?.reduce((acc, levy) => (acc[levy.levyType] = { "total": objectKey.levy ? levy.total : undefined, "isCustomAdjustment": levy?.isCustomAdjustment}, acc), {}), 'epr': objectKey.epr}}), {});
  }

  generatePaymentRun() {
    if (isUndefined(this.state.bankProcessingDate.value)) {
      const newState = {...this.state};
      newState.bankProcessingDate.error = 'This field is required';
      this.setState(newState);
    }
    else {
      this.setState({openDialog: false}, () => {
        this.props.isLoading('GeneratePaymentRun');
        this.props.setLoadingText('Generating Payment Run, this may take a while. DO NOT close this window. Please continue with other work on a separate window/tab.')
        let payeeCompanyIds = []
        let invoiceItems = {}
        let paymentDueDate;

        if(!isEmpty(this.state.selectedPayeeCompanyIds)) {
          const withItems = filter(this.state.invoiceItemsGroupedByPayee, payee => payee?.items?.length)
          const withItemsCompanyIds = map(withItems, 'companyId')
          forEach(this.state.selectedPayeeCompanyIds, payeeCompanyId => {
            if(withItemsCompanyIds.includes(payeeCompanyId))
              invoiceItems = {
                ...invoiceItems,
                ...(this.getInvoiceItemsFrom(find(this.state.invoiceItemsGroupedByPayee, {companyId: payeeCompanyId})?.items) || [])
              }
            else
              payeeCompanyIds.push(payeeCompanyId)
          })
        }
        if(!isEmpty(this.state.selectedInvoiceItems)) {
          let selectedInvoiceItems = [...this.state.selectedInvoiceItems];
          invoiceItems = {...invoiceItems, ...this.getInvoiceItemsFrom(selectedInvoiceItems)}
          let minPaymentDueObj = selectedInvoiceItems.sort((a, b) => new Date(a.paymentDue) - new Date(b.paymentDue));
          paymentDueDate = moment(get(minPaymentDueObj, '0.paymentDue'), 'DD/MM/YYYY').format('YYYY-MM-DD hh:mm:ss')
        }
        let identifier = generateIdentifier('paymentRun');
        const company = currentUserCompany();
        let data = {
          'invoiceItems': invoiceItems,
          'payeeCompanyIds': payeeCompanyIds,
          'identifier' : identifier,
          'payer': {
            'companyId': get(company, 'id'),
            'role': 'payer'
          },
          'paymentDue': paymentDueDate,
          'bankProcessingDate': this.state.bankProcessingDate.value,
          'isFreightPayable': true
        };
        APIService.invoices()
          .appendToUrl(`payment-runs/?company_id=${company.id}`)
          .post(data)
          .then((res) => {
            if (res.errors) {
              if(res.errors[0].includes('recreated'))
                alertifyjs.error(res.errors[0] + ', reloading...', 2, () => window.location.reload());
              else
                alertifyjs.error(res.errors[0]);

            }
            else {
              alertifyjs.success("Payment Run created successfully");
              this.props.forceStopLoader();
              window.location = '#/invoices/payment-runs';
            }
          });
      });
    }
  }

  handleBankProcessingDateChange(value) {
    const newState = {...this.state};
    newState.bankProcessingDate.value = value;
    newState.bankProcessingDate.error = '';
    this.setState(newState);
  }

  toFormattedDate = date => {
    if (date) {
      const mDate = moment(date)
      return mDate.isValid() ? mDate.format(getCountryFormats().date) : date
    }
    return date
  }

  setPayableItems() {
    const { invoiceItems } = this.state
    if (!isEmpty(this.state.companyDirectory)) {
      map(invoiceItems, obj => {
        let payer = find(this.state.companyDirectory, { id: obj.companyId });
        obj.payerName = get(payer, 'name');
        obj.allSelected = false;
        if (obj.recipients && !isEmpty(obj.recipients)) {
          let recipientDisplayStr = "";
          map(obj.recipients, recipient => {
            if (isEmpty(recipientDisplayStr))
              recipientDisplayStr = recipientDisplayStr + recipient.email;
            else
              recipientDisplayStr = recipientDisplayStr + ", " + recipient.email;
          });
          obj['recipientDisplay'] = 'Recipients: (' + recipientDisplayStr + ')';
        }
        map(obj.items, invoiceItem => {
          invoiceItem.paymentDue = this.toFormattedDate(invoiceItem.paymentDue)
        })
      })
      this.setState({ invoiceItemsGroupedByPayee: invoiceItems });
    }
    this.props.dispatch(forceStopLoader())
  }

  getCompanyDirectory() {
    if (!this.state.isFetchingCompanyDirectory)
      this.setState({ isFetchingCompanyDirectory: true }, async () => {
        APIService.companies().appendToUrl('directory/names/?excludeGroups=true&include_self=True').get().then(companies =>
          this.setState({ companyDirectory: companies, isFetchingCompanyDirectory: false }, this.setPayableItems));
      })
  }

  isSelected = (invoiceItem, payee) => Boolean(this.state.selectedPayeeCompanyIds?.includes(payee?.companyId) || find(this.state.selectedInvoiceItems, {id: invoiceItem.id}) || invoiceItem.isCheckedItem);

  updateSelectedPayeeCompanyIdsFromSelectedItem = payee => {
    const [isSome, isAll] = this.isSomeSelectedForPayee(payee)
    const isAlreadySelectedPayee = this.state.selectedPayeeCompanyIds.includes(payee.companyId)
    if(isAll && !isAlreadySelectedPayee)
      this.setState({selectedPayeeCompanyIds: [...this.state.selectedPayeeCompanyIds, payee.companyId] })
    else if(isSome && isAlreadySelectedPayee)
      this.setState({selectedPayeeCompanyIds: without(this.state.selectedPayeeCompanyIds, payee.companyId) })
    else if (!isAll && !isSome && isAlreadySelectedPayee)
      this.setState({selectedPayeeCompanyIds: without(this.state.selectedPayeeCompanyIds, payee.companyId) })
  }

  updateSelectedInvoiceItem = (selected, isChecked, obj) => {
    const newState = { ...this.state };
    newState.selectedInvoiceItems = isChecked ? [...this.state.selectedInvoiceItems, selected] : reject(this.state.selectedInvoiceItems, { id: selected.id });

    this.setState(newState, () => this.updateSelectedPayeeCompanyIdsFromSelectedItem(obj));
  }

  onSelectAllToggle = (event, value, obj) => {
    const newState = { ...this.state };
    var index = this.state.invoiceItemsGroupedByPayee.findIndex(item => item.companyId === obj.companyId);
    set(newState, `invoiceItemsGroupedByPayee.${index}.allSelected`, value);
    if (value){
      const existingIds = new Set(this.state.selectedInvoiceItems.map(item => item.id))
      const uniqueNewItems = obj.items.filter(item => !existingIds.has(item.id));
      newState.selectedInvoiceItems = [...this.state.selectedInvoiceItems, ...uniqueNewItems];
    }
    else {
      let itemIds = map(obj.items, 'id');
      newState.selectedInvoiceItems = this.state.selectedInvoiceItems.filter(item => !itemIds.includes(item.id));
      map(this.state.invoiceItemsGroupedByPayee[index].items, (_item, itemIndex) => {
        if (_item.isChemicalItem)
          set(newState.invoiceItemsGroupedByPayee, `${index}.items.${itemIndex}.isCheckedItem`, false);
      })
    }
    this.setState(newState);
  }

  handleEmailRecipients = (event, companyId) => {
    event.stopPropagation()
    var index = this.state.invoiceItemsGroupedByPayee.findIndex(item => item.companyId === companyId);
    const selectedRecipients = get(this.state.invoiceItemsGroupedByPayee[index], 'recipients');
    this.setState({ selectedRecipients: selectedRecipients })
    APIService
      .profiles()
      .appendToUrl('employees-signature/')
      .get(null, null, { company_ids: companyId })
      .then(employees => {
        const contactsWithEmail = map(filter(employees, employee => employee.email), employee => ({ ...employee, title: `${employee.name} (${employee.email})`, value: employee.email }))
        this.setState({ emailRecipientsCompanyId: companyId, sellerEmployeeData: contactsWithEmail, recipientsDialogOpen: true })
      })
  }

  onDialogClose = () => this.setState({ recipientsDialogOpen: false, emailRecipientsCompanyId: null });

  onRecipientsChange = (event, selected) => {
    if (selected && selected.length <= 3)
      this.setState({ showRecipientsError: false });
    this.setState({ selectedRecipients: selected });
  }

  submitEmailRecipients = () => {
    if (this.state.selectedRecipients.length > 3) {
      this.setState({ showRecipientsError: true });
    }
    else {
      this.setState({ recipientsDialogOpen: false, showRecipientsError: false });
      let companyId = get(this.state.selectedRecipients, '0.companyId');
      if(!companyId && this.state.selectedRecipients.length == 0)
          companyId = this.state.emailRecipientsCompanyId;
      var index = this.state.invoiceItemsGroupedByPayee.findIndex(item => item.companyId === companyId);
      var invoiceIds = map(this.state.invoiceItemsGroupedByPayee[index]?.items, item => item.invoiceId);
      const data = {
        'selectedRecipients': this.state.selectedRecipients,
        'invoiceIds': invoiceIds,
      }
      APIService
        .invoices()
        .appendToUrl('email-recipients/')
        .put(data)
        .then(() => window.location.reload());
    }
  }

  fetchCSVData = (callback) => {
    const company = currentUserCompany();
    var param = this.state.isFilteredCsv ? 'show_filters' : '';
    if (this.state.customColumns) {
      param = param.length == 0 ? param + 'custom_csv' : param + '&custom_csv';
    }
    this.props.dispatch(setDownloadBar('Your Freight Payables CSV is getting prepared. Please visit <a href="/#/downloads">Downloads</a> in few moments.', true))
    const service = APIService.invoices();
    service.appendToUrl(`freight-payables/csv/?company_id=${company.id}&${param}`);
    service
      .get(this.props.token, {
        'Content-Type': 'text/csv',
        Accept: 'text/csv',
      })
      .then(() => this.setState({ csvPopup: false }));
    if (callback) {
      callback();
    }
  };

  customCsvEnabled(isFilteredCsv) {
    const newState = { ...this.state };
    newState.isFilteredCsv = isFilteredCsv;
    if (this.props.currentUser.company.enableCustomCsv) {
      newState.csvPopup = true;
      this.setState(newState);
    }
    else {
      newState.customColumns = false;
      this.setState(newState, this.fetchCSVData);
    }
  }

  async editCustomHeaderOptions() {
    const columnNames = await APIService.profiles().appendToUrl(`${this.props.currentUser.id}/report-preferences/freight_invoice_payables_csv/`).get();
    this.setState({ customColumnNames: columnNames, customHeaderOptions: true });
  }

  updateColumnCount(count) {
    this.setState({ customColumnTitle: `Edit Columns (${count})` });
  }

  handleFilters = bool => {
    this.setState({
      applyFilters: bool,
      openSideDrawer: bool,
    });
  };

  handleFilterState = (key, value) => {
    this.setState({ [key]: value }, () => {
      if (key === 'applyFilters' && value) {
        const { filters } = this.state;
        delete filters['commodity__id__in'];
        APIService
          .profiles()
          .filters()
          .post({ freight_invoice_payable: filters }, this.props.token)
          .then(res => {
            this.setState({ filters: get(res, 'filters.freight_invoice_payable', {}) }, () => {
              this.fetchInvoices();
            })
          });
      }
    });
  };

  customFilterValueExist = filterKeys => filterKeys.some(key => Boolean(get(this.state.filters, key)))

  filterCriteria = (key, value) => includes(FILTER_KEYS_TO_EXCLUDE, key) ? false : includes(PREDEFINED_DATE_RANGE_FILTER_KEYS, key) && value === 'custom' ? this.customFilterValueExist(get(INVOICE_FILTER_KEYS_MAPPING, key)) : value.length !== 0;

  getActionsOptionMapperListItems() {
    return [defaultViewAction];
  }

  handleGeneratePaymentRunClick() {
    const unverifiedNgrsForCompanies = uniqBy([
      ...flatten(this.state.selectedPayeeCompanyIds?.map(companyId => find(this.state.invoiceItemsGroupedByPayee, {companyId: companyId})?.unverifiedNgrs)),
      ...compact(map(this.state.selectedInvoiceItems, item => item?.sellerNgrDetails?.isVerified ? null : item.sellerNgrDetails))
    ], 'ngrNumber')

    this.setState(() => ({
      unverifiedNgrData: map(unverifiedNgrsForCompanies, sellerNgrDetails => {
        const { ngrType, ngrNumber, bankDetails } = sellerNgrDetails;

        if (isEmpty(bankDetails)) {
          return {
            companyName: '',
            ngrNumber,
            ngrType,
            shareholderPercent: '',
            bsbNumber: '',
            name: '',
            accountName: '',
            accountNumber: '',
          }
        }
        else if (isEqual(ngrType, 'shared')) {
          return map(bankDetails, item => {
            const { companyName, shareholderPercent, bsbNumber, name, accountName, accountNumber } = item;
            return {
              companyName,
              ngrNumber,
              ngrType,
              shareholderPercent,
              bsbNumber,
              name,
              accountName,
              accountNumber,
            };
          });
        } else {
          const [item] = bankDetails;
          const { companyName, shareholderPercent, bsbNumber, name, accountName, accountNumber } = item;
          return {
            companyName,
            ngrNumber,
            ngrType,
            shareholderPercent,
            bsbNumber,
            name,
            accountName,
            accountNumber,
          };
        }
      }),
    }));

    const company = currentUserCompany();
    let itemsWithIdenticalInvoices = {};
    let xeroMissingContacts = []
    const checkXeroContactStatus = company.xeroEnabled;
    let selectedInvoiceItems = [...this.state.selectedInvoiceItems, ...flatten(compact(map(this.state.selectedPayeeCompanyIds, companyId => find(this.state.invoiceItemsGroupedByPayee, {companyId: companyId})?.items)))];
    selectedInvoiceItems = uniq(selectedInvoiceItems)
    remove(selectedInvoiceItems, selectedInvoiceItem => selectedInvoiceItem.isChemicalItem);
    map(selectedInvoiceItems, selectedItem => {
      let invoice = get(selectedItem, 'invoice');
      if (checkXeroContactStatus) {
        const details = find(this.state.invoiceItems, invoiceItem => find(invoiceItem.items, item => item.ref === selectedItem.ref))
        if (details?.companyId && !details.xeroContactStatus) {
          const payee = find(this.state.companyDirectory, { id: details.companyId });
          xeroMissingContacts.push(payee.name)
        }
      }
      if (!has(itemsWithIdenticalInvoices, invoice)) {
        let invoiceItemsForSelectedItemInvoice = this.state.invoiceItems.filter(item => get(item, 'invoice') === invoice);
        if (invoiceItemsForSelectedItemInvoice.length > 1) {
          itemsWithIdenticalInvoices[invoice] = {};
          itemsWithIdenticalInvoices[invoice]['unselected'] = invoiceItemsForSelectedItemInvoice.filter(item => !this.state.selectedInvoiceItems.find(_item => item?.itemType === 'customitem' ? _item.description === item.description : _item.ref === item.ref)).map(item => item?.itemType === 'customitem' ? `${item.ref} - ${item.description}` : item.ref);
          itemsWithIdenticalInvoices[invoice]['selected'] = invoiceItemsForSelectedItemInvoice.filter(item => this.state.selectedInvoiceItems.find(_item => item?.itemType === 'customitem' ? _item.description === item.description : _item.ref === item.ref)).map(item => item?.itemType === 'customitem' ? `${item.ref} - ${item.description}` : item.ref);
        }
      }
    });

    if(checkXeroContactStatus) {
      this.state.selectedPayeeCompanyIds.forEach(companyId => {
        const selectedInvoiceItem = find(this.state.invoiceItemsGroupedByPayee, item => item.companyId == companyId)
        if(!selectedInvoiceItem?.xeroContactStatus)
          xeroMissingContacts.push(selectedInvoiceItem.payerName)
      })
    }

    xeroMissingContacts = uniq(xeroMissingContacts)

    let resultArray = []
    map(Object.entries(itemsWithIdenticalInvoices), item => {
      let invoice = get(item, '0');
      let selectedItems = get(item, '1.selected');
      let unselectedItems = get(item, '1.unselected');
      if (!isEmpty(unselectedItems)) {
        let unselectedVerb = unselectedItems.length > 1 ? 'have' : 'has';
        let selectedVerb = selectedItems.length > 1 ? 'have' : 'has';
        unselectedItems = `<ul><li>${unselectedItems.join('</li><li>')}</ul>`
        let itemRefs = `${invoice}: ${selectedItems.toString()} ${selectedVerb} been selected but ${unselectedItems} ${unselectedVerb} not been selected.`
        resultArray.push(itemRefs);
      }
    });

    let warningContent = '';
    if (!isEmpty(resultArray)) {
      resultArray = '<li>' + resultArray.join('</li><li>')
      warningContent = `<ul>${resultArray}</url>`
    }
    if (!isEmpty(xeroMissingContacts)) {
      const missingContactsText = '<li>' + xeroMissingContacts.join('</li><li>')
      warningContent += `<br/><br/><div>Following counter parties does not exist in Xero as Contacts:</div><ul>${missingContactsText}</ul>`
    }

    if (warningContent) {
      alertifyjs.confirm(
        'Warning',
        `<div>
            ${warningContent}
            <br/><br/>
            <div>
              Do you want to continue to create Payment Run ?
            </div>
        </div>`,
        () => this.setState({ openDialog: true }),
        () => { }
      );
    }
    else {
      this.setState({ openDialog: true });
    }
  }

  getSelectedSummary = () => {
    const { invoiceItemsGroupedByPayee, selectedPayeeCompanyIds, selectedInvoiceItems } = this.state
    let count = selectedInvoiceItems?.length || 0
    let total = sum(selectedInvoiceItems?.map(i => parseFloat(i?.total || 0))) || 0

    forEach(selectedPayeeCompanyIds, payeeCompanyId => {
      const payee = find(invoiceItemsGroupedByPayee, {companyId: payeeCompanyId})
      if(!payee?.items?.length || !intersection(selectedInvoiceItems?.map(item => item.id), payee?.items?.map(item => item.id))?.length) {
        count += payee.totalItems
        total += parseFloat(payee.total) || 0
      }
    })
    return {count, total}
  }

  allPayeeCompanyIds = () => compact(map(this.state.invoiceItemsGroupedByPayee, 'companyId'))

  fetchItems = (event, payeeCompanyId) => {
    event.preventDefault()
    event.stopPropagation()
    if(payeeCompanyId && this.state.expanded.includes(payeeCompanyId)) {
      this.setState({expanded: without(this.state.expanded, payeeCompanyId)})
    }
    else if(payeeCompanyId && !this.state.expanded.includes(payeeCompanyId) && !this.state.loadingItemsFor.includes(payeeCompanyId)) {
      const onComplete = () => {
        const isCompanyGroupSelected = this.state.selectedPayeeCompanyIds.includes(payeeCompanyId)
        if(isCompanyGroupSelected){
          const expandedItems = this.state.invoiceItemsGroupedByPayee
            .filter(payee => payeeCompanyId == payee.companyId)
            .reduce((allItems, payee) => [...allItems, ...(payee.items || [])], []);
          const allItems = [...this.state.selectedInvoiceItems, ...expandedItems];
          this.setState({selectedInvoiceItems: uniqBy(allItems, 'id')})
        }
      }
      this._fetchItems(payeeCompanyId, onComplete)
    }
  }

  _fetchItems = (payeeCompanyId, onComplete=null) => {
    if(payeeCompanyId && !this.state.loadingItemsFor.includes(payeeCompanyId)) {
      this.setState({expanded: uniq([...this.state.expanded, payeeCompanyId]), loadingItemsFor: uniq([...this.state.loadingItemsFor, payeeCompanyId])}, () => {
        const company = currentUserCompany();
        let payee = find(this.state.invoiceItems, {companyId: payeeCompanyId})
        if(!isEmpty(payee?.items)) {
          const newState = {...this.state}
          newState.loadingItemsFor = without(newState.loadingItemsFor, payeeCompanyId)
          this.setState(newState, () => {
            if (typeof onComplete === 'function') {
              onComplete();
            }
          });
        } else {
          APIService.invoices().appendToUrl(`freight-payable-items/?company_id=${company.id}&payee_company_ids=${payeeCompanyId}&only_items=true`).get().then(response => {
            const newState = {...this.state}
            payee.items = response[0]?.items || []
            payee.totalItems = payee.items.length || 0
            payee.total = sumBy(payee.items, 'total')
            payee.unverifiedNgrs = response[0].unverifiedNgrs || []
            payee.items.map(obj => obj.paymentDue = this.toFormattedDate(obj.paymentDue))
            payee.chemicalApplicationItems = response[0]?.chemicalApplicationItems || []
            payee = {...payee, ...response[0]}
            newState.loadingItemsFor = without(newState.loadingItemsFor, payeeCompanyId)
            this.setState(newState, () => {
              if (typeof onComplete === 'function') {
                onComplete();
              }
            });
          }).catch(() => {
            const newState = {...this.state}
            newState.loadingItemsFor = without(newState.loadingItemsFor, payeeCompanyId)
            this.setState(newState, () => {
              if (typeof onComplete === 'function') {
                onComplete();
              }
            });
          });
        }
      });
    } else if (typeof onComplete === 'function') {
      onComplete();
    }
  }

  onExpandAll = event => {
    event.stopPropagation()
    event.preventDefault()
    const payeeCompanyIds = compact(map(orderBy(this.state.invoiceItemsGroupedByPayee, 'companyName'), 'companyId'))
    if(payeeCompanyIds?.length) {
      this.props.dispatch(isLoading('LoadingExpandAll'))

      let remainingRequests = 0
      payeeCompanyIds.forEach(id => {
        const payee = find(this.state.invoiceItems, {companyId: id})
        if(isEmpty(payee?.items)) {
          remainingRequests++
        }
      })

      this.setState({expanded: [...payeeCompanyIds]}, () => {
        if(remainingRequests === 0) {
          this.props.dispatch(forceStopLoader())
          return
        }

        const onItemComplete = () => {
          remainingRequests--
          if(remainingRequests <= 0) {
            this.props.dispatch(forceStopLoader())

            const isExpandAll = Boolean(
              this.state.expanded?.length === this.state.invoiceItemsGroupedByPayee?.length &&
              this.state.expanded?.length
            )
            if(isExpandAll){
              const expandedItems = this.state.invoiceItemsGroupedByPayee
                    .filter(payee => this.state.expanded.includes(payee.companyId))
                    .reduce((allItems, payee) => [...allItems, ...(payee.items || [])], []);
              const allItems = [...this.state.selectedInvoiceItems, ...expandedItems];
              this.setState({selectedInvoiceItems: uniqBy(allItems, 'id')})
            }
          }
        }

        payeeCompanyIds.forEach(id =>
          this._fetchItems(id, onItemComplete)
        )
      })
    }
  }

  onCollapseAll = () => this.setState({expanded: []})

  onSelectAllItems = event => {
    event.persist();
    const isChecked = event.target.checked;
    const newState = {...this.state};

    if (isChecked) {
      newState.selectedPayeeCompanyIds = this.allPayeeCompanyIds();
      const expandedItems = this.state.invoiceItemsGroupedByPayee
            .filter(payee => this.state.expanded.includes(payee.companyId))
            .reduce((allItems, payee) => [...allItems, ...(payee.items || [])], []);
      const allItems = [...this.state.selectedInvoiceItems, ...expandedItems];
      newState.selectedInvoiceItems = uniqBy(allItems, 'id');
    } else {
      newState.selectedPayeeCompanyIds = [];
      newState.selectedInvoiceItems = [];
      newState.invoiceItemsGroupedByPayee.forEach((payeeGroup, groupIndex) => {
        map(payeeGroup.items, (_item, itemIndex) => {
          if (_item.isChemicalItem)
            set(
              newState.invoiceItemsGroupedByPayee,
              `${groupIndex}.items.${itemIndex}.isCheckedItem`,
              false
            );
        });
      });
    }
    this.setState({
      selectedPayeeCompanyIds: newState.selectedPayeeCompanyIds,
      selectedInvoiceItems: newState.selectedInvoiceItems
    }, () => {
      if(isChecked)
        this.fetchInvoiceDetail()
    });
  }

  fetchInvoiceDetail = () => {
    const company = currentUserCompany();
    const { selectedPayeeCompanyIds, invoiceItems } = this.state
    const toFetchCompanyIds = invoiceItems.filter(
      item => selectedPayeeCompanyIds.includes(item.companyId) && !item.total
    ).map(item => item.companyId)
    if (!toFetchCompanyIds.length || invoiceItems.length === 1)
      return
    this.props.dispatch(isLoading('some_component'))
    APIService.invoices().appendToUrl(
        `freight-payable-items/?company_id=${company.id}&payee_company_ids=${toFetchCompanyIds.join(',')}&items=false&verbose=true`
      ).get().
      then(
        response => {
          const originalPositions = Object.fromEntries(
            invoiceItems.map((item, index) => [item.companyId, index])
          );
          const filteredInvoiceItems = filter(invoiceItems, item => !toFetchCompanyIds.includes(item.companyId))
          const updatedItems = [...filteredInvoiceItems];
          response.forEach(item => {
            const originalIndex = originalPositions[item.companyId];
            if (originalIndex !== undefined && originalIndex < updatedItems.length) {
              updatedItems.splice(originalIndex, 0, item);
            } else {
              updatedItems.push(item);
            }
          });
          this.setState({
            invoiceItems: updatedItems,
          }, () => {
            this.setPayableItems()
        })
      }
    );
  }

  onSelectPayee = (event, companyId) => {
    const isChecked = event.target.checked
    this.setState({selectedPayeeCompanyIds: isChecked ? [...this.state.selectedPayeeCompanyIds, companyId] : without(this.state.selectedPayeeCompanyIds, companyId)}, () => {
      const payee = find(this.state.invoiceItemsGroupedByPayee, {companyId: companyId})
      if(isChecked && !payee?.total)
       this.fetchInvoiceDetail()
      else if(payee && payee?.items?.length)
        this.onSelectAllToggle(event, isChecked, payee)
    })
  }

  isSomeSelectedForPayee = payee => {
    const itemIds = map(payee?.items, 'id') || []
    if(itemIds.length) {
      const selected = filter(this.state.selectedInvoiceItems, item => itemIds.includes(item.id))
      return [Boolean(selected.length && selected.length !== itemIds.length), Boolean(selected.length && selected.length === itemIds.length)]
    }
    return [false, false] // some not all, all
  }

  render() {
    const {
      invoiceItemsGroupedByPayee, selectedInvoiceItems,
      recipientsDialogOpen, selectedRecipients, sellerEmployeeData,
      selectedPayeeCompanyIds
    } = this.state
    const isUsingPaymentRun = this.isUsingPaymentRun()
    const disablePaymentRun = (
      selectedPayeeCompanyIds.length === 0 &&
        !get(selectedInvoiceItems, 'length', 0) > 0
    );

    const allPayeeCompanyIds = this.allPayeeCompanyIds()
    const isExpandAll = Boolean(this.state.expanded?.length === this.state.invoiceItemsGroupedByPayee?.length && this.state.expanded?.length)
    const isEverythingSelected = Boolean(selectedPayeeCompanyIds?.length && selectedPayeeCompanyIds.length == allPayeeCompanyIds.length)
    const isSomeOfEverythingSelected = Boolean(selectedPayeeCompanyIds?.length && selectedPayeeCompanyIds.length != allPayeeCompanyIds.length)
    const { count, total} = this.getSelectedSummary()
    const invoiceItems = orderBy(invoiceItemsGroupedByPayee, obj => obj?.payeeName?.toLowerCase())
    const collapseButtonLabel =  invoiceItemsGroupedByPayee?.length > 1 ? "Collapse All": "Collapse"
    const expandButtonLabel = invoiceItemsGroupedByPayee?.length > 1 ? "Expand All": "Expand"

    return (
      <Paper>
        <div>
          <div style={{display: 'inline-flex', justifyContent: 'space-between', alignItems: 'center', width: '100%', padding: '8px 12px'}}>
            <div style={{display: 'inline-flex', alignItems: 'center'}}>
              <span style={{ fontWeight: 'bold', fontSize: '20px' }}>
                Freight Payable
                {
                  isUsingPaymentRun &&
                    <>
                      <FormControlLabel
                        onClick={this.onSelectAllItems}
                        sx={{marginLeft: '16px'}}
                        control={<Checkbox indeterminate={isSomeOfEverythingSelected} checked={isEverythingSelected} />}
                        label="Select All"
                      />
                      <Chip
                        icon={isExpandAll ? <CollapseIcon fontSize='inherit' /> : <ExpandIcon fontSize='inherit' />}
                        label={isExpandAll ? collapseButtonLabel : expandButtonLabel}
                        onClick={isExpandAll ? this.onCollapseAll : this.onExpandAll}
                      />
                      {
                        count > 0 &&
                          <span style={{fontSize: '1rem', marginLeft: '20px', fontWeight: 'normal'}}>
                            Selected Items: <b style={{marginRight: '10px'}}>{count.toLocaleString()}</b> Total: <b>{`${getCountryCurrency()} ${Number(total.toFixed(2)).toLocaleString()}`}</b>
                          </span>
                      }
                    </>
                }
              </span>
            </div>
            <div style={{display: 'inline-flex', alignItems: 'center'}}>
              <ListingControls
                controls={[
                  {
                    type: 'new',
                    order: 1,
                    hidden: !isUsingPaymentRun,
                    props: {
                      disabled: disablePaymentRun,
                      label: 'Payment Run',
                      onClick: this.handleGeneratePaymentRunClick
                    }
                  },
                  {
                    type: 'filter',
                    props: {
                      value: this.state.applyFilters,
                      onClick: () => this.handleFilters(true),
                      applied: Object.entries(this.state.filters).filter(val => this.filterCriteria(val[0], val[1]))?.length || 0
                    },
                  },
                  {
                    type: 'report',
                    props: {
                      defaultHandler: () => this.customCsvEnabled(false),
                      showMenus: !isEmpty(Object.entries(this.state.filters).filter(val => get(val, '1.length', 0) !== 0)),
                      optionMapper: [
                        { name: 'Complete List', description: 'List of outstanding payments for freights to be made', fx: () => this.customCsvEnabled(false) },
                        { name: 'Filtered List', description: 'List of outstanding payments for freights to be made matching current filter selection', fx: () => this.customCsvEnabled(true) },
                      ]
                    }
                  },
                  {
                    type: 'action',
                    props: {
                      showMenus: true,
                      optionMapper: this.getActionsOptionMapperListItems()
                    }
                  },
                ]}
              />
            </div>
          </div>
          <Dialog open={this.state.csvPopup} onClose={() => this.setState({ csvPopup: false })} aria-labelledby='form-dialog-title' fullWidth>
            <DialogTitleWithCloseIcon
              onClose={() => this.setState({ csvPopup: false })}
              closeButtonStyle={{ marginTop: '0px' }}
              id='form-dialog-title'>
              Freight Invoice Payable Report
            </DialogTitleWithCloseIcon>
            <DialogContent style={{ marginTop: '15px' }}>
              <span style={{ float: 'left' }}>Select checkbox for custom csv download</span>
              <div className='col-sm-6 padding-reset' style={{ marginTop: '10px' }}>
                <Checkbox
                  id={'custom-headers'}
                  checked={this.state.customColumns}
                  style={{ height: '40px' }}
                  onChange={() => this.setState({ customColumns: !this.state.customColumns })}
                />
                Custom Columns
                <a onClick={() => this.editCustomHeaderOptions()} hidden={!this.state.customColumns} style={CUSTOM_HEADER_EDIT}>View & Update</a>
              </div>
              <SideDrawer
                isOpen={this.state.customHeaderOptions}
                title={this.state.customColumnTitle}
                onClose={() => this.setState({ customHeaderOptions: false })}
                size="small"
              >
                <CustomHeaderOptions
                  customColumns={this.state.customColumnNames}
                  closeDrawer={() => this.setState({ customHeaderOptions: false })}
                  user={this.props.currentUser}
                  csv_type="freight_invoice_payables_csv"
                  updateColumnCount={(count) => this.updateColumnCount(count)}
                />
              </SideDrawer>
            </DialogContent>
            <DialogActions>
              <Button
                type='button'
                onClick={() => this.setState({ csvPopup: false })}
                variant='outlined'>
                Cancel
              </Button>
              <Button type='button' onClick={() => this.fetchCSVData()} color='primary' variant='outlined'>
                Download
              </Button>
            </DialogActions>
          </Dialog>
          {this.state.applyFilters && (
            <SideDrawer isOpen={this.state.openSideDrawer} title='Filters' size='big' onClose={() => this.handleFilters(false)} app='filters'>
              <InvoiceFilters
                handleFilterState={this.handleFilterState}
                filters={this.state.filters}
                freightPayableInvoice
              />
            </SideDrawer>
          )}
          <Divider light />
          {!isEmpty(invoiceItemsGroupedByPayee) &&
           <Virtuoso
             data={invoiceItems}
             style={{ height: '100vh' }}
             itemContent={(index, obj) => {
               const isExpanded = this.state.expanded.includes(obj.companyId)
               const [isSomeSelected,] = this.isSomeSelectedForPayee(obj)
               return (
                 <div id={index} key={index} style={{marginTop: (index == 0 || isExpanded) ? '6px': 0}}>
                   <div style={{ background: '#F5F5F5', display: 'inline-block', width: '100%' }}>
                     <PartyListItem
                       party={obj}
                       isExpanded={isExpanded}
                       isUsingPaymentRun={isUsingPaymentRun}
                       onClick={this.fetchItems}
                       onRecipientEdit={this.handleEmailRecipients}
                       controls={

                         <div style={{ float: "right", maxWidth: '400px' }}>
                           {
                             isUsingPaymentRun &&
                               <div style={{ float: "right", marginRight: '20px', lineHeight: '50px', maxWidth: '400px' }}>
                                 <FormControlLabel
                                   sx={{marginRight: '20px'}}
                                   control={
                                     <Badge badgeContent={obj.totalItems} color='secondary' sx={{'.MuiBadge-badge': {left: '-20px', top: '16px'}}} anchorOrigin={{
                                              vertical: 'top',
                                              horizontal: 'left',
                                            }}
                                     >
                                       <Checkbox
                                         sx={{padding: '6px'}}
                                         indeterminate={isSomeSelected}
                                         checked={selectedPayeeCompanyIds.includes(obj.companyId)}
                                         onChange={event => this.onSelectPayee(event, obj.companyId)}
                                         size='small'
                                       />
                                     </Badge>
                                   }
                                   label="Select All"
                                 />
                               </div>
                           }
                         </div>
                       }
                     />
                   </div>
                   <div style={{padding: '0 12px', marginTop: isExpanded ? '-0.5%' : 0}}>
                     {
                       (isEmpty(obj.items) || !isExpanded) ?
                         (
                           isExpanded ? <Skeleton width='100%' height={100} /> : <div />
                         ) :
                         <InvoicePayableTable
                           items={obj.items}
                           columns={isUsingPaymentRun ? [
                             {
                               size: 'small',
                               sx:{padding: 0},
                               align: 'left',
                               header: 'All',
                               checkbox: true,
                               className: 'xxxsmall',
                               onChange:(selected, isChecked) => this.updateSelectedInvoiceItem(selected, isChecked, obj),
                               func: item => this.isSelected(item, obj),
                               selectAll: true,
                               checked: selectedPayeeCompanyIds?.includes(obj.companyId),
                               indeterminate: isSomeSelected,
                               onSelectAll: event => this.onSelectPayee(event, obj.companyId)
                             },
                             ...FREIGHT_PAYABLE_ITEM_COLUMNS
                           ] : [...FREIGHT_PAYABLE_ITEM_COLUMNS]}
                           isSubscriber={obj.isSubscriber}
                         />
                     }
                   </div>
                   {
                     recipientsDialogOpen &&
                       <Dialog open keepMounted fullWidth onClose={this.onDialogClose}>
                         <DialogTitleWithCloseIcon
                           onClose={this.onDialogClose}
                           closeButtonStyle={{ marginTop: '0px' }}
                           id='form-dialog-title'>
                           Update Recipients
                         </DialogTitleWithCloseIcon>
                         <DialogContent>
                           <React.Fragment>
                             <div>
                               {'Update Recipients'}
                             </div>
                             <div>
                               <EmailAutocomplete
                                 options={sellerEmployeeData}
                                 onChange={this.onRecipientsChange}
                                 selected={selectedRecipients}
                               />
                             </div>
                             {
                               this.state.showRecipientsError &&
                                 <div style={{ marginTop: '3px', color: '#FF0000', fontSize: '15px' }}>
                                   You can select only 3 emails.
                                 </div>
                             }
                           </React.Fragment>
                         </DialogContent>
                         <DialogActions>
                           <Button color='default' onClick={this.onDialogClose}>Cancel</Button>
                           <Button onClick={this.submitEmailRecipients}>Proceed</Button>
                         </DialogActions>
                       </Dialog>
                   }
                 </div>
               )}
                         }
           />
          }
        </div>
        <Dialog
          open={this.state.openDialog}
          onClose={() => this.setState({ openDialog: false })}
          aria-labelledby="form-dialog-title"
          fullWidth
          PaperProps={{ style: { maxWidth: !isEmpty(this.state.unverifiedNgrData) ? '1400px' : '600px' } }}
        >
          <DialogTitleWithCloseIcon
            onClose={() => this.setState({ openDialog: false })}
            closeButtonStyle={{ marginTop: '0px' }}
            id='form-dialog-title'>
            Payment Run
          </DialogTitleWithCloseIcon>
          <DialogContent style={{ marginTop: '15px' }}>
            <span style={{ float: 'left' }}>
              This will generate invoices and a payment run for the selected items.
            </span>
            <div style={{ marginTop: '35px', marginBottom: '20px' }}>
              <CommonDatePicker
                id='bankProcessingDate'
                floatingLabelText='Bank Processing Date'
                onChange={this.handleBankProcessingDateChange}
                errorText={this.state.bankProcessingDate.error}
                value={this.state.bankProcessingDate.value}
                minDate={new Date()}
              />
            </div>
            {
              !isEmpty(this.state.unverifiedNgrData) &&
                <div>
                  <span style={{ marginTop: '35px' }}>
                    The following NGR&apos;s bank details are not verified from NGR.com. Please verify the account details before making the payment.
                  </span>
                  <Table style={{ border: "none" }}>
                    <TableHead>
                      <TableRow>
                        <TableCell>Company Name</TableCell>
                        <TableCell>NGR Number</TableCell>
                        <TableCell>NGR Type</TableCell>
                        <TableCell>Shareholder Percent</TableCell>
                        <TableCell>{`${getCountryLabel('bsb')} Number`}</TableCell>
                        <TableCell>Bank Name</TableCell>
                        <TableCell>Account Name</TableCell>
                        <TableCell>Account Number</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {map(this.state.unverifiedNgrData, row => (
                        <TableRow key={row.ngrNumber}>
                          <TableCell>{row.companyName}</TableCell>
                          <TableCell>{row.ngrNumber}</TableCell>
                          <TableCell>{row.ngrType}</TableCell>
                          <TableCell>{row.shareholderPercent}</TableCell>
                          <TableCell>{row.bsbNumber}</TableCell>
                          <TableCell>{row.name}</TableCell>
                          <TableCell>{row.accountName}</TableCell>
                          <TableCell>{row.accountNumber}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
            }

          </DialogContent>
          <DialogActions>
            <Button
              type='button'
              onClick={() => this.setState({ openDialog: false })}
              variant='outlined'>
              Cancel
            </Button>
            <Button type='button' onClick={this.generatePaymentRun} color='primary' variant='contained'>
              OK
            </Button>
          </DialogActions>
        </Dialog>

      </Paper>
    )
  }
}

const mapStateToProps = state => {
  return {
    breadcrumbs: state.main.breadcrumbs,
    currentUser: state.main.user.user,
  };
};

const mapDispatchToProps = dispatch => ({
  isLoading: (waitForComponent) => dispatch(isLoading(waitForComponent)),
  setLoadingText: text => dispatch(setLoadingText(text)),
  forceStopLoader: () => dispatch(forceStopLoader()),
  setDownloadBar: (message, isOpen, onClose) => dispatch(setDownloadBar(message, isOpen, onClose)),
});

export default connect(mapStateToProps, mapDispatchToProps)(FreightInvoicePayable);
