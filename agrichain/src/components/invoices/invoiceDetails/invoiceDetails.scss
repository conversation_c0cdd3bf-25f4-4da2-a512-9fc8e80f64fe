@import "../../../common/variables.scss";
.invoice-status {
  display: flex;
  margin-bottom: 10px;
  padding: 0 30px;
  &.new {
      margin-bottom: 8px;
      padding: 0;
  }
  .invoice-status-bar {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    width: 100%;
    li {
      border-right: 1px solid $colorGrey;
      min-width: 180px;
      padding: 12px 10px;
      display: flex;
      align-items: center;
      justify-content: center;

      &.status {
        text-align: center;
        line-height: 1.2;
        padding-left: 0;
      }

      &.status-tags {
        flex-direction: row;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding: 8px;
        width: 28%;
        border-right: 0;
      }
    }
    &.new {
        li {
            padding: 8px;
            min-width: none;
        }
    }

    .field-label {
      color: $colorLightGrey;
      font-size: 1rem;
      font-weight: 300;
    }

    .field-value {
      font-size: 1.250rem;
      color: $colorBlack;
    }

    .created-at {
      color: $colorLightGrey;
      font-size: 12px;
      font-weight: 300;
      padding-top: 2px;
    }

    .field-tag {
      color: $colorBlack;
      font-size: 11px;
      padding: 2px 5px;
      min-width: 110px;
      border-radius: 4px;
      background: $colorGrey;
      margin:5px;
      text-align: center;
      display: block;
    }
  }

  & .status-actions {
    display: flex;
    align-items: center;
    width: 26%;
  }

  & .status-actions-container {
      display: inline-block;
      flex-direction: column;
      height: 100%;
      justify-content: center;
      align-items: center;

      h4 {
        margin: 10px 0 5px 0;
        font-size: 1.10rem;
      }
      a {
        margin-bottom: 10px;
      }

      a:first-child {
        margin-right: 10px;
      }

      & .status-actions-wrap {
        display: flex;
        flex-direction: column;
        padding-bottom: 15px;
      }

    &.grey-bg {
      background: $colorLightestGrey;
    }
  }
}

.invoice-details-container {
  .invoice-details-section-container {
    padding: 20px 30px;
    margin-top: 10px;
    margin-bottom: 10px;
    color: #112c42;
    box-shadow: none;
    h2 {
      margin: 0;
      padding: 0;
      font-size: 20px;
      font-weight: 600;
      .expand-icon {
        width: auto;
        float: right;
        svg {
          vertical-align: middle;
        }
      }
    }

    .section-title {
      font-size: 13px;
      font-weight: 500;
      margin: 0;
      padding: 7px 0;
    }

    .section-details-container, .section-details-container-4, .section-details-container-2 {
      display: grid;
      grid-template-columns: 33% 33% 33%;
      margin-top: 10px;
      > div {
        border-right: 1px solid #e0e0e0;
        padding: 0 10px;
        &:last-child {
          border-right: 0;
        }
      }
      .section-details {
        margin: 25px 0;
      }
      ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
        li {
          padding-top: 7px;
          font-size: 13px;
          span {
            display: inline-block;
            &.field-label {
              width: 41%;
              font-weight: 500;
              color: #808080;
              padding-right:5px;
            }
            &.field-label-black-bold {
              width: 50%;
              font-weight: 500;
              color: #000;
              padding-right:5px;
            }
            &.field-value {
              width: 59%;
              font-weight: normal;
              padding-left:5px;
            }
            &.field-value-right {
              width: 50%;
              font-weight: normal;
              padding-left:5px;
              text-align: right;
            }
          }
        }
      }
    }
    .section-details-container-4 {
      grid-template-columns: 25% 25% 25% 25%;
    }
    .section-details-container-2 {
      grid-template-columns: 50% 50%;
    }
  }
}

.amend-section{
  padding-left: 0px;
}

.amend-section ul {
  padding: 0px;
  li {
    list-style-type: none;
  }
  .field-label {
    display: inline-block;
    width: 40%;
    font-weight: 500;
    color: #808080;
    font-size: 13px;
  }

  .field-value {
    display: inline-block;
    width: 60%;
    font-size: 13px;
  }

  .full-width-field-value {
    display: inline-block;
    width: 100%;
    font-size: 13px;
  }
}
.invoice-item-heading {
  max-height: 48px;
  min-height: 48px !important;
  background-color: #F7F7F7;
  box-shadow: inset 0 -1px 0 0 #E0E0E0, inset 0 0px 0 0 #E0E0E0;
  padding: 13.5px 30px;
  font-weight: 500;
  margin-top: 0px;
  margin-bottom: 0px;
}
.total-row {
  padding: 10px !important;
  border-top: 1px solid lightgray;
  border-bottom: 1px solid lightgray;
  margin-left: -30px;
  margin-right: -30px;
  background-color: rgb(247,247,247);
  font-weight: 600;
}
.table-container {
  width: 100%;
}
.action-centre-group.invoice-section-group {
  margin-right: -30px;
  margin-left: -30px;
  margin-top: 15px;
  div:first-child {
    box-shadow: none;
  }
}

.total-section {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    margin-bottom: 0px !important;
    li.highlighted {
        background-color: #f7f7f7;
        margin-left: -30px;
        margin-right: -40px;
        padding-bottom: 0px;
        padding-left: 30px;
        padding-right: 40px;
    }
}
.padding-right-30 {
    padding-right: 30px !important;
}
.padding-left-30 {
    padding-left: 30px !important;
}

.flex-direction-column {
  flex-direction: column;
}

.flex-direction-row {
  flex-direction: row !important;
}

.padding-left-20 {
    padding-left: 20px !important;
}

.sub-heading {
  height: 24px;
  width: 132.3px;
  color: #112C42;
  font-family: Roboto;
  font-size: 20px;
  line-height: 24px;
}

.payment-summary {
  color: #999999;
  font-family: Roboto;
  font-size: 16px;
  line-height: 26px;
  .payment-summary-value {
    color: #112C42;
    font-weight: 500;
  }
}

.margin-right-0 {
  margin-right: 0px !important;
}

tr.invoice-item-row:nth-of-type(odd) {
    background-color: rgba(245, 245, 245, 0.35);
}
tr.invoice-item-row.single-row {
    background-color: #FFF;
}
