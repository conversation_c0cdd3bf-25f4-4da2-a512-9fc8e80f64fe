.full-screen-container {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background: url('/images/background.jpg') left center no-repeat;
  background-size: cover;

  .content-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 536px;
  }
}
.content-box-link{
  color: #ffffff;
  text-align: center;
  padding: 5px;
  margin-top: 20px;

  a {
    color: #ffffff;
    text-decoration: underline;
  }
}

.content-box {
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.16), 0 2px 5px 0 rgba(0, 0, 0, 0.26);

  &--header {
    background: #112c42;
    height: 64px;
    padding-top: 13px;
    border-radius: 4px 4px 0 0;

    img {
      display: block;
      max-width: 100%;
      height: 40px;
      margin: 0 auto;
    }
  }
  &--content {
    padding: 16px;

    h4 {
      font-weight: 400;
      font-size: 20px;
      padding-left: 28px;
      margin: 0;

      &.accept-title {
        background: url('/images/accept-icon.svg') left center no-repeat;
        background-size: 24px;
      }

      &.reject-title {
        background: url('/images/reject-icon.svg') left center no-repeat;
        background-size: 24px;
      }
    }

    p {
      margin: 8px 0 30px;
      padding: 0;
      font-size: 14px;
    }

    .accept-field {
      width: 256px;
    }

    .reject-reason-field {
      .Textarea-root-141 {
        height: auto !important;
      }
      label + .MuiInput-formControl-129 {
        min-height: 96px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        padding: 8px;
      }
      .MuiInputLabel-formControl-117 {
        left: 10px;
      }
    }

    button {
      display: block;
      margin: 20px 0 0 auto !important;
    }
  }
}
