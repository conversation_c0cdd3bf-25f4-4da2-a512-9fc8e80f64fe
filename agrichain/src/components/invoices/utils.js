import { pick, get, compact, isNumber, cloneDeep } from 'lodash';
import { isSystemCompany, currentUser, getIndexOfSpliceItem, currentUserCompany } from '../../common/utils';
import { INVOICE_TABLE_OPTIONS_ITEMS, PAYMENT_RUN_TABLE_OPTIONS_ITEMS, WAREHOUSE_DASHBOARD_TABLE_OPTIONS_ITEMS } from '../../common/constants';

export const getActionMenuOptions = (invoice, fromDetails) => {
  const options = cloneDeep(INVOICE_TABLE_OPTIONS_ITEMS());
  const data = pick(invoice, ['id', 'entity'])
  const isSystem = isSystemCompany()

  options.forEach(option => {
    if (option.key === 'download_pdf') {
      option.component = true
      if(invoice?.type && invoice?.type.includes('Commodity Contract') && (isSystem || compact([invoice?.payee?.companyId, invoice?.payeeCompanyId]).includes(currentUser().companyId))) {
        option.subItems = [
          {
            component: true,
            data: {...data, party: 'seller'},
            key: "download_seller_pdf",
            text: "Seller's PDF"
          },
          {
            component: true,
            data: {...data, party: 'buyer'},
            key: "download_buyer_pdf",
            text: "Buyer's PDF"
          }
        ]
        option.hasFunc = false
      }
      else {
        option.data = data
        option.subItems = undefined
        option.hasFunc = true
      }
    }
    if (option.key === 'invoice_send_email' && invoice?.lastEmailAction?.actionType && !invoice?.lastEmailAction?.hasPendingEmail && currentUserCompany()?.enableResendingEmail)
      option.text = 'Resend Email';
  });
  if (isSystem) options.push({ key: 'regenerate_pdf', text: 'Regenerate PDF' });

  if(invoice?.xeroId && fromDetails)
    options.push({key: 'sync_xero_payments', text: 'Sync Xero Payments'})

  if(invoice?.isPartOfValidPaymentRuns)
    options.push({key: 'remove_from_payment_run', text: 'Remove from Payment Run'})

  const invoiceStatus = get(invoice, 'status') || get(invoice, 'statusDisplayName')
  const voidOrDuplicateSpliceItem = (invoiceStatus?.toLowerCase() == 'void') ? {key: 'void_and_duplicate'}: {key: 'duplicate'};
  if (invoiceStatus && voidOrDuplicateSpliceItem){
    const voidOrDuplicateItemIndex = getIndexOfSpliceItem(options, voidOrDuplicateSpliceItem);
    options.splice(voidOrDuplicateItemIndex, 1);
  }

  return options;
};

export const getWarehouseDashboardMenuOptions = invoice => {
  let options =   [...WAREHOUSE_DASHBOARD_TABLE_OPTIONS_ITEMS]

  if(invoice?.statusDisplayName === 'New')
    options.splice(6, 1)

  if(invoice?.systemAmount - invoice?.invoicedAmount)
    return options

  return [options[3], options[4], options[5], options[6]]
};

export const getPaymentRunMenuOptions = paymentRun => {
  let options =   [...PAYMENT_RUN_TABLE_OPTIONS_ITEMS];
  let paymentRunStatus = get(paymentRun, 'statusDisplay');
  if (paymentRunStatus !== 'Void') {
    options.push({ key: 'download_aba', text: 'Download ABA'});
  }
  if (paymentRunStatus !== 'Paid' && paymentRunStatus !== 'Void') {
    options.push({ key: 'mark_paid', text: 'Mark Paid'});
    options.push({ key: 'update_bank_processing_date', text: 'Update Bank Processing Date'});
    options.push({ key: 'void', text: 'Void'});
  }
  if (paymentRunStatus === 'Paid') {
    options.push({ key: 'un_paid', text: 'Mark Unpaid'});
  }
  if(!get(paymentRun, 'payerNgr.bankDetails[0].name'))
    options.push({key: 'assign_payer_ngr', text: 'Assign Buyer NGR/Bank'})

  return options;
};


export const toNumFromCurrencyValue = (value, currency) => {
  if(value && currency) {
    return parseFloat(value.replace(currency, '').replace(',', '').replace(' ', ''))
  }
  return 0
}

export const toCurrencyValueFromNum = (value, currency) => {
  if(value && currency) {
    const sign = value < 0 ? '-' : ''
    return `${sign}${currency} ${parseFloat(Math.abs(value)).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`
  }
  return `${currency} 0.00`
}

export const toNumFromTonnageValue = value => {
  if(isNumber(value))
    return value
  return value ? parseFloat(value.match(/\d+(\.\d+)?/)[0]) : value || 0
}
