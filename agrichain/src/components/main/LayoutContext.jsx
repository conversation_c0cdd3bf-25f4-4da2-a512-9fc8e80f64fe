import React from 'react';
export const AppContext = React.createContext('');
export const AppProvider = AppContext.Provider;
export const AppConsumer = AppContext.Consumer;

const LayoutContext = ({ subPages }) => {
  const [device, setDevice] = React.useState('web');
  const [mostUsedProducts, setMostUsedProducts] = React.useState({})
  return (
    <AppProvider
      value={{
        setDevice: setDevice,
        device: device,
        isMobileDevice: device === 'mobile',
        mostUsedProducts: mostUsedProducts,
        setMostUsedProducts: setMostUsedProducts
      }}>
      {subPages}
    </AppProvider>
  )
}
export default LayoutContext;
