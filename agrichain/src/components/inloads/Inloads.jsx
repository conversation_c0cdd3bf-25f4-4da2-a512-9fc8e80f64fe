import React from 'react';

import { connect } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import isEqual from 'lodash/isEqual';
import pickBy from 'lodash/pickBy';
import has from 'lodash/has';
import get from 'lodash/get';
import InloadsHomeStorageTable from '../../containers/InloadsHomeStorageTable';
import CreateStorageInload from '../../containers/CreateStorageInload';
import UpdateInload from '../../containers/UpdateInload';
import { getStorageInloads } from '../../actions/api/inloads';
import { getCommodities } from '../../actions/api/commodities';
import { receiveInloads } from '../../actions/companies/inloads';
import AddButton from '../common/AddButton';
import SideDrawer from '../common/SideDrawer';
import {setBreadcrumbs} from '../../actions/main';
import queryString from 'query-string';
import { withRouter } from 'react-router-dom';

class Inloads extends React.Component {
  constructor(props) {
    super(props);
    const queryParams = queryString.parse(props.location.search);
    this.state = {
      isAddInloadSideDrawerOpened: false,
      isEditInloadSideDrawerOpened: false,
      inload: null,
      movement: null,
      commodityId: has(queryParams, 'commodityId') ? parseInt(get(queryParams, 'commodityId')) : null,
      ngrId: has(queryParams, 'ngrId') ? parseInt(get(queryParams, 'ngrId')) : null,
      gradeId: has(queryParams, 'gradeId') ? parseInt(get(queryParams, 'gradeId')) : null,
      season: get(queryParams, 'season', null),
    };

    this.openAddInloadSideDrawer = this.openAddInloadSideDrawer.bind(this);
    this.closeAddInloadSideDrawer = this.closeAddInloadSideDrawer.bind(this);
    this.closeEditInloadSideDrawer = this.closeEditInloadSideDrawer.bind(this);
  }

  componentDidMount() {
    const {
      getInloads, storageId, commodities, getCommodities
    } = this.props;

    getInloads(storageId, this.getQueryParams());

    if (isEmpty(commodities))
      getCommodities();
  }

  getBreadcrumbs() {
    const { headerUrl, headerText, breadcrumbs } = this.props;
    let newBreadcrumbs = [];
    if(!isEmpty(breadcrumbs))
      newBreadcrumbs = [breadcrumbs[0]];
    newBreadcrumbs = [
      ...newBreadcrumbs,
      {text: headerText, route: headerUrl},
      {text: 'Inloads'}
    ];

    return newBreadcrumbs;
  }

  componentWillUnmount() {
    if(get(this, 'props.resetInloads'))
      this.props.resetInloads();
  }

  componentDidUpdate(prevProps){
    const queryParams = queryString.parse(this.props.location.search);
    const commodityId = has(queryParams, 'commodityId') ? parseInt(get(queryParams, 'commodityId')) : null;
    const ngrId = has(queryParams, 'ngrId') ? parseInt(get(queryParams, 'ngrId')) : null;
    const gradeId = has(queryParams, 'gradeId') ? parseInt(get(queryParams, 'gradeId')) : null;
    const season = get(queryParams, 'season');
    const newState = { ...this.state };
    if(commodityId !== newState.commodityId){
      newState.commodityId = commodityId;
    }
    if(ngrId !== newState.ngrId){
      newState.ngrId = ngrId;
    }
    if(gradeId !== newState.gradeId){
      newState.gradeId = gradeId;
    }
    if(season !== newState.season){
      newState.season = season;
    }
    if(!isEqual(this.state,newState)){
      this.setState(newState,() => {
        this.props.getInloads(this.props.storageId, this.getQueryParams());
      });
    }
    if (prevProps.storageType != this.props.storageType) {
      this.props.getInloads(this.props.storageId, this.getQueryParams());
    }
    const newBreadcrumbs = this.getBreadcrumbs();
    if(!isEqual(this.props.breadcrumbs, newBreadcrumbs))
      this.props.setBreadcrumbs(newBreadcrumbs);
  }

  getQueryParams() {
    let params = {
      commodity_id: this.state.commodityId,
      grade_id: this.state.gradeId,
      season: this.state.season,
    };
    if(this.state.ngrId){
      params.ngr_id = this.state.ngrId;
    }
    return pickBy(params, value => value);
  }

  openAddInloadSideDrawer() {
    this.setState({ isAddInloadSideDrawerOpened: true, isEditInloadSideDrawerOpened: false });
  }

  closeAddInloadSideDrawer() {
    this.setState({ isAddInloadSideDrawerOpened: false });
  }

  closeEditInloadSideDrawer() {
    this.setState({ isEditInloadSideDrawerOpened: false, inload: null, movement: null });
  }

  async handleDefaultCellClick(inload, context) {
    if(inload.freightMovementId)
      window.open(`#/freights/movements/${inload.freightMovementId}/details`);
    else
      context.setState({
        isEditInloadSideDrawerOpened: true,
        isAddInloadSideDrawerOpened: false,
        inload
      });
  }

  render() {
    return (
      <div>
        {
          !this.props.dontShowAddButton &&
          <AddButton
            label="Inload"
            onClick={this.openAddInloadSideDrawer}
            app="load"
          />
        }
        <InloadsHomeStorageTable
          ignoreOrder={true}
          handleDefaultCellClick={this.handleDefaultCellClick}
          cellClickContext={this}
        />
        <SideDrawer
          isOpen={this.state.isAddInloadSideDrawerOpened}
          title="Add Inload"
          size="big"
          onClose={this.closeAddInloadSideDrawer}
          app="load"
          isCreate={true}
        >
          <CreateStorageInload
            companyId={this.props.companyId}
            farmId={this.props.farmId}
            storageId={this.props.storageId}
            closeDrawer={this.closeAddInloadSideDrawer}
            showNgrField={this.props.showNgrField}
            isCreate={true}
          />
        </SideDrawer>
        <SideDrawer
          isOpen={this.state.isEditInloadSideDrawerOpened}
          title="Edit Inload"
          size="big"
          onClose={this.closeEditInloadSideDrawer}
          app="load"
          isCreate={false}
        >
          <UpdateInload
            companyId={this.props.companyId}
            farmId={this.props.farmId}
            storageId={this.props.storageId}
            inload={this.state.inload}
            closeDrawer={this.closeEditInloadSideDrawer}
            showNgrField={this.props.showNgrField}
            isCreate={false}
          />
        </SideDrawer>
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    token: state.main.user.token,
    commodities: state.master.commodities.items,
    breadcrumbs: state.main.breadcrumbs,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    resetInloads: () => dispatch(receiveInloads([])),
    getInloads: (storageId, queryParams) => dispatch(
      getStorageInloads(storageId, receiveInloads, queryParams)
    ),
    getCommodities: () => dispatch(getCommodities()),
    setBreadcrumbs: (breadcrumbs) => dispatch(setBreadcrumbs(breadcrumbs)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(Inloads));
