import React from 'react';
import Button from '@mui/material/Button';
import HelpIcon from './common/icons/Help';
import { redirectToFreshdesk } from '../common/utils';

const Help = () => {
  const onClick = event => {
    event.preventDefault();
    redirectToFreshdesk();
  };

  return (
    <div className="left-bar-control">
      <Button fullWidth type="button" href="#" onClick={onClick}>
        <HelpIcon style={{marginLeft: '-2px', marginRight: '17px'}}/>
        <span className="nav-text">Help</span>
      </Button>
    </div>
  );
};

export default Help;
