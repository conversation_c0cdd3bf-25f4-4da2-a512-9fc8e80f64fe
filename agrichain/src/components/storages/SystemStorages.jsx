import React from 'react';

import { connect } from 'react-redux';
import SystemStoragesTable from '../../containers/SystemStoragesTable';
import CreateSystemStorage from '../../containers/CreateSystemStorage';
import { getSystemStorages } from '../../actions/api/storages';
import {
  receiveSystemStorages,
  clickAddSystemStorageButton
} from '../../actions/companies/storages';
import AddButton from '../common/AddButton';
import SideDrawer from '../common/SideDrawer';
import get from 'lodash/get';

class SystemStorages extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      openSideDrawer: false,
    };
    this.handleAddSystemStorageButtonClick = this.handleAddSystemStorageButtonClick.bind(this);
    this.onCloseSideDraw = this.onCloseSideDraw.bind(this);
  }

  componentDidMount() {
    this.props.getSystemStorages(this.props.farmId);
  }

  handleAddSystemStorageButtonClick() {
    this.props.handleAddSystemStorageButtonClick();
    this.setState({
      openSideDrawer: true,
    });
  }

  onCloseSideDraw() {
    this.setState({
      openSideDrawer: false,
    });
  }

  render() {
    return (
      <div>
        {
          this.props.canActOnFarm ?
          <AddButton label="System Storage" onClick={this.handleAddSystemStorageButtonClick} app="storage" />
          : null
        }
        <SystemStoragesTable/>
        {this.props.isCreateFormDisplayed &&
         <SideDrawer
           isOpen={this.state.openSideDrawer}
           title="Add System Storage"
           size="medium"
           onClose={this.onCloseSideDraw}
           app="storage"
           >
           <CreateSystemStorage closeDrawer={this.onCloseSideDraw} farmId={this.props.farmId} companyId={get(this.props.selectedFarm, 'companyId')} />
         </SideDrawer>
        }
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    isCreateFormDisplayed: state.companies.systemStorages.isCreateFormDisplayed,
    selectedFarm: state.companies.farms.selectedFarm,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    getSystemStorages: (farmId) => dispatch(getSystemStorages(farmId,receiveSystemStorages, '?no_stocks')),
    handleAddSystemStorageButtonClick: () => dispatch(clickAddSystemStorageButton()),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(SystemStorages);
