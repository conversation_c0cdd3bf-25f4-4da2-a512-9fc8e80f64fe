import { Paper, Tab, Tabs } from '@mui/material';
import React from 'react';
import { connect } from 'react-redux';
import {isEqual} from 'lodash';
import { setBreadcrumbs, setHeaderText } from '../../actions/main';
import { getCompanyDetails } from '../../actions/companies';
import SendAlerts from './SendAlerts';
import ReceiveAlerts from './ReceiveAlerts';
import { isCompanyAdminOrObserver, isCurrentUserCompanyPlanLite, isSystemCompany } from '../../common/utils';

class Alerts extends React.Component {

  constructor(props) {
    super(props);
    this.SEND_ALERT = 'send'
    this.RECEIVE_ALERT = 'receive'
    this.canDisplaySendAlertTab = (
      !isCurrentUserCompanyPlanLite() &&
      (isCompanyAdminOrObserver() || isSystemCompany())
    )
    this.state = {
      selectedTab: this.SEND_ALERT
    }
  }

  componentDidMount() {
    this.setBreadcrumbs();
    if (this.shouldFetchCompany()) {
      this.props.getCompany(this.props.companyId);
    }
  }

  componentDidUpdate() {
    if (this.props.selectedCompany) {
      if (this.props.headerText !== this.props.selectedCompany.name) {
        this.props.setHeaderText(this.props.selectedCompany.name);
      }
      this.setBreadcrumbs();
    }
    if (this.shouldFetchCompany()) {
      this.props.getCompany(this.props.companyId);
    }
  }

  shouldFetchCompany() {
    return !this.props.selectedCompany || (this.props.selectedCompany.id !== this.props.companyId);
  }

  setBreadcrumbs() {
    if (this.props.selectedCompany) {
      const breadcrumbs = [
        { text: 'Companies', route: '/companies' },
        { text: this.props.selectedCompany.name, onClick: this.props.onDetailsClick, route: '/companies/' + this.props.selectedCompany.id + '/details' },
        { text: 'Alerts' }
      ];
      if (!isEqual(this.props.breadcrumbs, breadcrumbs)) {
        this.props.setBreadcrumbs(breadcrumbs);
      }
    }
  }

  onTabChanges = (event, tabValue) => {
    this.setState({selectedTab: tabValue})
  }

  render() {
    return (
      <Paper className='paper-table-paginated'>
        {this.canDisplaySendAlertTab && (
          <div className="subTab">
            <Tabs
              className="subTab-header"
              value={this.state.selectedTab}
              onChange={this.onTabChanges}
            >
              <Tab
                label="Send Alert"
                value={this.SEND_ALERT}
                className={this.state.selectedTab !== this.SEND_ALERT ? 'unselected-subtab' : ''}
              />
              <Tab
                label="Receive Alert"
                value={this.RECEIVE_ALERT}
                className={this.state.selectedTab !== this.RECEIVE_ALERT ? 'unselected-subtab' : ''}
              />
            </Tabs>

            <div className='subTab-container'>
            {
              this.state.selectedTab === 'send' && <SendAlerts companyId={this.props.companyId} />
            }
            {
              this.state.selectedTab === 'receive' && <ReceiveAlerts companyId={this.props.companyId} />
            }
            </div>
          </div>
        )}

        {!this.canDisplaySendAlertTab &&
          <ReceiveAlerts
            companyId={this.props.companyId}
            tableHeader="Receive Alerts"
          />
        }
      </Paper>
    )
  }

}

const mapStateToProps = (state) => {
  return {
    selectedCompany: state.companies.companies.selectedCompany,
    headerText: state.main.headerText,
    breadcrumbs: state.main.breadcrumbs,
  }
}

const mapDispatchToProps = dispatch => {
  return {
    getCompany: (companyId) => dispatch(getCompanyDetails(companyId)),
    setHeaderText: (text) => dispatch(setHeaderText(text)),
    setBreadcrumbs: (breadcrumbs) => dispatch(setBreadcrumbs(breadcrumbs)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(Alerts);
