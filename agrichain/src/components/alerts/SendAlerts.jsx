import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import AlertForm from './AlertForm';
import APIService from '../../services/APIService';
import GenericTable from '../GenericTable';
import {get} from 'lodash';
import { getCompanyDetails } from '../../actions/companies';
import AddButton from '../common/AddButton';

class SendAlerts extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      openForm: false,
      selected: undefined,
      data: [],
    }
  }

  componentDidMount() {
    this.getAlerts();
  }

  getAlerts() {
    APIService.alerts()
      .get(null, null, {company_id: this.props.companyId})
      .then(response => {
        this.setState({data: response});
      });
  }

  onAdd = () => this.setState({openForm: true});

  onCloseForm = () => this.setState({openForm: false, selected: null});

  onDefaultClick = item => {
    APIService.alerts(item.id)
      .get()
      .then(res => this.setState({selected: res, openForm: true}));
  }

  updateAlert(item) {
    if (item) {
      let isActive = get(item, 'isActive')
      const newState = {...this.state};
      let objIndex = newState.data.findIndex(obj => obj.id === item.id);
      newState.data[objIndex].isActive = !newState.data[objIndex].isActive;
      this.setState(newState);
      APIService.alerts(item.id)
        .put({'isActive': !isActive})
        .then(() => window.location.reload());
    }
  }

  render() {
    const COLUMNS = [
      {key: 'alertDisplayName', header: 'Alert', className: 'medium'},
      {key: 'description', header: 'Description', className: 'large'},
      {key: 'alertType', header: 'Contact Type', className: 'small'},
      {key: 'recipients', header: 'Recipients', className: 'large'},
      {key: 'frequencyDisplayName', header: 'Trigger', className: 'small'},
      {key: 'isActive', header: 'Is Active', className: 'small', showToggle: true, func: item => this.updateAlert(item)},
    ];

    return (
      <Fragment>
        <AddButton
          label='Alert'
          app='alerts'
          tooltipTitle='Add a new alert'
          onClick={this.onAdd}
        />

        <GenericTable
          columns={COLUMNS}
          items={this.state.data}
          handleDefaultCellClick={this.onDefaultClick}
          showActiveToggle={true}
          updateAlert={(item) => this.updateAlert(item)}
          displayIDColumn='alertDisplayName'
        />

        {this.state.openForm &&
          <AlertForm alert={this.state.selected} companyId={this.props.companyId} open={true} onClose={this.onCloseForm}/>
        }
      </Fragment>
    )
  }

}

const mapStateToProps = (state) => {
  return {
    selectedCompany: state.companies.companies.selectedCompany,
    headerText: state.main.headerText,
    breadcrumbs: state.main.breadcrumbs,
  }
}

const mapDispatchToProps = dispatch => {
  return {
    getCompany: (companyId) => dispatch(getCompanyDetails(companyId))
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(SendAlerts);
