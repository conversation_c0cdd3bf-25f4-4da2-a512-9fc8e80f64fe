import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import APIService from '../../services/APIService';
import GenericTable from '../GenericTable';
import {get} from 'lodash';
import ReceiveAlertForm from './ReceiveAlertForm';
import { currentUser, isObserver, isStaff, isSuperuser } from '../../common/utils';
import { isLoading } from '../../actions/main';

class ReceiveAlerts extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      openForm: false,
      selected: undefined,
      data: []
    }
    this.getAlerts = this.getAlerts.bind(this)
  }

  componentDidMount() {
    this.getAlerts();
  }

  getAlerts() {
    this.props.isLoading()
    APIService.profiles()
    .appendToUrl(`${currentUser().id}/alerts/`)
    .get(null, null)
    .then(response => {
        this.setState({data: response});
    });
  }

  onCloseForm = () => this.setState({openForm: false, selected: null});

  onDefaultClick = item => {
    APIService.profiles()
      .appendToUrl(`${item.employeeId}/alerts/${item.id}/`)
      .get()
      .then(res => this.setState({selected: res, openForm: true}));
  }

  updateAlert(item) {
    if (item) {
      let isActive = get(item, 'isActive')
      const newState = {...this.state};
      let objIndex = newState.data.findIndex(obj => obj.id === item.id);
      newState.data[objIndex].isActive = !newState.data[objIndex].isActive;
      this.setState(newState);
      APIService.profiles()
        .appendToUrl(`${item.employeeId}/alerts/${item.id}/`)
        .put({'isActive': !isActive})
        .then(() => this.getAlerts());
    }
  }

  render() {
    const COLUMNS = [
      {key: 'alertDisplayName', header: 'Alert', className: 'medium'},
      {key: 'description', header: 'Description', className: 'large'},
      {key: 'alertType', header: 'Contact Type', className: 'small'},
      {key: 'roles', header: 'Roles Opted For', className: 'small', formatter: (item) => {
        if (item.roles && item.roles.length > 0) {
          return item.roles.map(role => role).join(', ');
        }
        return '-';
      }
      },
      {key: 'isActive', header: 'Is Active', className: 'small', showToggle: true, func: item => this.updateAlert(item)},
    ];
    if(isStaff() || isSuperuser() || isObserver()){
      COLUMNS.splice(2, 0, {key: 'username', header: 'Username', className: 'medium'})
      COLUMNS.splice(3, 0, {key: 'userRole', header: 'Role', className: 'medium'})
    }
    else
      COLUMNS.splice(2, 0, {key: 'internalSites', header: 'Sites', className: 'large'})

    return (
      <Fragment>
        <GenericTable
          columns={COLUMNS}
          items={this.state.data}
          handleDefaultCellClick={this.onDefaultClick}
          showActiveToggle={true}
          updateAlert={(item) => this.updateAlert(item)}
          displayIDColumn='alertDisplayName'
          tableHeader={this.props.tableHeader}
        />

        {this.state.openForm &&
          <ReceiveAlertForm
            alert={this.state.selected}
            companyId={this.props.companyId}
            open={true}
            onClose={this.onCloseForm}
            getAlerts={this.getAlerts}
          />
        }
      </Fragment>
    )
  }

}

const mapStateToProps = (state) => {
  return {
    selectedCompany: state.companies.companies.selectedCompany,
    headerText: state.main.headerText,
    breadcrumbs: state.main.breadcrumbs,
  }
}

const mapDispatchToProps = dispatch => {
  return {
    isLoading: () => dispatch(isLoading())
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(ReceiveAlerts);
