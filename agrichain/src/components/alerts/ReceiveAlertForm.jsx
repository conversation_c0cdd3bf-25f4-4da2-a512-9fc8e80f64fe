
import React from 'react';
import alertifyjs from 'alertifyjs';
import { required } from '../../common/validators';
import CommonSelect from '../common/select/CommonSelect';
import SideDrawer from '../common/SideDrawer';
import { get, set, includes, forEach, some, filter, omit } from 'lodash';
import CommonButton from '../common/CommonButton';
import APIService from '../../services/APIService';
import { Autocomplete, TextField, FormControlLabel, Switch } from '@mui/material';
import {
    RECEIVE_ALERT_NAMES, EMAIL_ALERTS, SMS_ALERTS, MOBILE_PUSH_NOTIFICATION_ALERTS, receiveAlertDescriptions, alertConfig,
    AlertRoles
} from './constants';

class ReceiveAlertForm extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            open: false,
            description: undefined,
            includeInternalSites: false,
            includeExternalSites: false,
            alertNames: RECEIVE_ALERT_NAMES,
            sites: [],
            selectedSites: [],
            selectedRoles: [],
            fields: {
                name: {
                    value: undefined,
                    errors: [],
                    validators: [required()]
                },
                channel: {
                    value: [],
                    validators: [required()],
                    errors: [],
                },
            },
            siteSelectionError: ''
        }
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleSiteChange = this.handleSiteChange.bind(this);
        this.handleRoleChange = this.handleRoleChange.bind(this);
        this.getAlertRolesByType = this.getAlertRolesByType.bind(this);
    }

    getAlertRolesByType = (alertType) => {
        return alertConfig[alertType]?.map(key => AlertRoles[key]) || [];
    };

    async componentDidMount() {
        const { open, alert } = this.props;
        const newState = { ...this.state };
        const response = await APIService.companies(this.props.companyId).appendToUrl('company_sites/minimal/').get()
        const sites = filter(response, site => site?.isActive)
        newState.sites = sites;

        if (alert) {
            let alertName = alert?.name
            newState.fields.name.value = alertName;
            newState.fields.channel.value = alert?.channel;
            newState.description = receiveAlertDescriptions[alertName];
            newState.includeInternalSites = alert?. includeInternalSites || false
            newState.includeExternalSites = alert?.includeExternalSites || false
            newState.selectedSites = get(alert, 'internalSites', [])
            if(newState.includeInternalSites && newState.selectedSites.length == 0)
                newState.selectedSites = newState.sites.map(site => site.id)
            const AVAILABLE_ROLES = this.getAlertRolesByType(alertName);
            newState.selectedRoles = get(alert, 'roles', []).map(roleId =>
                AVAILABLE_ROLES.find(role => role.id === roleId)
            ).filter(Boolean);
        }
        newState.open = open;
        this.setState(newState);
    }

    onClose = () => this.setState({ open: false }, () => this.props.onClose());

    setFieldValue(path, value, validateAfterSet = true, callback) {
        this.setState(
            state => set(state, `${path}.value`, value),
            () => {
                if (validateAfterSet) this.setFieldErrors(path);
                if (callback) callback(this.state);
            }
        );
    }

    setFieldErrors(path) {
        this.setState(state => set(state, `${path}.errors`, this.getFieldErrors(path)));
    }

    getFieldErrors(path) {
        const errors = [];
        const value = get(this.state, `${path}.value`);
        const validators = get(this.state, `${path}.validators`, []);

        validators.forEach((validator) => {
            if (validator.isInvalid(value)) {
                errors.push(validator.message);
            }
        });

        return errors;
    }

    setAllFieldsErrors(fieldsToOmit) {
        forEach(omit(this.state.fields, fieldsToOmit), (value, key) => {
            this.setFieldErrors(`fields.${key}`);
        });
    }

    handleSiteChange = newValue => this.setState({selectedSites: newValue})

    handleSelectChange = (value, id) => this.setFieldValue(`fields.${id}`, value, true);

    getChannel() {
        let alertName = this.state.fields.name.value;
        if (includes(MOBILE_PUSH_NOTIFICATION_ALERTS, alertName))
            return [{ name: 'Mobile Push Notification', id: 'mobile_push_notification' }];
        if (includes(EMAIL_ALERTS, alertName))
            return [{ name: 'Email', id: 'email' }];
        if (includes(SMS_ALERTS, alertName))
            return [{ name: 'SMS', id: 'sms' }];
    }

    handleRoleChange = (_event, newValue) => {
        this.setState({ selectedRoles: newValue });
    };

    handleSubmit = event => {
        event.preventDefault();
        let hasError = false;

        this.setState({
            siteSelectionError: ''
        });

        if (!this.state.includeInternalSites && !this.state.includeExternalSites) {
            alertifyjs.error('At least one site type (Own Company or External) must be switched on')
            return
        }

        if (this.state.includeInternalSites && (this.state.selectedSites && this.state.selectedSites.length === 0)) {
            this.setState({
                siteSelectionError: 'Please select at least one site'
            });
            hasError = true;
        }

        const formData = {
            name: this.state.fields.name.value,
            channel: this.state.fields.channel.value,
            includeInternalSites: this.state.includeInternalSites,
            includeExternalSites: this.state.includeExternalSites,
            sites: this.state.includeInternalSites ? this.state.selectedSites.map(site => site.id): [],
            roles: this.state.selectedRoles.map(role => role.id)
        };

        this.setAllFieldsErrors([]);

        if (!this.getIsFormInvalid() && !hasError) {
            this.submit(formData)
        }
    };


    getIsFormInvalid() {
        return some(this.state.fields, field => this.getIsFieldInvalid(field))
    }

    getIsFieldInvalid(field) {
        return some(field.validators, (validator) => {
            return validator.isInvalid(field.value);
        });
    }

    submit(data) {
        const { alert } = this.props;

        const handleResponse = () => {
            this.onClose();
            this.props.getAlerts()
        };

        if (alert)
            APIService.profiles().appendToUrl(`${alert.employeeId}/alerts/${alert.id}/`).put(data).then(handleResponse);
    }

    render() {
        const { fields } = this.state;
        const ERROR_STYLE = { textAlign: 'left' };

        return (
            <SideDrawer isOpen={this.state.open} title='Alert Details' onClose={this.onClose} size='medium'>
                <form noValidate>
                    <div className="cardForm cardForm--drawer">
                        <div className="cardForm-content row">
                            <div className="col-sm-12 form-wrap" style={{ marginTop: '15px', marginBottom: "-25px" }}>
                                <CommonSelect
                                    id="name"
                                    floatingLabelText="Name"
                                    value={fields.name.value}
                                    selectedItemId={fields.name.value}
                                    errorText={get(fields.name, 'errors[0]', '')}
                                    selectConfig={{ text: 'name', value: 'id' }}
                                    onChange={this.handleNameChange}
                                    items={this.state.alertNames}
                                    errorStyle={ERROR_STYLE}
                                    varient={null}
                                    disabled={this.props.alert || (window.location.hash.includes('stocks') && !this.props.alert)}
                                />
                            </div>

                            {this.state.description &&
                                <div className="col-sm-12" style={{ fontSize: '13px', fontWeight: 350 }}>
                                    <span>Description: {this.state.description}</span>
                                </div>
                            }

                            <div className="col-sm-12 form-wrap" style={{ marginTop: '20px' }}>
                                <CommonSelect
                                    id="channel"
                                    items={this.getChannel()}
                                    value={fields.channel.value}
                                    selectedItemId={fields.channel.value}
                                    onChange={this.handleSelectChange}
                                    floatingLabelText='Alert Type'
                                    errorText={get(fields.channel, 'errors[0]', '')}
                                    variant='standard'
                                    selectConfig={{ text: 'name', value: 'id' }}
                                />
                            </div>

                            <div className="col-sm-12 form-wrap" style={{ marginTop: '10px'}}>
                                <Autocomplete
                                    multiple
                                    id="role-selector"
                                    options={this.getAlertRolesByType(get(this.props.alert, 'name'))}
                                    getOptionLabel={(option) => option.name}
                                    renderInput={(params) => (
                                        <TextField
                                            {...params}
                                            label="Select Roles to receive alerts for"
                                            variant="outlined"
                                        />
                                    )}
                                    value={this.state.selectedRoles}
                                    onChange={this.handleRoleChange}
                                    isOptionEqualToValue={(option, value) => option.id === value.id}
                                />
                            </div>

                            <div className="col-sm-12 form-wrap" style={{ marginTop: '15px' }}>
                                <FormControlLabel
                                    control={(
                                        <Switch
                                            color='primary'
                                            checked={this.state.includeInternalSites}
                                            onChange={event => this.setState({ includeInternalSites: event.target.checked })}
                                        />)
                                    } label="Include Own Company Sites"
                                />
                                {this.state.includeInternalSites && (
                                    <Autocomplete
                                        multiple
                                        id="site-selector"
                                        options={this.state.sites}
                                        getOptionLabel={(option) => option.name}
                                        renderInput={(params) => (
                                            <TextField
                                                {...params}
                                                label="Site"
                                                variant="outlined"
                                                error={!!this.state.siteSelectionError}
                                                helperText={this.state.siteSelectionError}
                                            />
                                        )}
                                        style={{marginTop: '10px'}}
                                        value={this.state.selectedSites || null}
                                        onChange={(event, newValue) => this.handleSiteChange(newValue)}
                                        isOptionEqualToValue={(option, value) => option.id === value.id}
                                    />
                                )}
                            </div>
                            <div className="col-sm-12 form-wrap" style={{ marginTop: '15px' }}>
                                <FormControlLabel
                                    control={(
                                        <Switch color='primary'
                                            checked={this.state.includeExternalSites}
                                            onChange={event => this.setState({ includeExternalSites: event.target.checked })}
                                        />
                                    )} label="Include External Sites"
                                />
                            </div>
                            <div style={{ marginTop: '20px', marginLeft: 'auto', float: 'right' }}>
                                <CommonButton
                                    variant="contained"
                                    onClick={this.onClose}
                                    label='Cancel'
                                />
                                <CommonButton
                                    variant="contained"
                                    type='submit'
                                    primary
                                    onClick={this.handleSubmit}
                                    label='Submit'
                                    disabled={
                                        get(this.state.fields.name.errors, '0')
                                    }
                                />
                            </div>
                        </div>
                    </div>
                </form>
            </SideDrawer>
        )
    }
}

export default ReceiveAlertForm;
