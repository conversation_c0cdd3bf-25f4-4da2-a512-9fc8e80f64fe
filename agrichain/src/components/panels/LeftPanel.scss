@import "../../common/variables";

.left-bar {
  width: 75px;
  position: fixed;
  left: 0;
  min-height: 100vh;
  background: rgb(0, 25, 43);
  padding: 0;
  z-index: 2;
  padding-left: 0px;
  top: 88px;
  bottom: 0;
  overflow: auto;
  padding-bottom: 130px;
  .left-bar-control {
      padding: 0;
    a {
      font-size: 12px;
      padding: 11px 10px 11px 20px;
      font-weight: 300;
      color: #ffffff;
      justify-content: flex-start;
      text-transform: capitalize;
      border-left: 3px solid $colorNavyBlue;
      text-decoration: none;

      svg {
        margin-right: 15px;
      }
      @media screen and (min-width: 1025px) {
        font-size: 14px;
      }

      &.selected {
        border-left: 3px solid $colorGreen;
        border-radius: 0;
        color: $colorGreen;
      }
    }

    & .nav-text {
        font-size: 0;
      transition: all 0s ease;
    }
  }
}

.btn-pinned {
  width: 40px;
  height: 40px;
  border: none;
  background: #00192b url("/images/pinned.png") no-repeat 5px;
  background-size: 28px;
  position: fixed;
  bottom: 5px;
  left: 15px;
  -webkit-transition: all 0.8s ease;
  -moz-transition: all 0.8s ease;
  -ms-transition: all 0.8s ease;
  transition: all 0.8s ease;
}

@media only screen and (max-width: 1360px) {
  .left-bar-control {
    a div svg {
      margin-left: 33% !important;
    }
    a div span {
      display: none;
    }
  }
}
