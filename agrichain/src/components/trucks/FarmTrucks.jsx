import React from 'react';

import { connect } from 'react-redux';
import TrucksTable from '../../containers/TrucksTable';
import TrailersTable from '../../containers/TrailersTable';
import { getFarmTrucks, getCompanyTrailers } from '../../actions/api/trucks';
import {
  receiveTrailers,
  receiveTrucks,
  clickAddTruckButton,
  cancelEditTruck,
  clickAddTrailerButton,
  cancelEditTrailer,
} from '../../actions/companies/trucks';
import Paper from '@mui/material/Paper';
import {setBreadcrumbs} from '../../actions/main';
import isEqual from 'lodash/isEqual';
import {getCompanyIdFromCurrentRoute, isSystemCompany} from '../../common/utils';
import { getCompanyDetails } from '../../actions/companies';
import AddButton from '../common/AddButton';
import SideDrawer from '../common/SideDrawer';
import CreateTruck from '../../containers/CreateTruck';
import UpdateTruck from '../../containers/UpdateTruck';
import CreateTrailer from '../../containers/CreateTrailer';
import UpdateTrailer from '../../containers/UpdateTrailer';
import get from 'lodash/get';

class Trucks extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      color: '#7b1fa2',
      openTruckSideDrawer: false,
      trailersFetched: false,
    };

    this.openTruckSideDraw = this.openTruckSideDraw.bind(this);
    this.closeTruckSideDraw = this.closeTruckSideDraw.bind(this);
    this.openTrailerSideDraw = this.openTrailerSideDraw.bind(this);
    this.closeTrailerSideDraw = this.closeTrailerSideDraw.bind(this);
    this.onColorChange = this.onColorChange.bind(this);
  }

  onColorChange(color) {
    if(color !== this.state.color)
      this.setState({color: color});
  }

  openTruckSideDraw(){
    this.props.handleAddTruckButtonClick();
    this.setState({ openTruckSideDrawer: true, });
  }

  closeTruckSideDraw() {
    this.setState({ openTruckSideDrawer: false, });
  }

  openTrailerSideDraw(){
    this.props.handleAddTrailerButtonClick();
    this.setState({ openTrailerSideDrawer: true, });
  }

  closeTrailerSideDraw() {
    this.setState({ openTrailerSideDrawer: false, });
  }

  getCompanyId() {
    return this.props.companyId || get(this.props, 'selectedFarm.companyId');
  }

  componentDidMount() {
    this.getCompanyIfNeeded();
    this.setBreadcrumbs();
    this.props.getTrucks(this.props.farmId);
    if(this.getCompanyId()) {
      this.setState({trailersFetched: true}, () => {
        this.props.getTrailers(this.getCompanyId());
      });
    }
  }

  componentDidUpdate() {
    this.setBreadcrumbs();
    if(!this.state.trailersFetched) {
      this.setState({trailersFetched: true}, () => {
        this.props.getTrailers(this.getCompanyId());
      });
    }
  }

  getCompanyIfNeeded() {
    if(getCompanyIdFromCurrentRoute() && (!this.props.selectedCompany || this.props.selectedCompany.id !== parseInt(getCompanyIdFromCurrentRoute()))) {
      const companyId = getCompanyIdFromCurrentRoute();
      companyId && this.props.getCompany(companyId);
    }
  }

  setBreadcrumbs() {
    if(this.props.farmId && this.props.farmName){
      const companyId = getCompanyIdFromCurrentRoute();
      const farmName = this.props.farmName;
      const farmId = this.props.farmId;
      let breadcrumbs = [];
      if(companyId && this.props.selectedCompany) {
        const parentRoute = '/companies/' + this.props.selectedCompany.id;
        breadcrumbs = [
          {text: 'Companies', route: '/companies'},
          {text: this.props.selectedCompany.name, route:  parentRoute + '/details'},
          {text: 'Farms', route: parentRoute + '/farms'},
          {text: 'Trucks'}
        ];
        const stocksOption = { text: farmName, route: '/stocks/storages-view?farmId=' + farmId };
        const detailsOption = { text: farmName, route: parentRoute + '/farms/' + farmId + '/settings/details?details' };
        if(this.props.canActOnFarm && this.props.selectedFarm.isAssociated) {
          breadcrumbs.splice(breadcrumbs.length-1, 0, stocksOption);
        } else {
          breadcrumbs.splice(breadcrumbs.length-1, 0, detailsOption);
        }
      } else {
        if(this.props.canActOnFarm && (this.props.selectedFarm.isAssociated || isSystemCompany())) {
          breadcrumbs = [
            {text: 'Farms', route: '/farms'},
            {text: farmName, route: '/stocks/storages-view?farmId=' + farmId},
            {text: 'Settings', route: '/farms/' + farmId + '/settings/details?details'},
            {text: 'Trucks'}
            ];
        } else {
          breadcrumbs = [
            {text: 'Farms', route: '/farms'},
          ];

          breadcrumbs = breadcrumbs.concat([
            {text: 'Settings', route: '/farms/' + farmId + '/settings/details?details'},
            {text: 'Trucks'}
          ]);
        }
      }
      if(!isEqual(this.props.breadcrumbs, breadcrumbs)) {
        this.props.setBreadcrumbs(breadcrumbs);
      }
    }
  }

  render() {
    return (
      <div>
        <Paper className="paper-table">
          <div>
            {this.props.canActOnFarm && <AddButton label="Truck" onClick={this.openTruckSideDraw} app="truck" />}
            <TrucksTable isCompanyTrucks={false}/>
            {
              this.props.isCreateFormDisplayed &&
              <SideDrawer
                isOpen={ this.state.openTruckSideDrawer }
                title="Add Trucks"
                size="big"
                onClose={this.closeTruckSideDraw}
                app="truck"
                paperStyle={{borderLeft: '8px solid ' + this.state.color || '#fff'}}
                >
                <CreateTruck onColorChange={this.onColorChange} closeDrawer={ this.closeTruckSideDraw } canAccessAny={true} companyId={this.props.selectedFarm.companyId} farmId={this.props.selectedFarm.id} company={this.props.selectedCompany}/>
              </SideDrawer>
            }
            {
              this.props.isUpdateFormDisplayed &&
              <SideDrawer
                isOpen={ this.props.isUpdateFormDisplayed }
                title="Edit Trucks"
                size="big"
                onClose={this.props.cancelEditTruck}
                app="truck"
                paperStyle={{borderLeft: '8px solid ' + this.state.color || '#fff'}}
                >
                <UpdateTruck selectedCompany={this.props.selectedCompany} onColorChange={this.onColorChange} canAccessAny={true} companyId={this.props.selectedFarm.companyId} farmId={this.props.selectedFarm.id} />
              </SideDrawer>
            }
          </div>
        </Paper>
        <Paper className="paper-table" style={{marginTop: '15px', marginBottom: '45px'}}>
          <div>
            {
              this.props.canActOnFarm && <AddButton label="Trailer" onClick={this.openTrailerSideDraw} app="truck" />
            }
            <TrailersTable />
            {this.props.isTrailerCreateFormDisplayed &&
             <SideDrawer
               isOpen={ this.state.openTrailerSideDrawer }
               title="Add Trailer"
               onClose={this.closeTrailerSideDraw}
               app="truck"
               >
               <CreateTrailer onColorChange={this.onColorChange} closeDrawer={ this.closeTrailerSideDraw } canAccessAny={true} companyId={this.getCompanyId()} />
             </SideDrawer>
            }
            {
              this.props.isUpdateFormDisplayed ?
              <SideDrawer
                isOpen={ this.props.isTrailerUpdateFormDisplayed }
                title="Edit Trailer"
                onClose={this.props.cancelEditTrailer}
                app="truck"
                >
                <UpdateTrailer canAccessAny={this.props.isUpdateFormDisplayed} companyId={this.getCompanyId()} />
              </SideDrawer>
              : null
            }
          </div>
        </Paper>
      </div>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    isUpdateFormDisplayed: state.companies.companies.company.trucks.isUpdateFormDisplayed,
    isCreateFormDisplayed: state.companies.companies.company.trucks.isCreateFormDisplayed,
    isTrailerCreateFormDisplayed: state.companies.companies.company.trucks.isTrailerCreateFormDisplayed,
    isTrailerUpdateFormDisplayed: state.companies.companies.company.trucks.isTrailerUpdateFormDisplayed,
    breadcrumbs: state.main.breadcrumbs,
    selectedCompany: state.companies.companies.selectedCompany,
    selectedFarm: state.companies.farms.selectedFarm,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    getTrucks: (companyId) => dispatch(getTrucks(companyId)),
    getTrailers: (companyId) => dispatch(getCompanyTrailers(companyId, receiveTrailers)),
    setBreadcrumbs: (breadcrumbs) => dispatch(setBreadcrumbs(breadcrumbs)),
    getCompany: (companyId) => dispatch(getCompanyDetails(companyId)),
    handleAddTruckButtonClick: () => dispatch(clickAddTruckButton()),
    handleAddTrailerButtonClick: () => dispatch(clickAddTrailerButton()),
    cancelEditTruck: () => dispatch(cancelEditTruck()),
    cancelEditTrailer: () => dispatch(cancelEditTrailer()),
  };
};

function getTrucks(farmId) {
  return (dispatch) => {
    dispatch(getFarmTrucks(farmId, receiveTrucks));
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Trucks);

