import React from 'react'
import ListItem from '@mui/material/ListItem'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import SubscriberIcon from '@mui/icons-material/VerifiedUser';
import RegisteredIcon from '@mui/icons-material/PrivacyTip';
import Tooltip from '@mui/material/Tooltip'
import startCase from 'lodash/startCase'
import CompanyIcon from '../common/icons/Company'
import { COMPANY_TYPES, BLACK } from '../../common/constants'


const CompanyListItem = ({company, ...rest}) => {
  let type = company?.typeId ? COMPANY_TYPES[company.typeId] : ''
  type = type ? (type === 'bhc' ? 'BHC' : startCase(type)) : type

  return (
    <ListItem {...rest}>
      <ListItemIcon sx={{minWidth: 'auto', marginRight: '16px'}}>
        {
          company?.transactionParticipation ?
            <Tooltip title={`Subscriber ${type}`}>
          <SubscriberIcon color='success' />
          </Tooltip>:
          (
            company.isRegistered ?
              <Tooltip title={`Registered ${type}`}>
            <RegisteredIcon color='warning' />
            </Tooltip>:
            <CompanyIcon fill={BLACK} title={`${type} Company`} />
          )
        }
    </ListItemIcon>
      <ListItemText
        primary={company.name || company.businessName || company.displayName}
        secondary={company?.entityName || ''}
      />
    <span style={{fontSize: '12px', color: 'rgba(0, 0, 0, 0.6)'}}>{type}</span>
    </ListItem>
  )
}

export default CompanyListItem
