import React, { Component } from 'react';
import { connect } from 'react-redux';
import { setHeaderText, setBreadcrumbs, forceStopLoader, isLoading } from '../../actions/main';
import APIService from '../../services/APIService';
import { isCompanyAdminOrObserver, isCurrentUserBelongsToCompany, isSuperuser } from '../../common/utils';
import { GROWER } from '../../common/constants';
import CommonTabs from '../common/CommonTabs';
import XeroMappingsContainer from './XeroMappingsContainer';
import ExternalPortals from './ExternalPortals';
import ImpexDocs from './ImpexDocs';

class Integrations extends Component {
  constructor(props) {
    super(props)
    this.state = {
      company: props.company,
      activeTab: this.props.location.pathname,
    }
    this.handleTabChange = this.handleTabChange.bind(this)
  }

  getCompanyId = props => (props || this.props).match.params.company_id

  componentDidMount() {
    this.fetchCompany()
    this.props.dispatch(setHeaderText('Integrations'))
    this.props.dispatch(forceStopLoader())
  }

  componentDidUpdate(prevProps) {
    if(this.getCompanyId(prevProps) !== this.getCompanyId())
      this.fetchCompany()
    if (prevProps.location.pathname !== this.props.location.pathname)
      this.handleTabChange(this.props.location.pathname)
  }

  fetchCompany = () => {
    const companyId = this.getCompanyId()
    if(companyId && companyId !== this.state.company?.id) {
      const { dispatch } = this.props
      dispatch(isLoading('IntegrationsCompany'))
      APIService
        .companies(companyId)
        .get()
        .then(response => this.setState(
          {company: response},
          () => {
            const breadcrumbs = [
              {text: 'Companies', route: '/companies'},
              {text: this.state.company.name, route: `/companies/${companyId}/details`},
              {text: 'Integrations'}
            ]
            dispatch(setBreadcrumbs(breadcrumbs))
            dispatch(forceStopLoader())
          }
        ))
    }
  }

  handleTabChange = value => this.setState(state => ({ ...state, activeTab: value }));

  render() {
    const { company } = this.state
    const PARENT_URL = this.props.match.url;
    let _PARENT_URL = PARENT_URL
    if(!_PARENT_URL.endsWith('/'))
      _PARENT_URL += '/'
    const IMPEX_DOCS_URL = _PARENT_URL + 'impexdocs'
    const BULK_HANDLERS_URL = _PARENT_URL + 'bulk-handlers'
    const tabs = [
      {label: 'Xero', url: PARENT_URL, component: () => <XeroMappingsContainer company={company} />},
      {label: 'Impex Docs', url: IMPEX_DOCS_URL, component: () => <ImpexDocs company={company} />},
    ]
    const BHCAccountTab = {label: 'Bulk Handler Accounts', url: BULK_HANDLERS_URL, component: () => <ExternalPortals company={company} />}
    const canViewBHCAccountTab = company?.typeId === GROWER && ((isCompanyAdminOrObserver() && isCurrentUserBelongsToCompany(company.id)) || isSuperuser())
    if(canViewBHCAccountTab)
      tabs.push(BHCAccountTab)


    const isHandlerTab = this.props.location.pathname.includes('/bulk-handlers')
    return (
      <div>
        <div className='tab'>
          <div className='tab-header-simple'>
            <CommonTabs
              variant='standard'
              value={this.state.activeTab}
              tabs={tabs}
              tabStyle={{minWidth: '200px'}}
            />
            <div className='tab-content col-xs-12'>
              {
                this.state.activeTab == PARENT_URL && company?.id && !isHandlerTab &&
                  <XeroMappingsContainer company={company} />
              }
            </div>
            <div className='tab-content col-xs-12'>
              {
                this.state.activeTab == IMPEX_DOCS_URL && company?.id && !isHandlerTab &&
                  <ImpexDocs company={company} />
              }
            </div>
            <div className='tab-content col-xs-12'>
              {
                this.state.activeTab == BULK_HANDLERS_URL && company?.id && isHandlerTab && canViewBHCAccountTab &&
                  <ExternalPortals company={company} />
              }
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export default connect()(Integrations);
