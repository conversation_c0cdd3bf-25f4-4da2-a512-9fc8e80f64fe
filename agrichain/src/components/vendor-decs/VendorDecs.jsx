import React from 'react';
import { connect } from 'react-redux';
import { Paper } from '@mui/material';
import { get, cloneDeep, isEqual, isMatch, includes, capitalize, compact, map, orderBy, uniq, without } from 'lodash';
import { setHeaderText, setBreadcrumbs, setSubHeaderText } from '../../actions/main';
import VendorDecsTable from '../../containers/VendorDecsTable';
import { getVendorDecs, storeVendorDecMovementEntity } from '../../actions/companies/vendor-decs';
import CommonListingButton from '../common/CommonListingButton';
import { isCustomerGradeOrTonnageMissing } from '../freights/utils';
import { defaultViewAction, vendorDecCountryConfig, isCurrentUserGrower, isGlobalEligibilityDecsPath, isSystemCompany } from '../../common/utils';
import ListingControls from '../common/ListingControls'
import AddIcon from '@mui/icons-material/AddCircle';
import DownIcon from '@mui/icons-material/ArrowDropDown';
import EligibilityDecsTable from '../../containers/EligibilityDecsTable';
import { ELIGIBILITY, VENDOR } from './constants';
import ExpandIcon from '@mui/icons-material/Expand';
import CollapseIcon from '@mui/icons-material/UnfoldLess';
import { Chip } from '@mui/material';

class VendorDecs extends React.Component {
  constructor(props) {
    super(props);
    this.moduleName = vendorDecCountryConfig()?.moduleName
    this.state = {
      items: [],
      paginationData: {},
      expanded: []
    };
  }

  componentDidMount() {
    const {queryString, movementId, dispatch} = this.props;
    dispatch(getVendorDecs(null, true, queryString, isEqual(this.moduleName, ELIGIBILITY)));
    dispatch(storeVendorDecMovementEntity(movementId));
    this.setHeader();
  }

  componentDidUpdate() {
    if(!isEqual(this.props.breadcrumbs, this.getBreadcrumbs()))
      this.setBreadcrumbs();
    if(!isEqual(this.props.headerText, this.getHeaderText()))
      this.setHeaderText();
    if(!isEqual(this.props.subheaderText, this.getSubHeaderText()))
      this.setSubHeaderText();
  }

  isLockedToOneParent() {
    return Boolean(this.props.queryString);
  }

  setHeader() {
    this.setHeaderText();
    this.setSubHeaderText();
    this.setBreadcrumbs();
  }

  setHeaderText() {
    const { dispatch } = this.props;
    dispatch(setHeaderText(this.getHeaderText()));
  }

  setSubHeaderText() {
    const { dispatch } = this.props;
    dispatch(setSubHeaderText(this.getSubHeaderText()));
  }

  setStandAloneBreadcrumbs() {
    const { dispatch, count } = this.props;
    const breadcrumbs = [{text: `${capitalize(this.moduleName)} Declarations (${count})`}];
    dispatch(setBreadcrumbs(breadcrumbs));
  }

  getBreadcrumbs() {
    const { count, baseBreadcrumbs } = this.props;
    const vendorDecBreadcrumb = {text: `${capitalize(this.moduleName)} Declarations (${count})`};
    if(this.isLockedToOneParent())
      return [...cloneDeep(baseBreadcrumbs), vendorDecBreadcrumb];
    return [vendorDecBreadcrumb];
  }

  getHeaderText() {
    const { baseHeaderText } = this.props;
    return baseHeaderText || `${capitalize(this.moduleName)} Declarations`;
  }

  getSubHeaderText() {
    const { baseSubHeaderText } = this.props;
    return baseSubHeaderText || '';
  }

  setBreadcrumbs() {
    this.props.dispatch(setBreadcrumbs(this.getBreadcrumbs()));
  }

  onAddClick = (type) => {
    if (isEqual(type, ELIGIBILITY)) {
      let URL = '/#/eligibility-decs/new/'
      if (this.props?.contract)
        URL += `?contractId=${this.props?.contract?.id}`;
      else if (this.props?.order)
        URL +=  `?orderId=${this.props?.order?.id}`;
      else if (this.props?.movement)
        URL += `?movementId${this.props?.movement?.id}`;

      window.location = URL;
      return
    }
    if (this.isLockedToOneParent() && this.props.onAddClick){
      if(this.props?.movement && includes(['grainCommodity', 'truckCleanliness'], type)){
        isCustomerGradeOrTonnageMissing(this.props.movement, (isMissing) => {
          if(!isMissing){
            this.props.onAddClick(type);
          }
        });
      }
      else{
        this.props.onAddClick(type);
      }
    }
    else {
      if (type == 'grainCommodity') window.location = '/#/vendor-decs/grain/new/';
      else window.location = '/#/vendor-decs/truck/new/';
    }
  };

  getActionsOptionMapperListItems() {
    return [defaultViewAction];
  }

  expandItems = (event, companyId) => {
    event.preventDefault()
    event.stopPropagation()
    if(companyId && this.state.expanded.includes(companyId))
      this.setState({expanded: without(this.state.expanded, companyId)})
    else if(companyId && !this.state.expanded.includes(companyId))
      this.setState({expanded: uniq([...this.state.expanded, companyId])})
  }

  onExpandAll = event => {
    event.stopPropagation()
    event.preventDefault()
    const name = isCurrentUserGrower() ? 'licenseeName' :  'declarantName'
    const partyId = isCurrentUserGrower() ? 'licenseeId' : 'declarantId'
    const companyIds = compact(map(orderBy(this.props.items, name), partyId))
    this.setState({expanded: [...companyIds]})
  }

  onCollapseAll = () => this.setState({expanded: []})

  render() {
    const { hideAddButton } = this.props;
    let showoptions = !(isMatch(get(this.props,'queryString'),'?order_id') || isMatch(get(this.props,'queryString'),'?contract_id'));
    const isExpandAll = Boolean(this.state.expanded?.length === this.props.items?.length && this.state.expanded?.length)
    const collapseButtonLabel =  this.props.items?.length > 1 ? "Collapse All": "Collapse"
    const expandButtonLabel = this.props.items?.length > 1 ? "Expand All": "Expand"
    return (
      <React.Fragment>

      <Paper className="paper-table-paginated">
        <div style={isGlobalEligibilityDecsPath() ? {display: 'inline-flex', justifyContent: 'space-between', alignItems: 'center', width: '100%', padding: '8px 12px'} : { position: 'relative' }}>
          {isGlobalEligibilityDecsPath() &&
          <div style={{display: 'inline-flex', alignItems: 'center'}}>
            <span style={{ fontWeight: 'bold', fontSize: '20px' }}>
              Declarations {!isSystemCompany() ? `- (Grouped by ${isCurrentUserGrower() ? 'Licensee' : 'Declarant'})`: null }
              {!isSystemCompany() &&
              <Chip
                sx={{marginLeft: '10px'}}
                icon={isExpandAll ? <CollapseIcon fontSize='inherit' /> : <ExpandIcon fontSize='inherit' />}
                label={isExpandAll ? collapseButtonLabel : expandButtonLabel}
                onClick={isExpandAll ? this.onCollapseAll : this.onExpandAll}
              />
              }
            </span>
          </div>
          }
          <div style={isGlobalEligibilityDecsPath() ? {display: 'inline-flex', alignItems: 'center'} : {float: 'right'}}>
            <ListingControls
              controls={[
                {
                  type: 'new',
                  order: 1,
                  hidden: hideAddButton,
                  component: hideAddButton ? null : (
                    <CommonListingButton
                      startIcon={<AddIcon fontSize='inherit' />}
                      endIcon={isEqual(this.moduleName, VENDOR) ? <DownIcon fontSize='inherit' /> : null}
                      defaultHandler={() => this.onAddClick(isEqual(this.moduleName, VENDOR) ? 'grainCommodity' : ELIGIBILITY)}
                      showMenus={isEqual(this.moduleName, VENDOR) && showoptions}
                      optionMapper={[
                        {
                          name: "Grain Commodity",
                          fx: () => this.onAddClick('grainCommodity'),
                          enableOption: true
                        },
                        vendorDecCountryConfig()?.allowTruckCleanliness ? {
                          name: "Truck Cleanliness",
                          fx: () => this.onAddClick('truckCleanliness'),
                          enableOption: true
                        } : null
                      ]}
                      title={`Create ${capitalize(this.moduleName)} Declaration`}
                      showDownLoadIcon={false}
                      name={`${capitalize(this.moduleName)} Declaration`}
                      style={{margin: '0 4px', textTransform: 'none'}}
                      hideVertIcon
                    />
                  )
                }
              ]}
            />
          </div>
        </div>
        {isEqual(this.moduleName, 'vendor') ? <VendorDecsTable /> : <EligibilityDecsTable expanded={this.state.expanded} />}
      </Paper>
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  return {
    count: get(state.companies.vendorDecs, 'items.length') || 0,
    breadcrumbs: state.main.breadcrumbs,
    headerText: state.main.headerText,
    subHeaderText: state.main.subHeaderText,
    items: state.companies.vendorDecs.items,
  };
};

export default connect(mapStateToProps)(VendorDecs);
