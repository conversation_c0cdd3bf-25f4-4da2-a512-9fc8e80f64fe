import React from 'react';
import alertifyjs from 'alertifyjs';
import { connect } from 'react-redux';
import { isEmpty, some, get, cloneDeep, filter, forEach, set, map, uniqBy } from 'lodash';
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { setHeaderText, setBreadcrumbs, isLoading, forceStopLoader, setSubHeaderText } from '../../../actions/main';
import APIService from '../../../services/APIService';
import CommonButton from '../../common/CommonButton';
import CustomEmailDialog from '../../common/CustomEmailDialog';
import { REQUIRED_FIELD } from '../../../common/constants';
import  CommonTextField from '../../common/CommonTextField';
import CommonAutoSelect from '../../common/autocomplete/CommonAutoSelect';
import SeasonSelect from '../../common/select/SeasonSelect';
import Preview from '../Preview';
import { generateIdentifier, vendorDecCountryConfig } from '../../../common/utils';
const EMAIL_PARTIES = ['declarant', 'licensee'];

class EligibilityDecForm extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      fields: {
        identifier: { ...cloneDeep(REQUIRED_FIELD), value: generateIdentifier('vd') },
        declarant: { ...cloneDeep(REQUIRED_FIELD) },
        licensee: { ...cloneDeep(REQUIRED_FIELD) },
        season: { ...cloneDeep(REQUIRED_FIELD) },
      },
      declarantParties: [],
      licenseeParties: [],
      declarantContacts: [],
      licenseeContacts: [],
      contacts: [],
      allCompanyParties: [],
      showEmailDialog: false,
      selectedEntity: null
    };
    this.formRef = React.createRef();
    this.focusOnFirstErrorField = this.focusOnFirstErrorField.bind(this);

    this.fieldsOrder = ["identifier", "declarant", " licensee", "season"];
    this.fieldRef = {};
    this.fieldsOrder.forEach(e => (this.fieldRef[e] = React.createRef()));
  }


  componentDidMount() {
    this.setHeader();
    this.props.dispatch(forceStopLoader());
    APIService
      .companies(this.props.user.companyId)
      .appendToUrl('companies/minimal/?include_parent_company=true')
      .get()
      .then(response => this.setState({allCompanyParties: response, declarantParties: response, licenseeParties: response }))
    this.fetchEntityDetails()
  }

  getEntityFromUrl() {
    const hash = window.location.hash;
    const queryStart = hash.indexOf('?');
    if (queryStart === -1) return null;
  
    const queryString = hash.substring(queryStart + 1);
    const params = new URLSearchParams(queryString);
  
    if (params.has('contractId'))
      return { entity: 'contract', id: params.get('contractId') };
    else if (params.has('orderId'))
      return { entity: 'freightorder', id: params.get('orderId') };
    else if (params.has('movementId'))
      return { entity: 'freightcontract', id: params.get('movementId') };

    return null;
  }

  fetchEntityDetails() {
    const entity = this.getEntityFromUrl();
    if (!isEmpty(entity)) {
      APIService.profiles().appendToUrl(`transactions/${entity.entity}/${entity.id}/`).get(this.unregToken).then(data => {
        this.setState({ selectedEntity: data }, 
            () => {
              this.fetchContractContacts();
              this.populateFieldsFromEntity(data);
            });
      });
    }
  }

  populateFieldsFromEntity(entity) {
    const newState = { ...this.state };
    newState.fields.declarant.value = get(entity, 'seller.companyId');
    newState.fields.licensee.value = get(entity, 'buyer.companyId');
    newState.fields.season.value = get(entity, 'season');
    this.setState(newState)
  }

  fetchContractContacts() {
    const { selectedEntity } = this.state;
    const contractId = get(selectedEntity, 'contractId');
    if(contractId) {
      APIService.contracts(contractId).appendToUrl('contacts/').get(this.unregToken).then(contacts => {
        this.setState({contacts: contacts, declarantContacts: contacts?.seller, licenseeContacts: contacts?.buyer}, () => {});
      });
    }
    else {
      const id = get(selectedEntity, 'id');
      const type = get(selectedEntity, 'entity');
      APIService.freights().appendToUrl(`${type}/${id}/contacts/`).get(this.unregToken).then(contacts => {
        this.setState({ contacts: contacts, declarantContacts: contacts?.seller, licenseeContacts: contacts?.buyer }, () => { });
      });
    }
  }

  openEmailDialog = () => {
    this.setState({ showEmailDialog: true });
  };

  closePreview = () => {
    this.setState({preview: false});
  };

  customizer = (objValue, srcValue) =>{
      return srcValue;
  };

  setHeader() {
    this.setHeaderText();
    this.setBreadcrumbs();
  }

  setHeaderText() {
    const { dispatch } = this.props;
    dispatch(setHeaderText('Create Eligibility Declaration'));
    dispatch(setSubHeaderText(''));
  }

  setBreadcrumbs() {
    const { dispatch } = this.props;
    const breadcrumbs = [
      {text: 'Eligibility Declarations', route: '/eligibility-decs'},
      {text: 'New'}
    ];
    dispatch(setBreadcrumbs(breadcrumbs));
  }

  getPayload(finalSubmit = true) {
    let payload = {}
    let entity = this.getEntityFromUrl()
    payload.identifier = this.state.fields.identifier.value;
    payload.declarantId = this.state.fields.declarant.value
    payload.licenseeId = this.state.fields.licensee.value;
    payload.season = this.state.fields.season.value;
    payload.countryId = this.props.user?.countryId
    if (finalSubmit && !isEmpty(entity)) {
      payload.entity = entity.entity;
      payload.entityId = entity.id;
    }
    return payload;
  }

  getPreview() {
    const { dispatch } = this.props;
    const data = this.getPayload(false);
    dispatch(isLoading('eligibilityDecPreview'));
    APIService.vendor_decs().appendToUrl('soft-view/').request(
      'POST', data, null, { responseType: 'blob' }
    ).then(response => {
      this.setState({
        preview: true,
        previewData: window.URL.createObjectURL(response.data)
      }, () => {
        dispatch(forceStopLoader());
      });
    });
  }

  closeEmailDialog = (communicationData, justClose) => {
    if(justClose) {
      this.gotOncePartyContacts = false;
      this.setState({showEmailDialog: false});
    }
    else if(this.state.showEmailDialog) {
      this.setState({showEmailDialog: false}, () => {
        this.create(communicationData);
      });
    }
  };

  create = communicationData => {
    const { dispatch } = this.props;
    const data = this.getPayload();
    data.communication = communicationData || {};
    dispatch(isLoading('creatingVendorDec'));
    APIService.vendor_decs().post(data).then(response => {
      dispatch(forceStopLoader());
      if (response.errors) {
        alertifyjs.error(get(response.errors, '_All_.0'), 3);
      }
      else {
        this.closePreview();
        window.location.hash = this.getAfterCreateRedirectURL();
      }
    });
  };

  getAfterCreateRedirectURL() {
    const URL = `/${vendorDecCountryConfig()?.moduleName}-decs`;
    const entity = get(this.state.selectedEntity, 'entity');
    const id = get(this.state.selectedEntity, 'id');

    if(entity && id) {
      if(entity === 'contract')
        return `/contracts/${id}${URL}`;
      if(entity === 'freightorder')
        return `/freights/orders/${id}${URL}`;
      if(entity === 'freightcontract')
        return `/freights/movements/${id}${URL}`;
    }
    return URL;
  }


  onSubmit = event => {
    event.preventDefault();
    this.setAllFieldsErrors()
    this.focusOnFirstErrorField();
    if(!this.hasErrors())
      this.checkIdentifierUniqueness();
  };

  checkIdentifierUniqueness() {
    const identifier = this.state.fields.identifier.value;
    if(identifier) {
      APIService.vendor_decs().appendToUrl(`${identifier}/is-unique/?is_eligibility=true`).get().then(response => {
        if(response.isUnique) {
          if(!this.hasErrors())
            this.getPreview();
        }
        else
          alertifyjs.error('Eligibility Dec with this identifier already exists.', 3);
      });
    }
  }

  handleCancel = () => window.location = '#/eligibility-decs'

  getSelectedEmailParties = () => ['declarant', 'licensee'];

  getEmailSubject() {
    const { user } = this.props;
    const identifier = this.state.fields.identifier.value;
    return `${get(user, 'company.name', '')} Eligibility Declaration #${identifier}`;
  }

  getPartyContacts() {
    const { declarantContacts, licenseeContacts } = this.state;
    return {
      declarant: declarantContacts,
      licensee: licenseeContacts,
    };
  }

  getPartyEmails() {
    return {
      declarant:  null,
      licensee:  null
    };
  }

  setFieldErrors(path) {
    this.setState(state => set(state.fields, `${path}.errors`, this.getFieldErrors(path)));
  }


  setAllFieldsErrors() {
    forEach(this.state.fields, (value, key) => {
        this.setFieldErrors(`${key}`);
    });
  }

  hasErrors = () => some(map(this.state.fields, value => !isEmpty(value.errors)))

  focusOnFirstErrorField() {
    for (let i = 0; i < this.fieldsOrder.length; i++) {
      const formField = this.fieldRef[this.fieldsOrder[i]];
      const field = get(this.state.fields, this.fieldsOrder[i]);
      const currentFields = [ "identifier", "declarant", "licensee", "season"];
      if(currentFields.indexOf(this.fieldsOrder[i]) !== -1){
        if (isEmpty(this.state.fields[this.fieldsOrder[i]].value)) {
          formField.current.focus();
          break;
        }
      }
      else if (
        field && get(field, 'errors.length') > 0
      ) {
        if (formField.current?.node) {
          formField.current.node.previousSibling.focus();
          break;
        } else {
          formField.current?.focus();
          break;
        }
      }
    }
  }

  getFieldErrors(field) {
    const errors = [];
    const value = get(this.state.fields, `${field}.value`);
    const validators = get(this.state.fields, `${field}.validators`, []);

    validators.forEach((validator) => {
      if (validator.isInvalid(value)) {
        errors.push(validator.message);
      }
    });

    return errors;
  }

  handleIdentifierChange = event => {
    const newState  = { ...this.state }
    newState.fields.identifier.value = event.target.value
    newState.fields.identifier.errors = this.getFieldErrors(event.target.value) || []
    this.setState(newState)
  }

  getCompanyContacts = (companyId, id) => {
    APIService.contracts().companies(companyId).contacts().get()
    .then(contacts => {
      this.setState({ [`${id}Contacts` ] : uniqBy(contacts, 'id') });
    })
  }

  handleFieldsChange = (value, id) => {
    const newState  = { ...this.state }
    newState.fields[id].value = value
    newState.fields[id].errors = this.getFieldErrors(id) || []
    this.setState(newState, () => {
      if(value)
        this.getCompanyContacts(value, id)
    })
  }
 
  handleSeasonChange = (value, id) => {
    const newState  = { ...this.state }
    newState.fields.season.value = value
    newState.fields.season.errors = this.getFieldErrors(id) || []
    this.setState(newState)
  }

  render() {
    const { previewData, preview, showEmailDialog } = this.state;
    const emailSubject = this.getEmailSubject();
    const selectedEmailParties = this.getSelectedEmailParties();
    return (
      <div ref={this.formRef} id='eligibility-dec-form-container'>
        <form onSubmit={this.onSubmit} noValidate>
        <div className='cardForm'>
          <div style={{ marginBottom: '20px' }}>
            <h4 className='cardForm-title'>General</h4>
            <div className='cardForm-content col-md-5'>
              <div className='col-md-12 form-wrap padding-reset' style={{ marginBottom: '15px' }}>
                <CommonTextField
                  id='identifier'
                  label='Identifier'
                  setRef={this.fieldRef["identifier"]}
                  placeholder='Please enter 14 digit unique number'
                  value={this.state.fields.identifier.value}
                  onChange={this.handleIdentifierChange}
                  helperText={this.state.fields.identifier.errors}
                  fullWidth
                />
              </div>
            </div>
          </div>
        </div>
        <div className='cardForm'>
          <div style={{ marginBottom: '20px' }}>
            <h4 className='cardForm-title'>Parties</h4>
              <div className='cardForm-content col-md-5'>
                <div className="col-md-12 form-wrap padding-reset">
                  <CommonAutoSelect
                    items={filter(this.state.declarantParties, obj => { return obj.id != this.state.fields.licensee.value; })}
                    label="Declarant"
                    setRef={this.fieldRef["declarant"]}
                    id="declarant"
                    value={this.state.fields.declarant.value}
                    onChange={this.handleFieldsChange}
                    errorText={this.state.fields.declarant.errors}
                    fullWidth
                    disabled={this.state.selectedEntity}
                  />
                </div>
              </div>
              <div className="cardForm-content col-md-5 col-md-offset-1">
                <div className="col-md-12 form-wrap padding-reset">
                  <CommonAutoSelect
                    items={filter(this.state.licenseeParties, obj => { return obj.id != this.state.fields.declarant.value; })}
                    label="Licensee"
                    setRef={this.fieldRef["licensee"]}
                    id="licensee"
                    value={this.state.fields.licensee.value}
                    onChange={this.handleFieldsChange}
                    errorText={this.state.fields.licensee.errors}
                    fullWidth
                    disabled={this.state.selectedEntity}
                  />
                </div>
              </div>
          </div>
        </div>
        <div className='cardForm'>
          <div style={{ marginBottom: '20px' }}>
            <h4 className='cardForm-title'>Season</h4>
            <div className='cardForm-content col-md-5'>
              <div className='col-md-12 form-wrap padding-reset'>
                <SeasonSelect
                  id="season"
                  setRef={this.fieldRef["commodity.season"]}
                  onChange={this.handleSeasonChange}
                  season={this.state.fields.season.value}
                  errorText={get(this.state.fields, 'season.errors[0]', '')}
                  disabled={this.state.selectedEntity}
                />
              </div>
            </div>
          </div>
        </div>
        <div className='cardForm'>
          <div style={{ marginBottom: '20px' }}>
            <h4 className='cardForm-title'>Declaration</h4>
            <div className='cardForm-content'>
              <div clasName="form-wrap" style={{margin: "5px", marginLeft: '15px'}}>
                  <p><b>I AM THE UNDERSIGNED PRODUCER AND/OR SELLER, AND DO SOLEMNLY DECLARE THAT</b>, unless otherwise
                  specified by me, the grain to be delivered, or sold by me, or on my behalf is of a variety eligible for the kind of grain and
                  class (if applicable) declared by me in my dealings with the recipient of this Declaration.
                  <div>This Declaration is made and intended to apply to all deliveries of grain kinds listed in the document, Kinds of Grain that
                  Require a Declaration of Eligibility for Delivery of Grain, made by me or on my behalf from and including the date indicated
                  below until the end of the crop year.
                  </div>
                  </p>
                <h4>NOTES</h4>
                <div>
                  <ol>
                    <li>
                    The following seeds designated as grain for the purposes of the Canada Grain Act require a declaration: barley,
                    beans, buckwheat, canola, fababeans, flaxseed, lentils, mustard seed, oats, peas, rapeseed, rye, triticale, and wheat
                    (Kinds of Grain that Require a Declaration of Eligibility for Delivery of Grain).
                    </li>
                    <li>
                      <p>Varieties of grain not designated to a class, where applicable, under the Canada Grain Act are only eligible for the
                      lowest grade in any class(<a href="www.grainscanada.gc.ca/en/grain-quality/variety-lists" target='_blank' rel="noopener noreferer">www.grainscanada.gc.ca/en/grain-quality/variety-lists</a>), unless exempted by
                      Commission Order (<a href="grainscanada.gc.ca/en/industry/orders/index.html" target="_blank" rel="noopener noreferer">grainscanada.gc.ca/en/industry/orders/index.html</a>)</p>
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-12 padding-reset" style={{textAlign: 'right'}}>
              <CommonButton
                onClick={this.handleCancel}
                label='Cancel'
                variant="contained"
                color='secondary'
              />
              <CommonButton
                type='submit'
                onClick={this.onSubmit}
                primary={true}
                label='Continue And Review'
                variant="contained"
              />
            </div>
        </div>
        </form>
        <Dialog open={preview} onClose={this.closePreview} scroll='paper' fullScreen>
          <DialogTitle>Vendor Declaration Preview</DialogTitle>
          <DialogContent>
            <Preview ref={el => (this.componentRef = el)} data={previewData} />
          </DialogContent>
          <DialogActions style={{paddingBottom: "40px"}}>
            <CommonButton label='Back' key='closeButton' default onClick={this.closePreview} variant='flat' />
            <CommonButton label='Submit' key='submitButton' primary={true} onClick={this.openEmailDialog} variant='flat' />
          </DialogActions>
        </Dialog>
        {
          showEmailDialog &&
          <CustomEmailDialog
            parties={EMAIL_PARTIES}
            selectedParties={selectedEmailParties}
            title="Email PDF copies to"
            open={showEmailDialog}
            noBody={true}
            subject={emailSubject}
            disableAcceptanceRequired={true}
            onClose={this.closeEmailDialog}
            partyEmails={this.getPartyEmails()}
            partyContacts={this.getPartyContacts()}
          />
        }
      </div>
    );
  }
}

const mapStateToProps = state => {
  return {
    user: state.main.user.user,
    allCompanyParties: state.companies.companies.companyParties,
  };
};

export default connect(mapStateToProps)(EligibilityDecForm);
