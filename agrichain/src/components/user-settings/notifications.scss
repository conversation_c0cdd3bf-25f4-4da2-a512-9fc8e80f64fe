@import "../../common/variables";

button.notification {
    color: $colorNavyBlue;
    font-size: 1.5rem;
    margin-right: 30px;
    background-image: url("/images/<EMAIL>");
    background-repeat: no-repeat;
    background-size: contain;
    height: 30px;
    width: 30px;

    &:hover {
        background-color: transparent;
    }

    svg {
        visibility: hidden;
    }

    & .MuiBadge-colorSecondary-52 {
        background-color: $colorGreen!important;
    }
}
.notification-drawer div:nth-child(3) {
    width: 384px;
    left: auto;
    top: 59px;
    right: 109px;
    border-radius: 4px;
    max-height: 410px;
    overflow: auto;

    @media screen and (max-width: 523px) {
        width: auto;
        right: auto;
    }

    b {
        font-weight: 500;
    }

    a {
        &:hover {
            text-decoration: none;
        }
    }

    .unread {
        background-color: #f5f8fa;
    }

    li {
        font-size: 12px;
        height: 50px;
        display: block;
    }
}
.notification-drawer div[class*="MuiBackdrop-"] {
    opacity: 0 !important;
}

.notification-drawer {
    div.notifications-content {
        overflow: auto;
    }
    div.notifications-body {
        overflow-y: scroll;
        margin-top: 27px;
    }
    div.header, div.footer {
        font-size: 12px;
        padding: 5px 10px;
    }
    div.header {
        position: fixed;
        background-color: #fff;
        z-index: 1;
        border-bottom: 1px solid lightgray;
    }
    div.footer {
        border-top: 1px solid lightgray;
    }
    span.title {
        font-weight: bold;
        padding: 0;
    }
    span.controls {
        padding: 0;
        text-align: right;
    }
    span.controls-separator {
        padding: 0;
        text-align: center;
        margin-top: -3px;
        font-weight: bold;
        color: lightgray;
    }
    .notification-row {
        white-space: normal;
        padding: 10px;
    }
    div.notification-text {
        overflow: hidden;
        padding: 0;
    }
    div.notification-timestamp {
        font-size: 11px;
        color: #808080;
        clear: both;
    }
    .no-notifications-row {
        white-space: normal;
        padding: 10px 0;
        text-align: center;
        height: 25px;
        div {
            padding: 15px;
        }
    }
    div.footer .control {
        text-align: center;
    }
}
