import React, { Component } from 'react';
import alertifyjs from 'alertifyjs';
import { connect } from 'react-redux';

import moment from 'moment';
import CommonDatePicker from '../common/CommonDatePicker';
import CommonButton from '../common/CommonButton';
import { get, isEmpty, set, mapValues, has, find, isEqual, some, forEach, partialRight, map, pick, includes, filter } from 'lodash';
import { required, selected } from '../../common/validators';
import CommodityAutoComplete from '../common/autocomplete/CommodityAutoComplete';
import GradeAutoComplete from '../common/autocomplete/GradeAutoComplete';
import SeasonSelect from '../common/select/SeasonSelect';
import APIService from '../../services/APIService';
import CommonAutoSelect from '../common/autocomplete/CommonAutoSelect';
import SiteAsyncAutocomplete from '../common/autocomplete/SiteAsyncAutocomplete';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import CommonTimePicker from '../common/CommonTimePicker';
import { getTracks } from '../../actions/main/index';
import { positiveDecimalFilter } from '../../common/input-filters';
import {
  getDateTimeFromUTC,
  getAutoSelectFocusField,
  getCountryCurrency,
} from '../../common/utils';
import { MT_UNIT } from '../../common/constants';
import { getFixtures } from '../../actions/companies/contracts';
import { getCompanyGroups} from '../../actions/api/companies';
import { COMMODITIES, PAYMENT_SCALES } from '../../common/constants';
import CurrencyField from '../common/CurrencyField'
import { getCompanyContractBids } from '../../actions/companies/contract-bids';

class AddContractBid extends Component {
    constructor(props) {
        super(props);
        this.state = {
            handlers: [],
            paymentScales: [],
            ngrs: [],
            site: null,
            fields: {
                siteId: {
                    value: null,
                    validators: [required()],
                    errors: []
                },
                track: {
                    value: null,
                    validators: [],
                    errors: []
                },
                commodityId: {
                    value: null,
                    validators: [required()],
                    errors: []
                },
                gradeId: {
                    value: null,
                    validators: [required()],
                    errors: []
                },
                season: {
                    value: null,
                    validators: [required()],
                    errors: []
                },
                price: {
                    value: null,
                    validators: [required()],
                    errors: []
                },
                limit: {
                    value: null,
                    validators: [required()],
                    errors: []
                },
                paymentTermId: {
                    value: null,
                    validators: [required(), selected()],
                    errors: []
                },
                paymentScaleId: {
                    value: null,
                    validators: [required(), selected()],
                    errors: []
                },
                startDate: {
                    value: '',
                    validators: [required()],
                    errors: []
                },
                startTime: {
                  value: '',
                  validators: [required()],
                  errors: []
                },
              endDate: {
                value: '',
                validators: [required()],
                errors: []
              },
              endTime: {
                value: moment().endOf('day').format('HH:mm'),
                validators: [required()],
                errors: []
              },
              deliveryStartDate: {
                value: '',
                validators: [required()],
                errors: []
            },
            deliveryEndDate: {
                value: '',
                validators: [required()],
                errors: []
            },
            groupId: {
                value: '',
                validators: [],
                errors: []
            },
              buyer: {
                    companyId: {
                        value: this.props.currentUser.companyId,
                        validators: [required(), selected()],
                        errors: [],
                    },
                    contactId: {
                        value: null,
                        validators: [],
                        errors: [],
                    },
                    ngrId: {
                        value: null,
                        validators: [required(), selected()],
                        errors: [],
                    },
                },
            }

        };
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleValueChange = this.handleValueChange.bind(this);
        this.handleFieldChange = this.handleFieldChange.bind(this);
        this.setFieldErrors = this.setFieldErrors.bind(this);
        this.setAllFieldsErrors = this.setAllFieldsErrors.bind(this);
        this.getFieldErrors = this.getFieldErrors.bind(this);
        this.fieldsOrder = [
            'siteId', 'track', 'commodityId', 'gradeId',
            'season', 'price', 'limit', 'paymentScaleId', 'paymentTermId', 'startDate',
            'startTime', 'endDate', 'endTime', 'buyer.companyId', 'buyer.ngrId', 'deliveryStartDate', 'deliveryEndDate', 'groupId'
        ];
        this.fieldRef = {};
        this.fieldsOrder.forEach(e => (this.fieldRef[e] = React.createRef()));
    }

    componentDidMount(prevProps, prevState) {
        const { dispatch } = this.props;
        Promise.all([
            this.getNgrs(),
            dispatch(getCompanyGroups(this.props.currentUser.companyId)),
            dispatch(getFixtures()),
            dispatch(getTracks()),
        ]);

        const newState = { ...this.state };
        let _set = false
        if (!this.props.viewSelectedContractBid && !this.props.editSelectedContractBid && (this.props.isCreate || this.props.duplicateSelectedContractBid)) {
            _set = true
                const startDateTime = getDateTimeFromUTC(Date().toLocaleString());
                newState.fields.startDate.value = startDateTime.date;
                newState.fields.startTime.value = startDateTime.time;
        }
        if (!find(this.state.handlers, {id: get(this.props, 'selectedContractBid.siteId')}) && (this.props.editSelectedContractBid || this.props.viewSelectedContractBid || this.props.duplicateSelectedContractBid) && this.props.selectedContractBid) {
            _set = true
            newState.site = { 'id': get(this.props, 'selectedContractBid.siteId'), 'name': get(this.props, 'selectedContractBid.siteName') };
            newState.fields.siteId.value = get(this.props, 'selectedContractBid.siteId');
            newState.fields.track.value = get(this.props, 'selectedContractBid.track');
            newState.fields.commodityId.value = get(this.props, 'selectedContractBid.commodityId');
            newState.fields.gradeId.value = get(this.props, 'selectedContractBid.gradeId');
            newState.fields.season.value = get(this.props, 'selectedContractBid.season');
            newState.fields.price.value = get(this.props, 'selectedContractBid.price');
            newState.fields.limit.value = get(this.props, 'selectedContractBid.limit');
            newState.fields.paymentTermId.value = get(this.props, 'selectedContractBid.paymentTerm.id');
            newState.fields.buyer.ngrId.value = get(this.props, 'selectedContractBid.buyer.ngrId');
            if (this.props.viewSelectedContractBid || this.props.editSelectedContractBid) {
                const startDateTime = getDateTimeFromUTC(get(this.props, 'selectedContractBid.startDateTime'));
                const endDateTime = getDateTimeFromUTC(get(this.props, 'selectedContractBid.endDateTime'));
                newState.fields.startDate.value = startDateTime.date;
                newState.fields.startTime.value = startDateTime.time;
                newState.fields.endDate.value = endDateTime.date;
                newState.fields.endTime.value = endDateTime.time;

                const deliveryStartDate = getDateTimeFromUTC(get(this.props, 'selectedContractBid.deliveryStartDate'));
                const deliveryEndDate = getDateTimeFromUTC(get(this.props, 'selectedContractBid.deliveryEndDate'));
                newState.fields.deliveryStartDate.value = deliveryStartDate.date;
                newState.fields.deliveryEndDate.value = deliveryEndDate.date;
            }
        }

      if(_set && !isEqual(prevState, newState))
        this.setState(newState);
    }

    componentDidUpdate(prevProps, prevState) {
      const newState = { ...this.state };
      let _set = false

      if (!isEqual(this.props.paymentScales, prevState.paymentScales)) {
        _set = true
            if (newState.fields.commodityId.value === COMMODITIES.CANOLA) {
                newState.paymentScales = this.props.paymentScales;
                newState.fields.paymentScaleId.value = get(this.props, 'selectedContractBid.paymentScale');
                newState.fields.paymentScaleId.errors = [];
            } else 
                newState.paymentScales = [find(this.props.paymentScales, { id: PAYMENT_SCALES.FLAT })];
        }
        if (!this.state.fields.groupId.value && !isEmpty(this.props.companyGroups) && this.props.selectedContractBid?.group) {
            _set = true
            newState.fields.groupId.value = get(this.props, 'selectedContractBid.group.id')
        }
      if(_set && !isEqual(prevState, newState))
        this.setState(newState);
    }

    async getNgrs() {
        if (!this.props.viewSelectedContractBid) {
            await APIService.companies(this.props.currentUser.companyId)
                .ngrs()
                .appendToUrl('minimal/')
                .get()
                .then(items => {
                    const newState = { ...this.state };
                    newState.ngrs = items;
                    newState.fields.buyer.companyId.value = this.props.currentUser.companyId;
                    newState.fields.buyer.contactId.value = this.props.currentUser.id;
                    if (this.props.isCreate)
                        newState.fields.paymentTermId.value = this.props.currentUser.company.paymentTermId;
                    this.setState(newState);
                });
            await this.defaultNgr(this.props.currentUser.companyId)
        }
    };

    setAllFieldsErrors() {
        forEach(this.state.fields, (value, key) => this.setFieldErrors(key));
        forEach(this.state.fields.buyer, (value, key) => this.setFieldErrors(`buyer.${key}`));
    }

    getAmendedData = data => {
        const { selectedContractBid } = this.props;
        let editedData = { ...data}
        map(data, (value, key) => {
            if(includes(['startDateTime', 'endDateTime'], key) && value == moment(get(selectedContractBid, key)).utc().format('YYYY-MM-DD HH:mm:ss'))
                delete editedData[key];
            else if (value === get(selectedContractBid, key))
                delete editedData[key];
        });
        return editedData;
    }

    handleSubmit(event) {
        event.preventDefault();
        this.setAllFieldsErrors();

        let isFormInvalid = some(this.state.fields, field => {
            return field.errors.length > 0;
        }) || some(this.state.fields.buyer, field => {
            return field.errors.length > 0;
        });
        this.focusOnFirstErrorField();
        if (!isFormInvalid) {
            const utcStartDateTime = moment(this.state.fields.startDate.value + ' ' + this.state.fields.startTime.value).utc();
            const utcEndDateTime = moment(this.state.fields.endDate.value + ' ' + this.state.fields.endTime.value).utc();
            if (moment(utcStartDateTime).isAfter(utcEndDateTime)) {
                isFormInvalid = true;
                alertifyjs.error("End date should be greater than start date");
            };
            if (!isFormInvalid) {
                const data = mapValues(this.state.fields, 'value');
                const buyer = mapValues(this.state.fields.buyer, 'value');
                data.buyer = buyer;
                if (has(data, 'startDate') && has(data, 'startTime')) {
                    delete data['startDate'];
                    delete data['startTime'];
                    data.startDateTime = utcStartDateTime.format('YYYY-MM-DD HH:mm:ss');
                }
                if (has(data, 'endDate') && has(data, 'endTime')) {
                    delete data['endDate'];
                    delete data['endTime'];
                    data.endDateTime = utcEndDateTime.format('YYYY-MM-DD HH:mm:ss');
                }
                data.gradeId = get(data, 'gradeId.id', get(data, 'gradeId'));

                if (!this.props.editSelectedContractBid || this.props.duplicateSelectedContractBid) {
                    APIService.contract_bids().request(
                        'POST', data, null
                    ).then(() => {
                        alertifyjs.success('Contract Bid Created', 3);
                        this.props.onClose();
                        if (this.props.tab == '/contract-bids/active')
                            this.props.dispatch(getCompanyContractBids(true, null, 'my_active'));
                        else
                            window.location.reload();
                    }).catch(res => alertifyjs.error(get(res, 'response.data.errors', 'An Error Occurred'), 3));
                }
                else if (this.props.editSelectedContractBid && this.props.selectedContractBid?.createdById == this.props.currentUser.id) {
                    let editedData = this.getAmendedData(pick(data, ['limit', 'startDateTime', 'endDateTime']))
                    if (Object.keys(editedData).some(key => key !== 'startDateTime')) {
                        APIService.contract_bids(this.props.selectedContractBid.id)
                        .put(editedData)
                        .then(() => {
                            alertifyjs.success('Contract Bid Updated', 3);
                            this.props.onClose();
                            this.props.dispatch(getCompanyContractBids(true, null, 'my_active'));
                        }).catch(res => alertifyjs.error(get(res, 'response.data.errors', 'An Error Occurred'), 3));
                    }
                }
            }
        }
    }
    async defaultNgr(value) {
        if (this.props.isCreate) {
            APIService.companies(value).appendToUrl('ngrs/tags/title_transfer/')
                .get()
                .then(response => {
                    const newState = {...this.state};
                    set(newState.fields, 'buyer.ngrId.value', get(response, 'ngrId'));
                    this.setState(newState);
                })
        }
    }

    focusOnFirstErrorField() {
        const nestedFields = ["siteId", "track", "season", "price", "limit", 'paymentScaleId', 'paymentTermId', "buyer.ngrId",
            "startDate", "startTime", "endDate", "endTime"
        ];
        const autoCompleteFields = ["commodityId", "gradeId"];

        for (let i = 0; i < this.fieldsOrder.length; i++) {
            const formField = this.fieldRef[this.fieldsOrder[i]];
            const field = this.state.fields[this.fieldsOrder[i]];
            if (nestedFields.indexOf(this.fieldsOrder[i]) !== -1) {
                if ((this.fieldsOrder[i] === "siteId" && this.state.fields.siteId.errors.length) ||
                    (this.fieldsOrder[i] === "track" && this.state.fields.track.errors.length) ||
                    (this.fieldsOrder[i] === 'paymentScaleId' && this.state.fields.paymentScaleId.errors.length) ||
                    (this.fieldsOrder[i] === 'paymentTermId' && this.state.fields.paymentTermId.errors.length) ||
                    (this.fieldsOrder[i] === "buyer.ngrId" && this.state.fields.buyer.ngrId.errors.length) ||
                    (this.fieldsOrder[i] === "buyer.companyId" && this.state.fields.buyer.companyId.errors.length) ||
                    (this.fieldsOrder[i] === "startDate" && this.state.fields.startDate.errors.length) ||
                    (this.fieldsOrder[i] === "startTime" && this.state.fields.startTime.errors.length) ||
                    (this.fieldsOrder[i] === "endDate" && this.state.fields.endDate.errors.length) ||
                    (this.fieldsOrder[i] === "endTime" && this.state.fields.endTime.errors.length)) {
                    getAutoSelectFocusField(this.fieldRef, this.fieldsOrder[i]);
                    break;
                }
                if ((this.fieldsOrder[i] === "siteId" && this.state.fields.siteId.errors.length > 0) ||
                    (this.fieldsOrder[i] === "season" && this.state.fields.season.errors.length > 0) ||
                    (this.fieldsOrder[i] === "price" && this.state.fields.price.errors.length) ||
                    (this.fieldsOrder[i] === "limit" && this.state.fields.limit.errors.length)) {
                    formField.current.focus();
                    break;
                }
            } else if (autoCompleteFields.indexOf(this.fieldsOrder[i]) !== -1) {
                if (field && field.errors.length > 0) {
                    getAutoSelectFocusField(this.fieldRef, this.fieldsOrder[i]);
                    break;
                }
            } else if (field && field.errors.length > 0) {
                if (get(formField, 'current.node')) {
                    formField.current.node.previousSibling.focus();
                    break;
                } else {
                    const current = get(formField, 'current');
                    if (current)
                        current.focus();
                    break;
                }
            }
        }
    }

    async handleValueChange(value, id, item) {
        const newState = { ...this.state };
        set(newState.fields, `${id}.value`, value);
        if (id == 'commodityId' && !this.props.selectedContractBid) {
            if (value === COMMODITIES.CANOLA) {
                newState.paymentScales = this.props.paymentScales;
                newState.fields.paymentScaleId.value = PAYMENT_SCALES.AOF_BASIC;
                newState.fields.paymentScaleId.errors = [];
            } else {
                newState.paymentScales = [find(this.props.paymentScales, { id: PAYMENT_SCALES.FLAT })];
            }
        }
        if(id == 'siteId')
            newState.site = item;
        if (id == 'buyer.companyId' && value) {
            this.defaultNgr(value)
            APIService.companies(value)
            .ngrs()
            .appendToUrl('minimal/')
            .get()
            .then(items => {
                const newState = { ...this.state };
                newState.ngrs = items;
                this.setState(newState);
            });
        }
        if(!isEmpty(this.state.ngrs))
            newState.ngrs = this.state.ngrs;
        this.setState(newState, () => this.setFieldErrors(id));
    }

    setFieldErrors(key) {
        const newState = { ...this.state };
        set(newState.fields, key + '.errors', this.getFieldErrors(key));
        this.setState(newState);
    }

    getFieldErrors(key) {
        let errors = [];
        const value = get(this.state.fields, `${key}.value`);
        const validators = get(this.state.fields, `${key}.validators`) || [];
        
        validators.forEach(validator => {
            if (validator.isInvalid(value)) {
                errors.push(validator.message);
            }
        });
        return errors;
    }

    handleFieldChange = event => this.handleValueChange(event.target.value, event.target.id);

    handleCheckboxClick = event => this.handleValueChange(event.target.checked, event.target.id);

    getSelectedCommodity = commodityId => {
        const id = commodityId || this.state.fields.commodityId.value
        return id ? find(this.props.commodities, {id: id}) : null
    }

    render() {
        const priceUnit = get(this.getSelectedCommodity(this.state.fields.commodityId.value), 'tonnageUnit', MT_UNIT)
        return (
            <form noValidate>
                <div className="cardForm cardForm--drawer">
                    <div className="cardForm-content row">
                        <div className='col-sm-12 form-wrap' ref={this.fieldRef['siteId']}>
                            <SiteAsyncAutocomplete
                                limitTags={2}
                                label="Site"
                                id="siteId"
                                onChange={item => this.handleValueChange(item?.id, 'siteId', item)}
                                selected={get(this.state, 'site', '')}
                                minLength={3}
                                variant="standard"
                                disabled={(this.props.viewSelectedContractBid || this.props.editSelectedContractBid) && this.props.selectedContractBid}
                                popupIcon={(this.props.viewSelectedContractBid && this.props.selectedContractBid) ? <i className="icon-lock-inline"></i> : undefined}
                                fullWidth
                                activeSitesOnly
                                addLabel
                                errorText={get(this.state.fields, 'siteId.errors[0]')}
                                farmId={(this.props.editSelectedContractBid || this.props.viewSelectedContractBid) ? get(this.props.selectedContractBid, 'siteId') : null}
                            />
                        </div>
                        <div className='col-md-12 form-wrap'>
                            <CommonAutoSelect
                                id='track'
                                setRef={this.fieldRef['track']}
                                label='Port (Optional)'
                                errorText={get(this.state.fields.track, 'errors[0]')}
                                value={this.state.fields.track.value}
                                errorStyle={{ textAlign: 'left' }}
                                onChange={this.handleValueChange}
                                items={this.props.tracks}
                                disabled={this.props.viewSelectedContractBid || this.props.editSelectedContractBid}
                            />
                        </div>
                        <div className='col-md-12 form-wrap'>
                            <CommodityAutoComplete
                                id="commodityId"
                                setRef={this.fieldRef["commodityId"]}
                                onChange={this.handleValueChange}
                                floatingLabelText="Commodity"
                                commodityId={this.state.fields.commodityId.value}
                                errorText={get(this.state, 'fields.commodityId.errors[0]', '')}
                                disabled={(this.props.viewSelectedContractBid || this.props.editSelectedContractBid) && this.props.selectedContractBid}
                            />
                        </div>
                        <div className='col-md-12 form-wrap'>
                            <GradeAutoComplete
                                id="gradeId"
                                onChange={this.handleValueChange}
                                floatingLabelText="Grade"
                                setRef={this.fieldRef["gradeId"]}
                                commodityId={this.state.fields.commodityId.value}
                                gradeId={this.state.fields.gradeId.value}
                                season={this.state.fields.season.value}
                                dependsOnCommodity
                                dependsOnSeason
                                selectedGradeId={this.state.fields.gradeId.value}
                                errorText={get(this.state, 'fields.gradeId.errors[0]', '')}
                                disabled={(this.props.viewSelectedContractBid || this.props.editSelectedContractBid)  && this.props.selectedContractBid}
                            />
                        </div>
                        <div className='col-md-12 form-wrap'>
                            <SeasonSelect
                                id="season"
                                setRef={this.fieldRef["season"]}
                                onChange={this.handleValueChange}
                                season={this.state.fields.season.value}
                                errorText={get(this.state, 'fields.season.errors[0]', '')}
                                disabled={(this.props.viewSelectedContractBid || this.props.editSelectedContractBid)  && this.props.selectedContractBid}
                            />
                        </div>
                        <div className='col-md-12 form-wrap'>
                            <CurrencyField
                                error={!isEmpty(get(this.state.fields.price, 'errors[0]'))}
                                id='price'
                                label={`Price (per ${priceUnit})`}
                                value={this.state.fields.price.value || ''}
                                helperText={get(this.state.fields.price, 'errors[0]')}
                                inputRef={this.fieldRef['price']}
                                fullWidth
                                onChange={this.handleFieldChange}
                                onKeyDown={event => positiveDecimalFilter(event, 2, 9999999.99)}
                                disabled={this.props.viewSelectedContractBid || this.props.editSelectedContractBid}
                                variant="standard"
                                selectedCurrency={getCountryCurrency()}
                                disabledCurrency={true}
                                />
                        </div>
                        <div className='col-md-12 form-wrap'>
                            <TextField
                                id='limit'
                                label='Limit'
                                inputRef={this.fieldRef['limit']}
                                placeholder='Limit'
                                value={this.state.fields.limit.value || ''}
                                fullWidth
                                maxLength='1000000'
                                helperText={get(this.state.fields.limit, 'errors[0]')}
                                error={!isEmpty(get(this.state.fields.limit, 'errors[0]'))}
                                style={{ float: 'left' }}
                                onChange={this.handleFieldChange}
                                onKeyDown={event => positiveDecimalFilter(event, 2, 999999.99)}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position='end' style={{ color: 'rgb(162,162,162)' }}>
                                        {priceUnit}
                                        </InputAdornment>
                                    ),
                                }}
                                disabled={this.props.viewSelectedContractBid}
                                variant="standard" />
                        </div>
                        <div className='col-md-12 form-wrap'>
                            <CommonAutoSelect
                                id='paymentScaleId'
                                setRef={this.fieldRef['paymentScaleId']}
                                onChange={this.handleValueChange}
                                label='Payment Scale'
                                value={this.state.fields.paymentScaleId.value}
                                floatingLabelText='Payment Scale'
                                dataSourceConfig={{ text: 'name', value: 'id' }}
                                errorText={get(this.state, 'fields.paymentScaleId.errors[0]', '')}
                                items={this.state.paymentScales}
                                disabled={(this.props.viewSelectedContractBid || this.props.editSelectedContractBid) && this.props.selectedContractBid}
                            />
                        </div>
                        <div className='col-md-12 form-wrap'>
                            <CommonAutoSelect
                                id='paymentTermId'
                                setRef={this.fieldRef['paymentTermId']}
                                onChange={this.handleValueChange}
                                label='Payment Terms'
                                dataSourceConfig={{ text: 'name', value: 'id' }}
                                value={this.state.fields.paymentTermId.value}
                                errorText={get(this.state, 'fields.paymentTermId.errors[0]', '')}
                                items={this.props.paymentTerms}
                                disabled={(this.props.viewSelectedContractBid || this.props.editSelectedContractBid)&& this.props.selectedContractBid}
                            />
                        </div>
                        {this.props.viewSelectedContractBid && this.props.selectedContractBid?.buyer?.ngrNumber ?
                            <div className='col-md-12 form-wrap'>
                                <TextField
                                    id="buyer.ngrId"
                                    label="Buyer NGR"
                                    placeholder='Buyer NGR'
                                    value={this.props.selectedContractBid?.buyer?.ngrNumber || ''}
                                    fullWidth
                                    disabled={this.props.viewSelectedContractBid || this.props.editSelectedContractBid}
                                    variant="standard" />
                            </div> :
                            <div className="col-md-12 form-wrap">
                                <CommonAutoSelect
                                    items={this.state.ngrs || []}
                                    setRef={this.fieldRef["buyer.ngrId"]}
                                    id="buyer.ngrId"
                                    dataSourceConfig={{ text: 'ngrNumber', value: 'id' }}
                                    label="Buyer NGR"
                                    value={this.state.fields.buyer.ngrId.value}
                                    errorText={get(this.state.fields.buyer.ngrId, 'errors[0]')}
                                    onChange={this.handleValueChange}
                                    disabled={this.props.viewSelectedContractBid || this.props.editSelectedContractBid}
                                />
                            </div>}
                        
                        <div className="col-md-12 form-wrap">
                            <CommonDatePicker
                                id="deliveryStartDate"
                                setRef={this.fieldRef["startDate"]}
                                floatingLabelText="Delivery Start Date"
                                onChange={this.handleValueChange}
                                errorText={get(this.state.fields.deliveryStartDate, 'errors[0]')}
                                value={this.state.fields.deliveryStartDate.value}
                                style={{ float: 'right' }}
                                disabled={this.props.viewSelectedContractBid || this.props.editSelectedContractBid}
                            />
                        </div>
                        <div className="col-md-12 form-wrap">
                            <CommonDatePicker
                                id="deliveryEndDate"
                                setRef={this.fieldRef["startDate"]}
                                floatingLabelText="Delivery End Date"
                                onChange={this.handleValueChange}
                                errorText={get(this.state.fields.deliveryEndDate, 'errors[0]')}
                                value={this.state.fields.deliveryEndDate.value}
                                style={{ float: 'right' }}
                                disabled={this.props.viewSelectedContractBid || this.props.editSelectedContractBid}
                            />
                        </div>
                        <div className='col-md-12 form-wrap'>
                            { this.props.viewSelectedContractBid && !find(this.props.companyGroups, { id: get(this.props, 'selectedContractBid.group.id') }) ?
                            <TextField
                                id="groupId"
                                label="Group"
                                placeholder='Group'
                                value={get(this.props, 'selectedContractBid.group.name') || ''}
                                fullWidth
                                disabled
                                variant="standard"
                                />
                            : <CommonAutoSelect
                                id='groupId'
                                setRef={this.fieldRef['group']}
                                onChange={this.handleValueChange}
                                label='Group'
                                value={this.state.fields.groupId.value}
                                floatingLabelText='Group'
                                dataSourceConfig={{ text: 'name', value: 'id' }}
                                items={filter(this.props.companyGroups, group => group?.type == 'contract_bids')}
                                disabled={(this.props.viewSelectedContractBid || this.props.editSelectedContractBid) && this.props.selectedContractBid}
                            />
                        }
                        </div>
                        <div className="col-md-12">
                            <div className="col-md-6 form-wrap no-left-padding">
                                <CommonDatePicker
                                    id="startDate"
                                    setRef={this.fieldRef["startDate"]}
                                    floatingLabelText="Start Date"
                                    onChange={this.handleValueChange}
                                    errorText={get(this.state.fields.startDate, 'errors[0]')}
                                    value={this.state.fields.startDate.value}
                                    style={{ float: 'right' }}
                                    disabled={this.props.viewSelectedContractBid && this.props.selectedContractBid}
                                />
                            </div>
                            <div style={{ marginLeft: '0px' }} className="col-md-6 col-md-offset-2 form-wrap no-right-padding">
                                <CommonTimePicker
                                    id="startTime"
                                    setRef={this.fieldRef["startTime"]}
                                    floatingLabelText="Start Time"
                                    value={this.state.fields.startTime.value}
                                    onChange={this.handleValueChange}
                                    errorText={get(this.state.fields.startTime, 'errors[0]')}
                                    style={{ float: 'right' }}
                                    disabled={this.props.viewSelectedContractBid && this.props.selectedContractBid}
                                />
                            </div>
                        </div>
                        <div className="col-md-12">
                            <div className="col-md-6 form-wrap no-left-padding">
                                <CommonDatePicker
                                    id="endDate"
                                    setRef={this.fieldRef["endDate"]}
                                    floatingLabelText="End Date"
                                    onChange={this.handleValueChange}
                                    errorText={get(this.state.fields.endDate, 'errors[0]')}
                                    disabled={this.props.viewSelectedContractBid}
                                    value={this.state.fields.endDate.value}
                                    style={{ float: 'right' }}
                                    minDate={moment()}
                                />
                            </div>
                            <div style={{ marginLeft: '0px' }} className="col-md-6 col-md-offset-2 form-wrap no-right-padding">
                                <CommonTimePicker
                                    id="endTime"
                                    setRef={this.fieldRef["endTime"]}
                                    floatingLabelText="End Time"
                                    value={this.state.fields.endTime.value}
                                    onChange={this.handleValueChange}
                                    disabled={this.props.viewSelectedContractBid}
                                    errorText={get(this.state.fields.endTime, 'errors[0]')}
                                    style={{ float: 'right' }}
                                />
                            </div>
                        </div>
                        <div className='col-sm-12 cardForm-action top15 padding-reset'>
                            <CommonButton type='button' variant='outlined' label='Close' default onClick={this.props.onClose} />
                            {!this.props.viewSelectedContractBid &&
                                <CommonButton type='submit' primary variant='outlined' label='Save' default onClick={this.handleSubmit} />
                            }
                        </div>
                    </div>
                </div>
            </form>
        );
    }
}

const mapStateToProps = state => {
    var paymentTerms = [];
    var paymentScales = [];
    if (!isEmpty(state.companies.contracts.paymentTerms)) {
        paymentTerms = state.companies.contracts.paymentTerms;
    }
    if (!isEmpty(state.companies.contracts.paymentScales)) {
        paymentScales = map(state.companies.contracts.paymentScales, partialRight(pick, ['id', 'name']));
    }
    const tracks = state.main.tracks;
    return {
        token: state.main.user.token,
        varieties: state.master.varieties.items || [],
        currentUser: state.main.user.user,
        paymentTerms: paymentTerms,
        paymentScales: paymentScales,
        tracks: tracks,
        selectedContractBid: state.companies.contractBids.selectedContractBid,
        editSelectedContractBid: state.companies.contractBids.editSelectedContractBid,
        viewSelectedContractBid: state.companies.contractBids.viewSelectedContractBid,
        duplicateSelectedContractBid: state.companies.contractBids.duplicateSelectedContractBid,
        commodities: state.master.commodities.items,
        companyGroups: state.companies.companies.companyGroups,
    };
};
export default connect(mapStateToProps)(AddContractBid);
