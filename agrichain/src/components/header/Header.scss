@import "../../common/variables";

header.header {
  position: fixed;
  flex-direction: row;
  justify-content: space-between;
  top: 0;
  left: 0;
  z-index: 1000;
  height: 76px;
  background-color: #fff !important;
  padding-right: 0px !important;
  box-shadow: none;
  border-bottom: 1px solid $colorGrey;

  @media screen and (min-width: 1024px){
    height: 88px;
  }

  & .content-left {
    width: 75px;
  }

  & .content-right{
    width: calc(100% - 75px);
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    transition: all 0.5s ease;
  }

  & .header-content{
    display: flex;
    justify-content: space-between;
  }

  .logo {
    padding-left: 15px;
    padding-right: 0;
    position: relative;
    top: 1px;
    margin-top: -1px;
  }

  img.agrichain-logo {
    width: 90%;
    display: none;
  }

  img.agrichain-logo-icon {
    width: 36px;
  }


  & .breadcrumb {
      padding-left: 12px !important;
      color: $colorBlack !important;
      width: 80% !important;

    & .page-header-icon {
        padding-right: 0px !important;
        padding-left: 5px !important;
        width: auto !important;
    }

    & .breadcrumb-title {
      width: 100% !important;
      font-size: 1rem !important;
      margin: 0 !important;

      @media screen and (min-width: 1024px) {
          font-size: 1.5rem !important;
      }

      span {
        font-weight: normal !important;
        font-size: 0.9rem !important;

        @media screen and (min-width: 1024px) {
            font-size: 1.250rem;
        }
      }
    }

    & .breadcrumb-nav {
        width: 100% !important;
        display: block !important;

      a {
          font-size: 0.750rem !important;
          color: $colorBlack !important;
      }
      .angle-right {
          width: 25px !important;
      }

      .no-link {
          font-size: 0.750rem !important;
          color: $colorLightGrey !important;
      }

      @media screen and (max-width: 523px) {
          overflow: hidden !important;
      }
    }
  }
}

div.offline-message-container {
    position: fixed;
    top: 0;
    color: white;
    background-color: red;
    opacity: 0.6;
    min-width: 100%;
    text-align: center;
    height: 87px;
    padding: 32px;
}
