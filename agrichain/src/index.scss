@import 'common/variables';

html, body {
    font-size: 16px;
    font-family: "Roboto", "Helvetica", "Arial", sans-serif !important;
    @media screen and (max-width: 523px) {
        font-size: 13px;
    }
    @media screen and (max-width: 850px) {
        div#non-prod-header, .header.app-header, div.left-bar {
            display: none !important;
        }
    }
    @font-face {
        font-display: auto;
    }
}

.content-wrap-right {
    padding: 8px 8px 10px 0;
}

.nav-collapsed {
    header.header {
        & .content-left {
            width: 230px;
            -webkit-transition: all 0.5s ease;
            -moz-transition: all 0.5s ease;
            -ms-transition: all 0.5s ease;
            transition: all 0.5s ease;
        }

        & .agrichain-logo{
            display: block;
        }

        & .agrichain-logo-icon{
            display: none;
        }

        & .content-right {
            width: calc(100% - 230px);
        }
    }

    .left-bar {
       width: 230px;
        -webkit-transition: all 0.5s ease;
        -moz-transition: all 0.5s ease;
        -ms-transition: all 0.5s ease;
        transition: all 0.5s ease;

        & span.nav-text {
            font-size: 14px;
            transition: all 0.5s ease;
        }
    }

    & .main-container {
        padding-left: 240px;
    }

    & .btn-pinned {
        background: #00192b url("/images/unpinned.png") no-repeat 5px;
        background-size: 28px;
        -webkit-transition: all 0.5s ease;
        -moz-transition: all 0.5s ease;
        -ms-transition: all 0.5s ease;
        transition: all 0.5s ease;
    }
}

.main-container {
    min-height: calc(100vh - 40px);
    padding-top: 76px;
    padding-bottom: 16px;
    padding-left: 85px;
    &.inside-app {
        padding-left: 0 !important;
        padding-top: 0 !important;
        > div.content.content-wrap-right {
            padding: 0 !important;
            .table-search-box {
                width: 50% !important;
                margin-top: 0px !important;
            }
            .freight-scheduler {
                top: 0px;
                margin-left: 0px;
                width: 100%;
                .section.left-section {
                    height: calc(100vh - 50px);
                }
                .right-section .content {
                    height: calc(100vh - 92px);
                }

            }
        }
    }
    &.logged-out {
        padding-left: 10px;
    }
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    transition: all 0.5s ease;

    @media screen and (min-width: 1024px){
        padding-top: 88px;
    }
    @media screen and (max-width: 850px) {
        padding-left: 0px;
        padding-top: 0px;
        [class^="col-xs-"] {
            padding-left: 5px;
            padding-right: 5px;
        }
        button[label=Save], button[label=Submit], button[label=Search], button[aria-label=Actions], button.common-listing-button, button.common-add-button {
            display: none !important;
            &.login-button {
                display: inline-block !important;
            }
        }
        .row {
            margin: 0 !important;
        }
        div.content-wrap-right {
            padding: 0;
        }
        .add-button {
            margin-bottom: 20px !important;
        }
        .table-search-box {
            width: 50% !important;
            margin-top: 0px !important;
        }
        div#generic-table-pagination>div {
            padding-left: 0;
        }
        .xxsmall {
            max-width: inherit !important;
        }
        .msmall {
            max-width: inherit !important;
        }
        .xsmall {
            max-width: inherit !important;
        }
        .small {
            max-width: inherit !important;
        }
        .medium {
            max-width: inherit !important;
        }
        .large {
            max-width: inherit !important;
        }
        .xlarge {
            max-width: inherit !important;
        }
        i.icon-lock {
            top: 18px !important;
        }

        div.side-drawer-paper {
            height: 100%;
            padding: 10px;
            top: 0;
            left: 0;
            svg.side-drawer-close-icon {
                top: 10px;
                right: 10px;
            }
        }
        div.cardForm-action {
            justify-content: center;
            margin-top: 10px
        }
        div.subTab-header>div>div {
            overflow: auto;
        }
        .common-button {
            min-width: inherit !important;
        }

        div.contract-details-container, div.order-details-container {
            div.section-details-container-4 {
                display: flex;
                flex-direction: column;
                > div {
                    border-right: none !important;
                }
            }
            .basic-info {
                margin-bottom: 0px;
            }
            div#movement-details-basic-info-card, div#order-details-basic-info-card, div#transfer-details-basic-info-card {
                overflow: auto;
            }
            div.contract-details-status-section, div.order-details-status-section {
                .item {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .field-label, .field-value {
                        font-size: 1.1rem;
                    }
                }
                .item2 > div {
                    padding: 5px;
                }
                .tonnage-widget {
                    width: 100% !important;
                    padding: 5px !important;
                    > span > span {
                        display: flex;
                        width: 100%;
                        span.field-label {
                            width: 10%;
                            display: flex;
                            justify-content: flex-start;
                            white-space: nowrap;
                        }
                        span.field-value {
                            width: 90%;
                            display: flex;
                            justify-content: flex-end;
                            white-space: nowrap;
                        }
                    }
                }
            }
            .tab .tab-header div.tabs {
                top: 0 !important;
                left: 0;
                width: 100%;
                & > div {
                    overflow: auto !important;
                }
            }
            div.contract-status {
                overflow: auto;
                padding: 0 !important;
                .status-actions {
                    display: none;
                }
                .contract-status-bar {
                    width: 100%;
                    li {
                        text-align: center;
                        width: auto !important;
                        justify-content: center !important;
                        padding: 0 5px !important;
                        align-items: center;
                        &:empty {
                            display: none;
                        }
                        &.status > span {
                            white-space: nowrap;
                        }
                    }
                    li:nth-child(3), li:nth-child(4), li:nth-child(5) {
                        padding: 5px !important;
                        > span {
                            display: flex;
                            width: 100%;
                            span.field-label {
                                width: 10%;
                                display: flex;
                                justify-content: flex-start;
                                white-space: nowrap;
                            }
                            span.field-value {
                                width: 90%;
                                display: flex;
                                justify-content: flex-end;
                                white-space: nowrap;
                            }
                        }
                    }
                }
            }
            .section-details-container {
                display: flex;
                flex-direction: column;
                & > div {
                    border-right: none !important;
                }
            }
        }
    }
}


.angle-right {
    position: relative;
    display: inline-block;
    width: 15px;
    height: 15px;

    &::after {
        position: absolute;
        top: 50%;
        left: 25%;
        content: "";
        width: 7px;
        height: 7px;
        -webkit-transform: rotate(45deg);
        -moz-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        transform: rotate(45deg);
        border-top: 1px solid $colorBlack;
        border-right: 1px solid $colorBlack;
    }
}
.btn-red {
   background: #f44336;
    span:first-child {
        z-index: 1;
        color: $colorWhite;
    }
    &:hover {
        opacity: 0.8;
    }
}

.btn-green {
    background: #6aae20;
    span:first-child {
        z-index: 1;
        color: $colorWhite;
    }
    &:hover {
        opacity: 0.8;
    }
}

.main-container {
    overflow: hidden;
}

@page {
    size: auto;
    margin: 0.5cm;
}

@media print
{
    .left-bar, .nav-bar {
        display: none !important;
    }
    .content {
        padding: 0 !important;
        display: inline !important;
        .col-sm-1, .col-sm-2, .col-xs-3, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-md-6, .col-md-12, .col-md-10 {
            float: left !important;
        }
        .col-sm-12, .col-md-12 {
            width: 100% !important;
        }
        .col-sm-11 {
            width: 91.66666667% !important;
        }
        .col-sm-10, .col-md-10 {
            width: 83.33333333% !important;
        }
        .col-sm-9 {
            width: 75% !important;
        }
        .col-sm-8 {
            width: 66.66666667% !important;
        }
        .col-sm-7 {
            width: 58.33333333% !important;
        }
        .col-sm-6, .col-md-6 {
            width: 50% !important;
        }
        .col-sm-5 {
            width: 41.66666667% !important;
        }
        .col-sm-4 {
            width: 33.33333333% !important;
        }
        .col-sm-3, .col-xs-3 {
            width: 25% !important;
        }
        .col-sm-2 {
            width: 16.66666667% !important;
        }
        .col-sm-1 {
            width: 8.33333333% !important;
        }
    }

}

div#json-data {
    position: absolute;
    left: 4%;
    width: 100%;
    div.response {
        padding: 20px;
        margin: 1px;
        background-color: rgba(240, 173, 78, 0.4);
        width: auto;
        font-style: italic;
        font-family: monospace;
        color: darkgray;
        &.error {
            background-color: rgba(239, 96, 48, 0.4)
        }
    }
}

.actions .btn-red-outlined {
    color: #f44336 !important;
    border: 1px solid rgba(244, 67, 54, 0.5) !important;

}
.actions .btn-red-outlined:hover {
    background-color: rgba(244, 67, 54, 0.08) !important;
}
