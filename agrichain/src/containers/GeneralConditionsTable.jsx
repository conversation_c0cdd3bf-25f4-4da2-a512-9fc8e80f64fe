import { connect } from 'react-redux';
import GenericTable from '../components/GenericTable';
import { clickEditGeneralConditionIcon } from '../actions/companies/conditions';

const mapStateToProps = (state) => {
  var conditions = state.companies.conditions.generalConditions;
  conditions.forEach(condition => {
    condition.defaultDisplayText = condition.default ? 'Yes' : 'No';
  });
  return {
    columns: [
      { key: 'module', header: 'Type', className: 'xlarge capitalize'},
      { key: 'name', header: 'Name', className: 'xlarge'},
      { key: 'details', header: 'Details', className: 'xlarge'},
      { key: 'defaultDisplayText', header: 'Default', className: 'small'},
    ],
    items: conditions,
    noRecordFoundText : 'No General Condition Exists. Create your first General Condition.',
    scrollToTopOnUpdate: false,
    editColumnClass: 'xsmall',
    orderBy: 'type',
    displayIDColumn: 'name'
  };
};
const mapDispatchToProps = (dispatch) => {
  return {
    handleDefaultCellClick: (item) => {
        dispatch(clickEditGeneralConditionIcon(item));
    }
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(GenericTable);
