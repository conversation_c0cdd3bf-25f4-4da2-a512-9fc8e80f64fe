import { connect } from 'react-redux';
import CommodityContractInvoiceForm from '../components/invoices/CommodityContractInvoiceForm';
import {createAmendInvoice, createInvoice, deleteInvoice, editInvoice, generateInvoice} from '../actions/companies/invoices';
import {clickAddEmployeeButton} from "../actions/company-settings/employees";

const mapDispatchToProps = (dispatch) => {
  return {
    submit: (data, successCallback) => {
      dispatch(createInvoice(data, false, false, successCallback));
    },
    createAmendInvoice: (data, successCallback) => {
      dispatch(createAmendInvoice(data, false, false, successCallback));
    },
    editInvoice: (data, invoiceId, contractId) => {
      dispatch(editInvoice(data, invoiceId, contractId));
    },
    deleteInvoice: (invoiceId) => {
      dispatch(deleteInvoice(invoiceId));
    },
    generateInvoice: (invoiceId, contractId, data) => {
      dispatch(generateInvoice(invoiceId, contractId, null, data));
    },
    handleAddEmployeeButtonClick: () => {
      dispatch(clickAddEmployeeButton());
    }
  };
};

export default connect(null, mapDispatchToProps)(CommodityContractInvoiceForm);
