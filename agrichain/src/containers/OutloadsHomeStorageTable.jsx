import { connect } from 'react-redux';
import GenericTable from '../components/GenericTable';
import find from 'lodash/find';
import get from 'lodash/get';
import {getGradeName, getDocketNumberValue, getCommoditySpecColumns, getCountryLabel} from '../common/utils';


const mapStateToProps = (state) => {
  const commodityId = get(state, 'companies.outloads.items[0].commodityId', null);
  const commodity = find(state.master.commodities.items, (commodity) => {
    return commodity.id === commodityId;
  });

  let columns = [
    { key: 'date', header: 'Date', },
    { key: 'time', header: 'Time' },
    { key: 'contract.referenceNumber', header: 'Contract No' },
    { key: 'referenceNumber', header: 'Movement No' },
    { key: 'freightProvider.name', header: 'Ft. Provider' },
    { key: 'truck.rego', header: getCountryLabel('rego') },
    { valueFunction: getDocketNumberValue, header: () => `${getCountryLabel('docket')} No`, fieldType: 'url-conditional-multi', urlKey: 'docketImageUrl' },
    { key: 'season', header: 'Season' },
    { key: 'ngr.ngrNumber', header: 'NGR' },
    { key: 'variety.name', header: 'Variety' },
    { default: getGradeName, header: 'Grade' },
    ...getCommoditySpecColumns(commodity),
    { key: 'netWeight', header: 'Tonnage' },
    { key: 'createdBy.name', header: 'User' },
  ];

  columns.splice(
    -2,
    0,
    { key: 'unshrunkTonnage', header: 'Unshrunk Tonnage', className: 'small' }
  );

  return {
    columns: columns,
    items: state.companies.outloads.items,
  };
};

export default connect(mapStateToProps)(GenericTable);
