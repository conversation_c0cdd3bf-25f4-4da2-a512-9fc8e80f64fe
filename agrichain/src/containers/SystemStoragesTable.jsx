import { connect } from 'react-redux';
import GenericTable from '../components/GenericTable';

const mapStateToProps = (state) => {
  let columns = [
    { key: 'location.name', header: 'Site' },
    { key: 'company.businessName', header: 'Operator'},
    { key: 'location.address.address', header: 'Location', map: {
        name: 'location.address.address',
        lat: 'location.address.latitude',
        lng: 'location.address.longitude'
      }}
  ];
  return {
    columns: columns,
    items: state.companies.systemStorages.items,
    noRecordFoundText : 'No System Storage Exists. Create your first System Storage.',
    scrollToTopOnUpdate: false,
    orderBy: 'location.name',
    order: 'asc',
  };
};

export default connect(
  mapStateToProps
)(GenericTable);
