import { connect } from 'react-redux';
import GenericTable from '../components/GenericTable';
import { clickEditNgrIcon } from '../actions/companies/ngrs';
import get from 'lodash/get';

const mapStateToProps = (state) => {
  return {
    columns: [
      { key: 'ngrNumber', header: 'NGR Number', },
      { key: 'ngrType', header: 'NGR Type' },
      { key: 'bankAccounts[0].accountDisplayName', header: 'Primary Shareholder' },
      { key: 'bankAccounts[0].shareholderPercent', header: '%' },
      { key: 'bankAccounts[1].accountDisplayName', header: 'Shareholder 2' },
      { key: 'bankAccounts[1].shareholderPercent', header: '%' },
      { urlKey: 'shareAuthorityFile', header: 'Share Authority Doc', fieldType: "url-conditional-multi", link: true,
      valueFunction: (item, column, index) => {return get(item, `shareAuthorityFile[${index}].name`);} },
    ],
    items: state.companies.ngrs.items,
    scrollToTopOnUpdate: false,
    orderBy: 'ngrNumber',
    order: 'asc',
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    handleDefaultCellClick: (item) => {
      dispatch(clickEditNgrIcon(item.id));
    },
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(GenericTable);
