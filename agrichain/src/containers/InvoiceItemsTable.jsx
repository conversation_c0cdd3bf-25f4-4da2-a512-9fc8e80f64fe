import { connect } from 'react-redux';
import GenericTable from '../components/GenericTable';
import {COMMODITIES} from "../common/constants";

const mapStateToProps = (state) => {
  var canola_fields = {};
  state.companies.contracts.titleTransfers.forEach((t) => {
    if(t.commodityContract.commodityId == COMMODITIES.CANOLA){
      canola_fields = [
        { key: 'coil', header: 'COIL', className: 'xsmall' },
        { key: 'impu', header: 'IMPU', className: 'xsmall' }
      ];
    } else {
      canola_fields = {};
    }
  });
  return {
    columns: [
      { key: 'identifier', header: 'Title Transfer No.', className: 'small'},
      { key: 'bhcSite.company.name', header: 'BHC Operator', className: 'small' },
      { key: 'bhcSite.name', header: 'Site', className: 'small' },
      { key: 'processOn', header: 'Transfer Date', className: 'small' },
      { key: 'grade.name', header: 'Grade', className: 'xsmall' },
      { key: 'tonnage', header: 'Tonnage', className: 'xsmall' },
      ...canola_fields,
      { key: 'sellerNgr.ngrNumber', header: 'Seller NGR', className: 'xsmall' },
      { key: 'buyerNgr.ngrNumber', header: 'Buyer NGR', className: 'xsmall' },
      { key: 'referenceNumber', header: 'Reference No.', className: 'xsmall' },
      { key: 'status', header: 'Status', className: 'xsmall capitalize' },
    ],
    items: state.companies.contracts.titleTransfers,
    scrollToTopOnUpdate: false,
    handleDefaultCellClick: true,
  };
};

export default connect(
  mapStateToProps
)(GenericTable);
