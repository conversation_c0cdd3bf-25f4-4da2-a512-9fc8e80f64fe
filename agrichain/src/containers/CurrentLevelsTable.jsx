import { connect } from 'react-redux';
import GenericTable from '../components/GenericTable';
import find from 'lodash/find';
import get from 'lodash/get';
import times from "lodash/times";
import isString from "lodash/isString";

const mapStateToProps = (state) => {
  const commodityId = get(state, 'companies.storageLevels.items[0].commodityId', null);
  const commodity = find(state.master.commodities.items, (commodity) => {
    return commodity.id === commodityId;
  });

  const specColumns = commodity && commodity.specs ?
      times(commodity.specs.length, index => {
        const spec = find(commodity.specs, { order: index + 1 });
        const code = get(spec, 'code','');
        const name = get(spec, 'name');
        const unit = isString(name) ? name.substring(name.indexOf('(') + 1, name.indexOf(')')) : null;
        var header = 'SP' + index;
        if(unit && name){
            header = `${code} (${unit})`;
        } else if(name){
            header = name;
        }
        const key = isString(code) ? `specs.${code.toLowerCase()}` : '';
        return { key: key, header: header };
      }) : {};

  return {
    columns: [
      { key: 'season', header: 'Season' },
      { key: 'ngrNumber', header: 'NGR' },
      { key: 'varietyName', header: 'Variety' },
      { key: 'gradeName', header: 'Grade' },
      ...specColumns,
      { key: 'tonnage', header: 'Tonnage' },
    ],
    items: state.companies.storageLevels.items,
  };
};

export default connect(
  mapStateToProps,
  null
)(GenericTable);
