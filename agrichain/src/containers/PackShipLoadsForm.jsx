import React from 'react';
import moment from 'moment';
import alertifyjs from 'alertifyjs';
import queryString from 'query-string';

import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import {
  get, set, compact, cloneDeep, omit, isEmpty, forEach, mapValues, some,
  find, isNumber, includes, map, merge, isEqual, isObject, isString, uniq,
  has, orderBy, filter, isNull, min, defaultTo, debounce
} from 'lodash';
import { InputAdornment, FormControlLabel, Checkbox, Dialog, DialogContent, Button, DialogActions, Autocomplete, TextField } from '@mui/material/';
import APIService from '../services/APIService';
import { getCommodities } from '../actions/api/commodities';
import { required, selected, valueBetween, valueAbove, valueBelow, truckRegoRegex } from '../common/validators';
import {
  FIELD,
  REQUIRED_FIELD,
  DATE_DB_FORMAT,
  TIME_DB_FORMAT,
  COMMODITIES,
  INSPECTION_ORIGIN_ID,
  WEIGHT_ORIGIN_ID,
  WEIGHT_DESTINATION_ID,
  DEFAULT_WEIGHT_ID,
  NGR_REQUIRED_FOR_STOCKS_WARNING_MESSAGE,
  FREIGHT_CONTRACT_TYPE,
  PACK_ORDER_TYPE_ID,
  OPTION_TYPE_WEB_SPLIT_LOADS,
  STORAGE_STOCK_EMPTY_UPDATE_OPTION_TYPES,
  UNIT_ABBREVIATIONS,
  LOAD_CREATE_OR_EDIT_ERROR_MESSAGE,
  FERTILISER_IDS,
  SEASON_NA
} from '../common/constants';
import {
  getSiteName, getDateTimeInUTC, getDateTimeFromUTC, isSystemCompany, getLoadWarningTableData, getCountryLabel, getCountryConfig, isCompanyGrower, getDefaultTruckTrailerUnit, getCountryDisplayUnit, currentUserCompany
} from '../common/utils';
import CommonDatePicker from '../components/common/CommonDatePicker';
import CommonTimePicker from '../components/common/CommonTimePicker';
import CommodityAutoComplete from '../components/common/autocomplete/CommodityAutoComplete';
import VarietyAutoComplete from '../components/common/autocomplete/VarietyAutoComplete';
import SeasonSelect from '../components/common/select/SeasonSelect';
import CommonSelect from '../components/common/select/CommonSelect';
import GradeAutoComplete from '../components/common/autocomplete/GradeAutoComplete';
import SpecParametersValue from '../components/common/SpecParametersValue';
import { positiveDecimalFilter } from '../common/input-filters';
import CommonButton from '../components/common/CommonButton';
import CommonTextField from '../components/common/CommonTextField';
import CommonAutoSelect from '../components/common/autocomplete/CommonAutoSelect';
import CommonSelectInput from '../components/common/CommonSelectInput';
import { validateRego, isLoading, forceStopLoader } from '../actions/main';
import { createTruck } from '../actions/api/trucks';
import { createPackShipLoads, updatePackShipLoads } from '../actions/api/outloads'
import { addTruck, emptyCreatedTruck } from '../actions/companies/trucks';
import { getStoredCommodityDetails } from '../actions/api/storages';
import CompanyAutocomplete from '../components/common/autocomplete/CompanyAutocomplete';
import SiteAsyncAutocomplete from '../components/common/autocomplete/SiteAsyncAutocomplete';
import { DialogTitleWithCloseIcon } from '../components/common/DialogTitleWithCloseIcon';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import { Cancel as RemoveIcon } from '@mui/icons-material'
import AddButton from '../components/common/AddButton';
import NumberField from '../components/common/NumberField';
import uniqBy from 'lodash/uniqBy';

const validatePickupDateTime = (pickupDate, pickupTime, inloadDateTime) => {
  if (!inloadDateTime) return { dateError: null, timeError: null };

  const localDateTime = getDateTimeFromUTC(inloadDateTime);
  let dateError = null;
  let timeError = null;

  if (pickupDate) {
    const pickupMoment = moment(pickupDate);
    const inloadMoment = moment(localDateTime.date);

    if (pickupMoment.isAfter(inloadMoment))
      dateError = "Pickup Date can't be after the Delivery Date.";

    else if (pickupMoment.isSame(inloadMoment, 'day') && pickupTime) {
      const pickupDateTime = moment(`${pickupDate} ${pickupTime}`).startOf('minute');
      const inloadDateTime = moment(`${localDateTime.date} ${localDateTime.time}`).startOf('minute');
      if (pickupDateTime.isAfter(inloadDateTime))
        timeError = "Pickup Time can't be after the Delivery Time";
    }
  }

  return { dateError, timeError };
};


class PackShipLoadsForm extends React.Component {
  constructor(props) {
    super(props);

    const queryParams = queryString.parse(this.props.location.search);
    const storageType = get(props.movement, 'freightPickup.consignor.sites[0].location.storageType');
    const isConsignorSiteBHC = get(props.movement, 'freightPickup.consignor.sites[0].isBhc');
    this.outload = get(props.movement.freightPickup, 'load.0')
    this.inload = get(props.movement.freightDelivery, 'load.0')
    const load = this.outload;
    this.farmId = get(this.props.match, 'params.farm_id');
    if (this.farmId)
      this.farmId = parseInt(this.farmId);
    this.storageId = get(this.props.match, 'params.storage_id');
    if (this.storageId)
      this.storageId = parseInt(this.storageId);
    const season = (queryString.season && queryString.season.toLowerCase() !== 'multiple') ? queryString.season : '';
    const isPackMovement = get(props.movement, 'typeId') === PACK_ORDER_TYPE_ID;
    this.countryTonnageLabel = getCountryLabel('tonnage');
    this.currentCompany = currentUserCompany();
    this.isCustomisedPackAndShip = this.currentCompany?.customisedPackAndShip
    this.state = {
      unit: getDefaultTruckTrailerUnit(),
      availableTonnageCache: {},
      isLoadingAvailableTonnage: false,
      submitting: false,
      existingTruckConfig: {},
      providerTrucks: [],
      outload: load,
      selectedStockOwner: null,
      stockOwner: {
        value: '',
        errors: [],
        validators: [required()]
      },
      isFetchingOrders: false,
      packAndShip: this.isCustomisedPackAndShip,
      selectedTrucks: [],
      companyNgrs: {},
      selectedNgr: undefined,
      sellerAvailableTonnage: undefined,
      sellerAvailableTonnageMessage: undefined,
      isVarietyMandatoryLabel: false,
      isDirectLoad: false,
      isStockUpdateLoad: false,
      showAmendTonnagePopup: false,
      isSubmit: false,
      selectedConsignee: null,
      lockStockOwner: false,
      chemicalApplicationRates: [],
      models: {
        comment: cloneDeep(FIELD),
        date: set(cloneDeep(REQUIRED_FIELD), 'value', moment().format(DATE_DB_FORMAT)),
        time: set(cloneDeep(REQUIRED_FIELD), 'value', moment().format(TIME_DB_FORMAT)),
        truckId: cloneDeep(FIELD),
        commodityId: set(cloneDeep(REQUIRED_FIELD), 'value', parseInt(queryParams.commodityId) || ''),
        varietyId: set(cloneDeep(FIELD), 'validators', [selected()]),
        season: set(cloneDeep(REQUIRED_FIELD), 'value', season),
        ngrId: {
          value: queryParams.ngrId || '',
          validators: [required()],
          errors: [],
        },
        tareWeight: cloneDeep(FIELD),
        grossWeight: cloneDeep(FIELD),
        netWeight: cloneDeep(FIELD),
        numberOfBags: this.currentCompany?.impexDocsEnabled ? cloneDeep(REQUIRED_FIELD) : cloneDeep(FIELD),
        farmFieldId: cloneDeep(FIELD),
        gradeId: {
          ...FIELD,
          value: parseInt(queryParams.gradeId) || '',
          validators: [required(), selected()]
        },
        storageId: set(props.movement && !load && storageType !== 'system' && !isConsignorSiteBHC ? cloneDeep(REQUIRED_FIELD) : cloneDeep(FIELD), 'value', undefined),
        farmId: cloneDeep(FIELD),
        estimatedNetWeight: {
          value: undefined,
          validators: this.isCustomisedPackAndShip ? [] : [required(), valueAbove(0.01)],
          errors: [],
        },
        consignor: {
          handlerId: {
            value: undefined,
            validators: [required()],
            errors: [],
          },
        },
        consignee: {
          handlerId: {
            value: undefined,
            validators: [required()],
            errors: [],
          },
        },
        quantity: {
          value: undefined,
          validators: [],
          errors: [],
        },
        containerNumber: {
          value: undefined,
          validators: [],
          errors: [],
        },
        containerTare: cloneDeep(FIELD),
        containerGross: cloneDeep(FIELD),
        sealNumbers: {
          value: isPackMovement ? get(this.props, 'movement.order.freightContainer.dualSeal') || get(this.props, 'movement.sealNumbers[1]') ? [{
            id: '0',
            value: get(this.props, 'movement.sealNumbers[0]')
          }, {
            id: '1',
            value: get(this.props, 'movement.sealNumbers[1]')
          }] : [{
            id: '0',
            value: get(this.props, 'movement.sealNumbers[0]')
          }] : [],
          validators: [],
          errors: [],
        },
        netCargo: cloneDeep(FIELD),
        specs: {},
      },
      matchingPackOrders: [],
      matchingPackOrderId: {
        value: null,
        validators: [],
        errors: []
      },
      trucks: [],
      commoditySpecs: [],
      gradeSpecs: [],
      ngrs: [],
      sites: [],
      queryParams,
      storage: null,
      selectedSite: null,
      gradeFloatingLabelText: 'Grade',
      ngrFloatingLabelText: 'NGR',
      seasonFloatingLabelText: 'Season',
      isNetWeightGreaterThanCurrentTonnage: false,
      isNetWeightValid: true,
      isFetchingBHC: false,
      isBHCFetched: false,
      initialStorageId: '',
      siteName:
        (props.movement && load) || (props.movement && storageType === 'system') || (props.movement && isConsignorSiteBHC)
          ? getSiteName(get(props.movement, 'freightPickup.consignor'), load)
          : null,
      showEstimatedNetWeightField: true,
      selectedConsignor: null,
      initialEstimatedNetWeight: 0,
      truckErrors: {},
      amendParentTonnage: false,
      chemicalApplications: [],
      inputText: null
    };
    this.handleTareWeightChange = this.handleTareWeightChange.bind(this);
    this.handleGrossWeightChange = this.handleGrossWeightChange.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
    this.handleSealChange = this.handleSealChange.bind(this);
    this.handleFieldChange = this.handleFieldChange.bind(this);
    this.handleSelectFieldChange = this.handleSelectFieldChange.bind(this);
    this.handleCommodityChange = this.handleCommodityChange.bind(this);
    this.handleVarietyChange = this.handleVarietyChange.bind(this);
    this.handleGradeChange = this.handleGradeChange.bind(this);
    this.handleSpecsChange = this.handleSpecsChange.bind(this);
    this.handleSiteChange = this.handleSiteChange.bind(this);
    this.handleQuantityFieldChange = this.handleQuantityFieldChange.bind(this);
    this.handleNumberOfBagsFieldChange = this.handleNumberOfBagsFieldChange.bind(this);
    this.getTonnageFieldsStates = this.getTonnageFieldsStates.bind(this);
    this.getSites = this.getSites.bind(this);
    this.checkNetWeightAgainstCurrentTonnage = this.checkNetWeightAgainstCurrentTonnage.bind(this);
    this.setFieldWarnings = this.setFieldWarnings.bind(this);
    this.getFieldWarnings = this.getFieldWarnings.bind(this);
    this.setFieldValue = this.setFieldValue.bind(this);
    this.handleIdentifierChange = this.handleIdentifierChange.bind(this);
    this.setFieldErrors = this.setFieldErrors.bind(this);
    this.setAllFieldsErrors = this.setAllFieldsErrors.bind(this);
    this.getNgrs = this.getNgrs.bind(this);
    this.getStorage = this.getStorage.bind(this);
    this.updateValidatorsBasedOnStorage = this.updateValidatorsBasedOnStorage.bind(this);
    this.isNetWeightMoreThanAllowed = this.isNetWeightMoreThanAllowed.bind(this);
    this.getTonnage = this.getTonnage.bind(this);
    this.voidLoad = this.voidLoad.bind(this);
    this.setOwnerAndNgrForCustomerOnlyMovements = this.setOwnerAndNgrForCustomerOnlyMovements.bind(this);
    this.checkStockUpdateLoad = this.checkStockUpdateLoad.bind(this)
    this.handleContainerTareChange = this.handleContainerTareChange.bind(this);
    this.handleContainerGrossChange = this.handleContainerGrossChange.bind(this)
    this.handleTruckDetailsSelectValueChange = this.handleTruckDetailsSelectValueChange.bind(this);
    this.validateAndSetDateTime = this.validateAndSetDateTime.bind(this);
    this.fetchMatchingPackOrders = this.fetchMatchingPackOrders.bind(this);
    this.setMatchingPackOrder = this.setMatchingPackOrder.bind(this);
    this.validateMatchingPackOrderSelection = this.validateMatchingPackOrderSelection.bind(this);
    this.getTonnageFieldsStates();
  }

  validateAndSetDateTime = (value, elementId) => {
    const inloadDateTime = get(this.props.movement, 'freightDelivery.load[0].dateTime');
    if (!inloadDateTime) return true;

    const newState = { ...this.state };
    const currentDate = get(this.state, 'models.date.value');
    const currentTime = get(this.state, 'models.time.value');

    const dateToValidate = elementId === 'date' ? value : currentDate;
    const timeToValidate = elementId === 'time' ? value : currentTime;

    const { dateError, timeError } = validatePickupDateTime(
      dateToValidate,
      timeToValidate,
      inloadDateTime
    );

    newState.models.date.errors = [];
    newState.models.time.errors = [];

    let hasError = false;

    if (dateError) {
      newState.models.date.errors = [dateError];
      hasError = true;
    }

    if (timeError) {
      newState.models.time.errors = [timeError];
      hasError = true;
    }

    if (elementId === 'date')
      newState.models.date.value = dateToValidate;
    else if (elementId === 'time')
      newState.models.time.value = value;

    if (hasError) {
      this.setState(newState);
      return false;
    }

    return true;
  };

  componentDidMount() {
    this.setStockOwnerFromLoadOrQueryParams()

    if (this.props.movement && this.hasQuantityBasedCommodity(this.props.movement.commodityId)) {
      const newState = { ...this.state };
      newState.models.quantity.validators = [required()];
      this.setState(newState);
    }
    if (get(this.props.movement, 'status') === 'manual_contract_complete_balanced') {
      const newState = { ...this.state };
      newState.models.truckId.validators = [];
      newState.models.freightProviderId.validators = [];
      this.setState(newState);
    }
    if (!this.props.movement && !get(this.outload, 'truckId')) {
      const newState = { ...this.state };
      newState.models.truckId.validators = [];
      newState.models.freightProviderId.validators = [];
      this.setState(newState);
    }
    if (isEmpty(this.props.commodities)) {
      this.props.getCommodities();
    }

    const commodityId = get(this.state, 'models.commodityId.value');
    if (commodityId) {
      this.handleCommodityChange(commodityId, 'commodityId');
    }
    const useConversions = getCountryConfig()?.showConversions
    if (this.outload || this.inload) {
      const newState = { ...this.state };

      this.props.isLoading();
      newState.models.truckId.value = get(this.outload, 'truckId') || get(this.inload, 'truckId');
      Object.keys(this.state.models).forEach(key => {
        if (!includes(['sealNumbers', 'tareWeight', 'netWeight', 'grossWeight'], key))
          newState.models[key].value = get(this.outload, key) || get(this.inload, key);
      });
      const isPackMovement = this.isPackMovement();
      const hasContainerMovement = isPackMovement && get(this.props.movement, 'containerMovement')
      if (isPackMovement && hasContainerMovement) {
        const containerMovement = this.props.movement?.containerMovement
        const inload = get(containerMovement?.freightDelivery?.load, '0')
        newState.packAndShip = true
        newState.models.truckId.value = inload?.truckId
        newState.models.containerNumber.value = this.props.movement?.containerNumber
        newState.models.netWeight.value = get(inload, 'splitWeights.truckNetWeight')
        newState.models.tareWeight.value =  get(inload, 'splitWeights.truckTare')
        newState.models.grossWeight.value = get(inload, 'splitWeights.truckGross')
        newState.models.netCargo.value = this.isCustomisedPackAndShip ? get(inload, 'splitWeights.netCargo') : null
        newState.models.consignee.handlerId.value = inload?.farmId
        newState.selectedConsignee = inload?.handler
        newState.trucks = uniqBy(compact([...newState.trucks, inload?.truck]), 'id')
        this.setSelectedProviderTruck(inload?.truck)
      }
      newState.models.containerTare.value = get(this.inload, 'splitWeights.containerTare') || this.inload?.tareWeight
      newState.models.containerGross.value = get(this.inload, 'splitWeights.containerGross') ||this.inload?.grossWeight
      newState.models.estimatedNetWeight.value = this.inload?.estimatedNetWeight
      newState.models.containerNumber.value = this.props.movement?.containerNumber

      if (this.outload?.dateTime) {
        const localDateTime = getDateTimeFromUTC(this.outload.dateTime);
        newState.models.date.value = localDateTime.date;
        newState.models.time.value = localDateTime.time;
      }
      if (this.outload?.ngrId) {
        newState.models.ngrId.value = get(this.outload, 'ngrId')
        newState.stockOwner.value = get(this.outload, 'ngr.companyId') || this.outload.ngrCompanyId
      }
      newState.models.estimatedNetWeight.value = (useConversions ? this.inload.netWeightLb || this.inload?.weightsInLB?.netWeightLb : this.inload.tonnage || this.inload.estimatedNetWeight) || this.inload.tonnage;

      if (useConversions) {
        const tareWeight = this.inload?.tareWeightLb || this.inload?.weightsInLB?.tareWeightLb
        const grossWeight = this.inload?.grossWeightLb || this.inload?.weightsInLB?.grossWeightLb
        newState.models.containerTare.value = tareWeight ? parseFloat(tareWeight).toFixed(2) : null;
        newState.models.containerGross.value = grossWeight ? parseFloat(grossWeight).toFixed(2) : null;
      }
      if (!newState.models.estimatedNetWeight.value) {
        const netWeight = this.getNetWeight(newState)
        newState.models.estimatedNetWeight.value = netWeight == 0.0 ? null : netWeight;
      }
      newState.initialEstimatedNetWeight = cloneDeep(newState.models.estimatedNetWeight.value);
      this.setState(newState, () => {
        if (isPackMovement && hasContainerMovement) {
          const containerMovement = this.props.movement?.containerMovement
          const inload = get(containerMovement?.freightDelivery?.load, '0')
          if (this.isCustomisedPackAndShip)
            APIService.trucks().appendToUrl(`?empty_container=true&include_truck_id=${inload?.truckId}`).get().then(trucks => this.setState({trucks}))
        }
        this.checkNetWeightAgainstCurrentTonnage();
        this.fetchChemicalApplications();
      });
    } else if (this.props.movement) {
      const { movement } = this.props;
      const newState = { ...this.state };

      if (this.isNGRFieldDisabled()) {
        newState.models.ngrId.value = get(movement, 'commodityContract.seller.ngrId');
        newState.selectedNgr = get(this.props.movement, 'commodityContract.seller.ngr')
      }
      newState.models.commodityId.value = movement.commodityId;
      newState.models.varietyId.value = movement.varietyId;
      newState.models.season.value = movement.season;
      newState.models.containerNumber.value = movement?.containerNumber;
      newState.models.containerTare.value = movement?.containerTare;
      newState.models.storageId.value = get(movement, 'freightPickup.consignor.sites[0].locationId')
      newState.models.gradeId.value = get(movement, 'plannedGradeId') || get(movement, 'commodityContract.gradeId');
      if (!newState.models.estimatedNetWeight.value) {
        const newtWeight = this.getNetWeight(newState)
        newState.models.estimatedNetWeight.value = newtWeight == 0.0 ? null : newtWeight;
      }
      newState.initialEstimatedNetWeight = cloneDeep(newState.models.estimatedNetWeight.value);

      const plannedFreightPickupDate = (
        get(this.outload, 'dateTime') ||
        get(this.props.movement, 'freightPickup.dateTime') ||
        get(this.props.movement, 'freightDelivery.load[0].dateTime') ||
        get(this.props.movement, 'freightDelivery.dateTime')
      )

      if (plannedFreightPickupDate && new Date(plannedFreightPickupDate) <= new Date()) {
        const localDateTime = getDateTimeFromUTC(plannedFreightPickupDate);
        newState.models.date.value = localDateTime?.date
        newState.models.time.value = localDateTime?.time
      }
      this.setState(newState, () => {
        if(this.isCustomisedPackAndShip)
          APIService.trucks().appendToUrl('?empty_container=true').get().then(trucks => this.setState({trucks}))
        if (get(this.props.movement, 'isBlended'))
          this.onChemicalApplicationAdd();
        this.setGradeSpecs(this.state.models.gradeId.value);
      });
    }

    if (this.outload && this.props.movement) {
      if (get(this.outload, 'dateTime')) {
        const newState = { ...this.state };
        const localDateTime = getDateTimeFromUTC(this.outload.dateTime);
        newState.models.date.value = localDateTime.date;
        newState.models.time.value = localDateTime.time;
        this.setState(newState);
      }
    }

    if (get(this.props, 'movement.typeId') === PACK_ORDER_TYPE_ID) {
      const { order } = this.props.movement;
      const newState = { ...this.state };
      const siteId = get(order, 'freightContainer.consignor.handlerId');
      const storageId = get(order, 'freightContainer.consignor.sites.0.locationId') || get(this.props.movement, 'freightPickup.consignor.sites.0.locationId')
      newState.models.consignor.handlerId.value = siteId;
      if (get(this.props, 'optionType') !== OPTION_TYPE_WEB_SPLIT_LOADS)
        newState.models.storageId.value = storageId;
      newState.selectedConsignor = get(order, 'freightContainer.consignor.handler') || get(this.props.movement, 'freightPickup.consignor.handler');
      newState.selectedSite = get(order, 'freightContainer.consignor.sites.0.location') || get(this.props.movement, 'freightPickup.consignor.sites.0.location');
      if (get(newState.selectedConsignor, 'id'))
        this.getConsignorSites(get(newState.selectedConsignor, 'id'));
      this.setState(newState);
    }

    if (has(this.state.outload, 'netWeight')) {
      const newState = { ...this.state };
      newState.models.estimatedNetWeight.value = useConversions ? this.state.outload.netWeightLb : this.state.outload.netWeight;
      this.setState(newState);
    }
    const isContainerMovement = this.isContainerMovement();
    const isPackMovement = this.isPackMovement();
    if (isContainerMovement || isPackMovement) {
      let containerTare = get(this.inload, 'tareWeight') || get(this.inload, 'splitWeights.containerTare')
      let containerGross = get(this.inload, 'grossWeight') || get(this.inload, 'splitWeights.containerGross')
      const newState = { ...this.state };
      newState.models.estimatedNetWeight.value = get(this.inload, 'estimatedNetWeight') || get(this.props, 'movement.plannedTonnage');
      newState.models.containerTare.value = this.inload ? (containerTare != 0 ? containerTare : null) : get(this.props, 'movement.containerTare');
      newState.models.containerGross.value = this.inload ? (containerGross !=0 ? containerGross : null) : null;
      newState.models.ngrId.value = get(this.props, 'movement.customer.ngrId');
      if (isPackMovement) {
        if (get(this.props, 'movement.sealNumbers[1]') || get(this.props, 'movement.order.freightContainer.dualSeal'))
          newState.models.sealNumbers.value = [{
            id: '0',
            value: get(this.props, 'movement.sealNumbers[0]')
          }, {
            id: '1',
            value: get(this.props, 'movement.sealNumbers[1]')
          }]
        else
          newState.models.sealNumbers.value = [{
            id: '0',
            value: get(this.props, 'movement.sealNumbers[0]')
          }]
      }
      this.setState(newState);
    }

    this.getStorage();
    this.getNgrs();
    this.getSites(true);
    this.setHandler();
    this.setGradeSpecs(this.state.models.gradeId.value);
    this.checkStockUpdateLoad();
    this.fetchAndSetStockOwner();

    if(this.props.movement?.id && this.isCustomisedPackAndShip)
      this.fetchMatchingPackOrders();

    const outloadDate = get(this.state, 'models.date.value');
    const outloadTime = get(this.state, 'models.time.value');
    const inloadDateTime = get(this.props.movement, 'freightDelivery.load[0].dateTime');

    if (outloadDate || outloadTime) {
      const { dateError, timeError } = validatePickupDateTime(
        outloadDate,
        outloadTime,
        inloadDateTime
      );

      if (dateError || timeError) {
        const currentOutload = this.state.models;
        const updatedOutload = {
          ...currentOutload,
          date: { ...currentOutload.date, errors: dateError ? [dateError] : currentOutload.date.errors },
          time: { ...currentOutload.time, errors: timeError ? [timeError] : currentOutload.time.errors }
        };

        this.setState({
          models: {
            ...this.state.models,
            updatedOutload
          }
        });
      }
    }
    if (get(this.props.movement, 'isBlended'))
      this.fetchChemicalApplicationRates();
  }

  fetchMatchingPackOrders = debounce((season=null, varietyId=null) => {
    if (get(this.props.movement, 'id')) {
      this.setState({ isFetchingOrders: true }, () => {
        let fetchURL = `minimal/pack-movement-creatable/?entity_id=${this.props.movement.id}&commodity_id=${this.props.movement?.commodityId}`;
        if (season) fetchURL += `&season=${season}`;
        if (varietyId) fetchURL += `&variety_id=${varietyId}`;
    
        APIService.freights().orders().appendToUrl(fetchURL).get().then(data => {
          const matchingPackOrderId = data.find(order => order.id == this.state.matchingPackOrderId.value)?.id;
          this.setState({
            matchingPackOrders: data,
            matchingPackOrderId: { ...this.state.matchingPackOrderId, value: matchingPackOrderId },
            isFetchingOrders: isEmpty(data)
          })
        });
          
      });
    
    }
  }, 500);

  getTrucks(companyId, callback) {
    const providerId = companyId;
    const containerMovement = this.props.movement?.containerMovement
    if (containerMovement && providerId) {
      let url = APIService.companies(providerId).trucks()
      const inload = get(containerMovement?.freightDelivery?.load, '0')
      let truckId = inload?.truckId;
      if (truckId)
        url.appendToUrl(`?include_truck_id=${truckId}`)
      url.get(this.props.token, null, {minimal: true})
      .then(trucks => {
          const newState = {...this.state};
          newState.trucks = trucks;
          if (!this.state.models.truckId.value)
            newState.models.truckId.value = truckId;
          this.setState(newState);
          if (callback) callback();
        });
    }
  }

  setMatchingPackOrder = (orderId) => {
    const matchingPackOrder = find(this.state.matchingPackOrders, {id: orderId})
    const season = get(matchingPackOrder, 'season')
    const ngrId = get(matchingPackOrder, 'customer.ngrId')
    const customer = get(matchingPackOrder, 'customer')
    this.setState({
      matchingPackOrderId: {
        ...this.state.matchingPackOrderId,
        value: orderId,
        errors: []
      },
      stockOwner: {
        ...this.state.stockOwner,
        value: customer?.companyId
      },
      selectedStockOwner: {
        abn: customer?.abn,
        id: customer?.companyId,
        name: customer?.companyName,
        transactionParticipation: customer?.transactionParticipation,
        typeId: customer?.typeId
      },
      models: {
        ...this.state.models,
        season: {
          ...this.state.models.season,
          value: season
        },
        ngrId: {
          ...this.state.models.ngrId,
          value: ngrId
        },
        commodityId: {
          ...this.state.models.commodityId,
          value: matchingPackOrder?.commodityId
        },
        gradeId: {
          ...this.state.models.gradeId,
          value: matchingPackOrder?.plannedGradeId
        }
      }
    });
  }

  validateMatchingPackOrderSelection = () => {
    if(this.isCustomisedPackAndShip && !get(this.props, 'movement.order') && !this.state.matchingPackOrderId.value) {
      this.setState({
        matchingPackOrderId: {
          ...this.state.matchingPackOrderId,
          errors: ['This field is required']
        }
      })
      return false
    }
    return true
  }

  fetchChemicalApplicationRates() {
    const companyId = get(this.props.movement, 'commodityContract.seller.companyId');
    if (companyId) {
      APIService.companies(companyId)
        .appendToUrl('application-rates/')
        .get()
        .then(resp => this.setState({ chemicalApplicationRates: resp }))
    }
  }

  fetchChemicalApplications() {
    if (get(this.props.movement, 'isBlended') && this.outload?.id) {
      APIService.loads(this.outload.id)
        .appendToUrl('chemical-applications/')
        .get()
        .then(response => {
          if (response && !isEmpty(response)) {
            const newState = { ...this.state };
            const movementChemicalApplications = (isEmpty(get(this.props.movement, 'chemicalApplications')) ? get(this.props.movement, 'orderChemicalApplications') : get(this.props.movement, 'chemicalApplications')) || [];
            forEach(response, (chemicalApplication, index) => {
              newState.chemicalApplications.push({
                id: index + 1,
                commodityId: chemicalApplication.commodityId,
                storageId: chemicalApplication.storageId,
                applicationRate: chemicalApplication.applicationFee,
                commodityIdOptions: movementChemicalApplications.map(_chemicalApplication => _chemicalApplication.commodityId),
                commodity: find(this.props.commodities, { id: chemicalApplication.commodityId }),
                chemicalLoadId: chemicalApplication.chemicalLoadId,
                errors: []
              })
            })
            this.setState(newState);
          }
        })
    }
  }

  handleSealChange(event) {
    const newState = { ...this.state };
    newState.models.sealNumbers.value.find(sealNumber => sealNumber.id === event.target.id).value = event.target.value;
    this.setState(newState);
  }

  isContainerMovement() {
    let { movement } = this.props;
    if (movement) {
      return (get(movement, 'typeId') !== PACK_ORDER_TYPE_ID && !includes([null, undefined], get(movement, 'containerNumber')));
    }
  }
  isPackMovement = () => get(this.props, 'movement.typeId') === PACK_ORDER_TYPE_ID;

  fetchAndSetStockOwner() {
    if (this.props.movement) {
      APIService.freights().contracts().appendToUrl('outloads/stock-owners/').post({ movement_id: this.props.movement.id }).then(response => {
        let lockStockOwner = Boolean(response?.outload && this.props.movement.commodityContractId)
        const newState = { ...this.state }
        newState.stockOwner.value = response.outload
        newState.lockStockOwner = lockStockOwner
        newState.models.ngrId.value = this.isNGRFieldDisabled() ? get(this.props.movement, 'commodityContract.seller.ngrId') : get(this.props.movement, 'customer.ngrId');
        newState.selectedNgr = this.isNGRFieldDisabled() ? get(this.props.movement, 'commodityContract.seller.ngr') : get(this.props.movement, 'customer.ngr')
        this.setState(newState)
      })
    }
  }

  checkShrinkageLoad() {
    return this.outload && get(this.outload, 'shrinkageParent')
  }

  checkStockUpdateLoad() {
    if (includes(STORAGE_STOCK_EMPTY_UPDATE_OPTION_TYPES, get(this.outload, 'optionType')))
      this.setState({ isStockUpdateLoad: true });
  }

  setOwnerAndNgrForCustomerOnlyMovements() {
    if (this.props.movement && includes([FREIGHT_CONTRACT_TYPE.CUSTOMER_ONLY, PACK_ORDER_TYPE_ID], this.props.movement.typeId) && this.props.isCreate
      && !get(this.state, 'models.ngrId.value') && get(this.state, 'stockOwner.value') == get(this.props.movement, 'customer.companyId')) {
      const { movement } = this.props;
      const newState = { ...this.state };
      set(newState.models, `ngrId.value`, get(movement, 'customer.ngrId'));
      set(newState, 'selectedNgr', get(movement, 'customer.ngr'));
      this.setState(newState);
    }
    else if (this.props.movement && this.props.movement.typeId === PACK_ORDER_TYPE_ID && this.props.isCreate && !get(this.state, 'models.ngrId.value') && get(this.props.movement, 'externalReferenceNumber')) {
      const { movement } = this.props;
      const newState = { ...this.state };
      set(newState.models, `ngrId.value`, get(movement, 'seller.ngrId'));
      set(newState, 'selectedNgr', get(movement, 'seller.ngr'));
      this.setState(newState);
    }
  }

  setStockOwnerFromLoadOrQueryParams() {
    if (!this.isNGRFieldDisabled()) {
      const newState = { ...this.state };
      if (this.outload) {
        newState.models.ngrId.value = this.outload.ngrId;
        const isSharedNgr = get(this.outload, 'ngr.ngrType') === 'shared';
        newState.stockOwner.value = get(this.state.outload, 'stockOwnerId') || (isSharedNgr ? newState.stockOwner.value : get(this.outload, 'ngr.companyId')) || get(this.outload, 'ownerId') || newState.stockOwner.value;
      } else if (this.state.queryParams.ngrId) {
        newState.models.ngrId.value = get(this.state, 'selectedNgr.id');
        newState.stockOwner.value = get(this.state, 'selectedNgr.companyId');
      }
      this.setState(newState);
    }
  }

  onCompanyChange = item => {
    const value = item?.id
    const newState = { ...this.state };
    newState.selectedStockOwner = item
    newState.stockOwner.value = value;
    const isSharedNgr = get(this.outload, 'ngr.ngrType') === 'shared';
    const ownerId = get(this.outload, 'stockOwnerId') || get(this.outload, 'ownerId') || (isSharedNgr ? this.state.stockOwner.value : get(this.outload, 'ngr.companyId'));
    if (ownerId && ownerId === value)
      newState.models.ngrId.value = this.outload?.ngrId;
    else if (value !== get(this.state, 'selectedNgr.companyId'))
      newState.models.ngrId.value = '';
    this.updateDependentValidatorsByStorage(newState);
    const cachedNgrs = get(this.state.companyNgrs, value);
    newState.ngrs = cachedNgrs?.length ? cachedNgrs : []
    this.setState(newState, () => {
      if (value) {
        if (isEmpty(cachedNgrs))
          APIService.companies(value).ngrs().appendToUrl('minimal/')
            .get()
            .then(ngrs => this.setState({
              companyNgrs: { ...this.state.companyNgrs, [value]: ngrs },
              ngrs: ngrs
            }, () => {
              this.setVarietyMandatory()
              this.setOwnerAndNgrForCustomerOnlyMovements()
              this.setStockOwnerMandatoryError()
            }));
      } else {
        this.setVarietyMandatory()
        this.setStockOwnerMandatoryError()
      }
    });
  };

  isVarietyMandatory = () => {
    const { selectedConsignor: selectedConsignor } = this.state
    let result = false
    if (selectedConsignor?.isVarietyMandatory) {
      const isMandatoryForGrowers = selectedConsignor.userTypeForVarietyMandatory.includes('growers')
      const isMandatoryForNonGrowers = selectedConsignor.userTypeForVarietyMandatory.includes('non_growers')
      let isVarietyMandatoryForLoad = ['inload_and_outload'].some(type => selectedConsignor.loadTypeForVarietyMandatory.includes(type))
      result = isMandatoryForGrowers && isMandatoryForNonGrowers && isVarietyMandatoryForLoad
      if (!result && isVarietyMandatoryForLoad && this.state.stockOwner.value) {
        const company = this.state.selectedStockOwner
        const isGrower = isCompanyGrower(company)
        result = isMandatoryForGrowers ? isGrower : !isGrower
      }
    }

    return result
  }

  setVarietyMandatory = () => {
    const isVarietyMandatory = this.isVarietyMandatory()
    if (isVarietyMandatory && !this.state.isVarietyMandatoryLabel) {
      const newState = { ...this.state }
      newState.models.varietyId.validators = [required(), selected()];
      newState.isVarietyMandatoryLabel = true;
      setTimeout(() => this.setState(newState), 100)
    } else if (!isVarietyMandatory && this.state.isVarietyMandatoryLabel) {
      const newState = { ...this.state }
      newState.models.varietyId.validators = [selected()];
      newState.models.varietyId.errors = [];
      newState.isVarietyMandatoryLabel = false;
      setTimeout(() => this.setState(newState), 100)
    }
  }

  setHandler = async () => {
    const self = this;
    const handler = get(self.outload, 'handler') || get(self.props.movement, 'freightPickup.consignor.handler');
    if (handler) {
      const newState = { ...self.state };
      await APIService.contracts().handlers().get(null, null, { handler_ids: handler.id }).then(res => newState.selectedConsignor = res?.handlers[0]);

      newState.models.consignor.handlerId.value = handler.id;
      newState.models.consignor.handlerId.errors = this.isRestrictedToEditForSite(handler) ? [LOAD_CREATE_OR_EDIT_ERROR_MESSAGE.replace('$action', 'created')] : [];
      self.setState(newState, () => {
        if (this.state.selectedConsignor?.id) {
          this.getConsignorSites(this.state.selectedConsignor.id)
          this.setVarietyMandatory()
        }
      });
    }
  }

  handleConsigneeChange = (item) => {
    const newState = { ...this.state };
    const value = item?.id
    newState.models.consignee.handlerId.value = value
    newState.models.consignee.handlerId.errors = this.isRestrictedToEditForSite(item) ? [LOAD_CREATE_OR_EDIT_ERROR_MESSAGE.replace('$action', 'created')] : [];
    newState.selectedConsignee = item ? item : null;
    this.setState(newState, () => {
      if (!isSystemCompany())
        this.setFieldErrors('models.consignee.handlerId');
    });
  };

  hasContainerMovement = () => this.isPackMovement() && get(this.props.movement, 'containerMovement')

  componentDidUpdate(prevProps) {
    if (!this.state.models.ngrId.value && this.isNGRFieldDisabled()) {
      const ngrId = this.getNGRId();
      if (ngrId) {
        const newState = { ...this.state };
        newState.models.ngrId.value = ngrId;
        this.setState(newState);
      }
    }
    if (this.isContainerMovement() && !this.state.selectedNgr) {
      this.setState({ selectedNgr: this.outload ? get(this.outload, 'ngr') : get(this.props, 'movement.customer.ngr') })
    }
    if (this.state.models.commodityId.value && !isEmpty(this.props.commodities) && isEmpty(this.state.commoditySpecs) && !isEmpty(this.getCommoditySpecs(this.state.models.commodityId.value)))
      this.handleCommodityChange(this.state.models.commodityId.value, 'commodityId');
    const isPackMovement = this.isPackMovement();
    const hasContainerMovement = isPackMovement && get(this.props.movement, 'containerMovement')
  
    if (hasContainerMovement && !this.state.packAndShip)
      this.setState({ packAndShip: true })
    if (this.props.createdTruck && !find(this.state.trucks, { id: this.props.createdTruck?.id })) {
      const newState = { ...this.state };
      newState.trucks = [...this.state.trucks, this.props.createdTruck]
      this.props.emptyCreatedTruck();
      this.setState(newState);
    }
    const owner = this.props.movement?.typeId == FREIGHT_CONTRACT_TYPE.SELLER_TO_BUYER ? get(this.props.movement, 'commodityContract.seller.company') : get(this.props.movement, 'customer.company')
    if (this.outload && isEmpty(this.state.selectedStockOwner) && isEqual(this.state.stockOwner.value, owner?.id))
      this.setState({ selectedStockOwner: owner })
    if (prevProps !== this.props) {
      this.checkStockUpdateLoad();
      this.setGradeSpecs(this.state.models.gradeId.value);
    }
  }

  getTonnageFieldsStates() {
    const newState = { ...this.state };
    if (this.props.movement) {
      newState.showEstimatedNetWeightField = false;
      newState.models.estimatedNetWeight.errors = [];
      if (this.props.movement.isAdHoc && !get(this.props.movement, 'freightPickup.consignor')) {
        newState.models.consignor.handlerId.validators = [required()];
      }
    } else {
      newState.showEstimatedNetWeightField = true;
      newState.models.estimatedNetWeight.errors = [];
    }
    this.changeFreightPickupTareWeightGrossWeightValidators(newState);
    this.changeFreightPickupGradeValidators(newState);
    this.setState(newState);
  }

  handlePackShipCheckboxClick = event => {
    const newState = { ...this.state };
    newState.packAndShip = event.target.checked;
    newState.models.netWeight.validators = event.target.checked ? [required(), valueAbove(0.01)] : []
    newState.models.truckId.validators = event.target.checked ? [required()] : []
    newState.models.netCargo.validators = event.target.checked && this.isCustomisedPackAndShip ? [required()] : []
    newState.models.estimatedNetWeight.validators = (newState.packAndShip && this.isCustomisedPackAndShip) ? [] : [required(), valueAbove(0.01)]
    this.setState(newState, () => {
      if (this.state.packAndShip && this.isCustomisedPackAndShip)
        APIService.trucks().appendToUrl('?empty_container=true').get().then(trucks => this.setState({trucks}))
    });
  }

  changeFreightPickupTareWeightGrossWeightValidators = newState => {
    const tareWeight = parseFloat(newState.models.tareWeight.value);
    const grossWeight = parseFloat(newState.models.grossWeight.value);
    if (this.props.movement) {
      if (
        !newState.models.tareWeight.value &&
        !newState.models.grossWeight.value &&
        get(newState, 'selectedSite.entity') === 'farm_field' &&
        (get(this.props.movement, 'commodityContract.weightId') !== WEIGHT_ORIGIN_ID ||
          !get(this.props.movement, 'commodityContractId') || !get(this.props.movement, 'order.commodityContractId'))
      ) {
        newState.models.tareWeight.validators = [valueAbove(0)];
        newState.models.grossWeight.validators = [valueAbove(0)];
      }
    }
    newState.models.tareWeight.validators = [isNaN(grossWeight) ? valueAbove(0) : valueAbove(0), valueBelow(grossWeight)];
    newState.models.grossWeight.validators = [isNaN(tareWeight) ? valueAbove(0) : valueAbove(tareWeight)];
    newState.models.tareWeight.errors = [];
    newState.models.grossWeight.errors = [];
  };

  changeFreightPickupGradeValidators = newState => {
    if (this.props.movement) {
      if (
        get(newState, 'selectedSite.entity') === 'farm_field' &&
        (get(this.props.movement, 'commodityContract.inspection.id') !== INSPECTION_ORIGIN_ID ||
          !get(this.props.movement, 'commodityContractId') || !get(this.props.movement, 'order.commodityContractId'))
      ) {
        newState.models.gradeId.errors = [];
      }
    } else {
      newState.models.gradeId.errors = [];
    }
  };

  confirmSubmit = async submitData => {
    const successCallback = () => {
      this.props.closeDrawer();
      if (!this.props.isCreate)
        localStorage.setItem('stocks', JSON.stringify({})); // stocks cache burst
      this.props.successCallback ? this.props.successCallback() : window.location.reload()
    };
    let datetimeString = this.outload ? this.outload.dateTime.replace("T", ' ').replace('Z', '') : '';
    datetimeString = datetimeString ? datetimeString.substring(0, 17) + '00' + datetimeString.substring(19) : '';
    if (this.outload && datetimeString != submitData.dateTime && get(this.outload, 'deviceSource.mobile')) {
      setTimeout(() => {
        alertifyjs.confirm(
          'Warning',
          'The load date and time details will be replaced with the data entered here and the date and time data saved from the mobile app will be lost. Please confirm this change.',
          () => {
            if (this.outload) {
              this.setState({ isSubmit: true })
              this.props.updatePackShipLoads(this.props.movement.id, submitData, successCallback);
              this.unlockSubmit(null, 2000)
            } else if (this.props.movement) {
              this.setState({ isSubmit: true })
              this.props.createPackShipLoads(submitData, successCallback);
              this.unlockSubmit(null, 2000)
            }
          },
          this.unlockSubmit,
        );
      }, 500)
    }
    else {
      if (this.outload) {
        this.setState({ isSubmit: true })
        this.props.updatePackShipLoads(this.props.movement.id, submitData, successCallback);
        this.unlockSubmit(null, 2000)
      } else if (this.props.movement) {
        this.setState({ isSubmit: true })
        this.props.createPackShipLoads(submitData, successCallback);
        this.unlockSubmit(null, 2000)
      }
    }
  };

  setStockOwnerMandatoryError() {
    let hasErrors = false;
    if (!this.isNGRFieldDisabled() && !isSystemCompany()) {
      let errors = [];
      if (!this.state.stockOwner.value) {
        errors = ['This field can not be blank'];
        hasErrors = true;
      }
      const newState = { ...this.state };
      newState.stockOwner.errors = errors;
      this.setState(newState);
    }
    return hasErrors;
  }

  getInitialEstimatedNetWeight() {
    if (this.outload && this.state.initialStorageId == this.state.models.storageId.value)
      return get(this.outload, 'netWeight', 0);

    if (this.props.movement && this.state.initialStorageId == this.state.models.storageId.value)
      return get(this.props.movement, 'freight_pickup.loads.0.netWeight', 0);

    return 0;
  }

  isRestrictedToEditForSite = site => get(site, 'externallySyncSource') || (get(site, 'stocksManagement') && this.props.userCompanyId != get(site, 'companyId') && !isSystemCompany());

  unlockSubmit = (errorMsg, timeout) => {
    setTimeout(() => {
      this.setState({ submitting: false }, () => {
        if (errorMsg)
          alertifyjs.error(errorMsg, 5);
      })
    }, timeout ? timeout : 10)
  }

  getInProgressAndDeliveredTonnage() {
    return get(this.props, 'movement.parentTotalDeliveredTonnage', 0) + get(this.props, 'movement.parentTotalProgressTonnage', 0);
  }

  getMaxAllowedTonnage(tonnageProperty) {
    const parentTonnage = min([get(this.props, 'movement.maxAllowedTonnageOnOrder'), get(this.props, 'movement.parentTotalTonnageWithTolerance')]);
    if (isNumber(parentTonnage) && isNumber(get(this.props, 'movement.parentTotalDeliveredTonnage'))) {
      let maxAllowedTonnage = parentTonnage - get(this.props, 'movement.parentTotalDeliveredTonnage', 0);
      if ((get(this.props, 'movement.status') === 'confirmed') || (get(this.props, 'movement.status') === 'in_progress'))
        maxAllowedTonnage = maxAllowedTonnage - get(this.props, 'movement.parentTotalProgressTonnage', 0)
      const weightId = get(this.props, 'movement.commodityContract.weightId', DEFAULT_WEIGHT_ID);
      const useConversions = getCountryConfig()?.showConversions;
      if (!tonnageProperty)
        tonnageProperty = useConversions ? 'netWeightLb' : 'netWeight';
      const counterLoad = this.getCounterPartLoad()
      if (this.outload || ['completed', 'delivered'].includes(this.props?.movement?.status))
        if (weightId === WEIGHT_ORIGIN_ID)
          maxAllowedTonnage = maxAllowedTonnage + Math.max(get(this.outload, tonnageProperty), this.props.movement.inferredTonnage);
        else
          maxAllowedTonnage = maxAllowedTonnage + Math.max(this.props.movement.inferredTonnage, get(this.outload, tonnageProperty), get(counterLoad, tonnageProperty));
      return parseFloat(maxAllowedTonnage.toFixed(2))
    }
    return 0;
  }

  handleSubmit(event) {
    event.preventDefault();
    event.stopPropagation()
    if (this.state.selectedConsignor && get(this.state.selectedConsignor, 'externallySyncSource')) {
      alertifyjs.error('Cannot perform this operation as site is externally managed', 5);
      return;
    }

    const outloadDateErrors = get(this.state, 'models.date.errors', []);
    const outloadTimeErrors = get(this.state, 'models.time.errors', []);

    if (outloadDateErrors.length > 0 || outloadTimeErrors.length > 0)
      return;

    this.setState({ submitting: true }, () => {
      let check = true;
      const farmId = this.outload?.farmId || this.outload?.farmField?.farmId
      if (farmId) {
        let existingSite = this.state.selectedConsignor
        if (existingSite) {
          if (this.isRestrictedToEditForSite(existingSite)) {
            this.unlockSubmit()
            return false
          }
        }
      }
      if(!get(this.props, 'movement.order') && this.state.matchingPackOrderId.value) {
        const matchingPackOrder = find(this.state.matchingPackOrders, {id: this.state.matchingPackOrderId.value})
        const remainingTonnage = get(matchingPackOrder, 'outstandingTonnage', 0)
        const netWeight = parseFloat(get(this.state.models, 'estimatedNetWeight.value', 0))
        const currentOrderTonnage = get(matchingPackOrder, 'deliveredTonnage', 0)
        const updateToTonnage = currentOrderTonnage + netWeight
        const unit = get(matchingPackOrder, 'unit')
  
        if(netWeight > remainingTonnage) {
          alertifyjs.confirm(
            'Warning',
            `<div>
                This pack order can take up to only ${remainingTonnage} ${unit}.
                Saving this load will take the ${this.countryTonnageLabel.toLowerCase()} of pack order to ${updateToTonnage.toFixed(2)} ${unit}. Do you want to automatically
                update the pack order ${this.countryTonnageLabel.toLowerCase()} to ${updateToTonnage.toFixed(2)} ${unit} on saving this load?
            </div>`,
            () => {
              this.submitData()
            },
            () => {
              this.unlockSubmit()
            }
          ).set('labels', { ok: 'Proceed', cancel: 'Cancel' });
          return;
        }
      }
      if (
        this.props.optionType == OPTION_TYPE_WEB_SPLIT_LOADS &&
        this.state.models.storageId.value &&
        get(this.props, 'movement.freightPickup.load')
      ) {
        const loads = this.props.movement.freightPickup.load
        const isExistingStorage = some(loads, load => get(load, 'storageId') == this.state.models.storageId.value)
        if (isExistingStorage) {
          this.unlockSubmit()
          return false
        }
      }
      let pickupSite = this.state.selectedConsignor
      const counterSlot = this.getCounterPartLoad();
      if (this.isRestrictedToEditForSite(pickupSite)) {
        this.unlockSubmit()
        return false
      }
      else if (this.isContainerMovement() && counterSlot && get(counterSlot, 'handler.id') == this.state.selectedConsignor?.id) {
        this.unlockSubmit('Pickup and Delivery sites cannot be the same in container movement')
        return false
      }
      if (this.props.movement?.typeId === PACK_ORDER_TYPE_ID && !isEqual(this.state.initialStorageId, get(this.state.models, 'storageId.value'))
        && get(this.state, 'selectedSite.type') === 'container') {
        check = false;
        alertifyjs.confirm(
          'Warning',
          `<div>
          <p>The storage selected for outload is a container. This action will remove stock from the container and add it to the current container</p>
          <p>Please confirm if this is the intended result</p>
        </div>`,
          () => {
            check = true;
            this.submitData(check);
          },
          this.unlockSubmit
        ).set('reverseButtons', true).set('labels', { ok: 'Confirm' });
      }
      const submitCallback = () => {
        if (check) {
          if (!this.getIsFormValid()) {
            this.unlockSubmit()
            return false
          } else {
            let pickupId = get(find(this.state.sites, { id: get(this.state.models, 'storageId.value') }), 'entity') != "farm_field";
            if (pickupId && this.state.models.consignor.handlerId.value)
              getStoredCommodityDetails(this.state.models.storageId.value, (data) => {
                let available_tonnage = this.props.isCreate ? get(find(data, ['id', get(this.state.models, 'commodityId.value')]), 'tonnage') : get(find(data, ['id', get(this.state.models, 'commodityId.value')]), 'tonnage') + this.getInitialEstimatedNetWeight();
                if ((!some(data, ['id', this.state.models.commodityId.value]) || some(data, ['id', this.state.models.commodityId.value]) && available_tonnage < get(this.state, 'models.estimatedNetWeight.value', 0)) && !isEmpty(data)) {
                  alertifyjs.confirm(
                    'Warning',
                    `<div className="">
                  <p>Please verify the storage information before outloading:</p>
                  <ul style="list-style: none;"><li>Storage Name: ${get(this.state, 'selectedSite.name')}</li>
                  <li>
                  <table>
                    <tr>
                      <th>Commodity</th>
                      <th>Grade</th>
                      <th>Season</th>
                      <th>Available ${getCountryLabel('tonnage')} (${this.isStrictQuantityBased() ? this.quantityUnit() : this.priceUnit()})</th>
                      <th>Space Remaining</th>
                    </tr>
                    ${getLoadWarningTableData(data, available_tonnage)}
                  </table>
                  </li>
                  </ul>
                </div>`,
                    () => {
                      this.submitData(check);
                    },
                    this.unlockSubmit
                  );
                }
                else this.submitData(check);
              });
            else this.submitData(check);
          }
        }
      }
      this.setAllFieldsErrors(submitCallback)
    })
  }

  async submitData(check) {
    if(!this.validateMatchingPackOrderSelection())
      return
    if (!this.getIsFormValid()) {
      this.unlockSubmit();
      return false;
    }
    if (isEmpty(this.state.stockOwner.errors) && this.getIsFormValid() && check) {
      const maxAllowedTonnage = this.getMaxAllowedTonnage()
      if (get(this.props, 'movement') && maxAllowedTonnage && maxAllowedTonnage < parseFloat(get(this.state.models, 'estimatedNetWeight.value', 0)))
        this.setState({ showAmendTonnagePopup: true });
      else
        this.confirmSubmit(this.getSubmitData());
    }
    else
      this.unlockSubmit()
  }

  handleFieldChange(event) {
    this.setFieldValue(`models.${event.target.id}`, event.target.value);
  }

  handleIdentifierChange(event) {
    const regex = new RegExp('^[a-zA-Z0-9]*$');
    if (regex.test(event.target.value)) {
      this.handleFieldChange(event);
    }
  }

  handleContainerTareChange(event) {
    let value = event.target.value;
    if (isString(value))
      value = value.replaceAll(',', '').replaceAll(' ', '')
    this.setFieldValue('models.containerTare', value, true, state => {
      const newState = { ...state };
      newState.models.estimatedNetWeight.value = this.getNetWeight(newState);
      newState.models.netCargo.value = this.recalculateNetCargo(newState)
      this.setState(newState);
    })
  }

  handleContainerGrossChange(event) {
    let value = event.target.value;
    if (isString(value))
      value = value.replaceAll(',', '').replaceAll(' ', '')
    this.setFieldValue('models.containerGross', value, true, state => {
      const newState = { ...state };
      newState.models.containerTare.validators = [valueAbove(0), valueBelow(value)];
      newState.models.containerGross.validators = [isNaN(newState.models.containerTare.value) ? valueAbove(0) : valueAbove(newState.models.containerTare.value)];
      newState.models.estimatedNetWeight.value = this.getNetWeight(newState);
      this.setState(newState, () => {
        if (get(this.state, 'models.tareWeight.value'))
          this.setFieldErrors('models.tareWeight');
        if (get(this.state, 'models.containerTare.value'))
          this.setFieldErrors('models.containerTare');
      });
    })
  }

  handleTareWeightChange(event) {
    let value = event.target.value
    if (isString(value))
      value = value.replaceAll(',', '').replaceAll(' ', '')
    this.setFieldValue(`models.${event.target.id}`, value, true, state => {
      const newState = { ...state };
      this.changeFreightPickupTareWeightGrossWeightValidators(newState);

      const tareWeight = parseFloat(newState.models.tareWeight.value);
      newState.models.grossWeight.validators = [valueAbove(tareWeight)];
      newState.models.netWeight.value = this.getNetWeight(newState, 'container');
      newState.models.netCargo.value = this.recalculateNetCargo(newState)
      this.setState(newState, () => {
        this.checkNetWeightAgainstCurrentTonnage();
        if (get(this.state, 'models.grossWeight.value'))
          this.setFieldErrors('models.grossWeight');
      });
    });
  }

  handleQuantityFieldChange(event) {
    let value = event.target.value
    if (isString(value)) {
      value = value.replaceAll(',', '').replaceAll(' ', '')
      if (value === '')
        value = null;
    }

    this.setFieldValue(`models.${event.target.id}`, value, true, state => {
      const newState = { ...state };
      if (this.isStrictQuantityBased())
        newState.models.estimatedNetWeight.value = value;
      this.setState(newState, () => {
        if (get(this.state, 'models.quantity.value')) {
          this.setFieldErrors('models.quantity');
        }
      });
    });
  }

  handleNumberOfBagsFieldChange(event) {
    let value = event.target.value
    if (isString(value)) {
      value = value.replaceAll(',', '').replaceAll(' ', '')
      if (value === '')
        value = null;
    }
    this.setFieldValue(`models.${event.target.id}`, value, true);
  }

  handleGrossWeightChange(event) {
    let value = event.target.value
    if (isString(value))
      value = value.replaceAll(',', '').replaceAll(' ', '')
    this.setFieldValue(`models.${event.target.id}`, value, true, state => {
      const newState = { ...state };
      this.changeFreightPickupTareWeightGrossWeightValidators(newState);
      const grossWeight = parseFloat(newState.models.grossWeight.value);
      newState.models.tareWeight.validators = [valueBelow(grossWeight)];
      newState.models.netWeight.value = this.getNetWeight(newState, 'container');
      newState.models.netCargo.value = this.recalculateNetCargo(newState)
      this.setState(newState, () => {
        this.checkNetWeightAgainstCurrentTonnage();
        if (get(this.state, 'models.tareWeight.value'))
          this.setFieldErrors('models.tareWeight');
        if (get(this.state, 'models.containerTare.value'))
          this.setFieldErrors('models.containerTare');
      });
    });
  }

  getNetWeight = (newState, site = 'pack') => {
    let tareWeight = site == 'pack' ? 'containerTare' : 'tareWeight'
    let grossWeight = site == 'pack' ? 'containerGross' : 'grossWeight'
    let netWeight = site == 'pack' ? 'estimatedNetWeight' : 'netWeight'
    let estimatedNetWeight = parseFloat(newState.models[grossWeight].value) - parseFloat(newState.models[tareWeight].value);
    if (isNaN(estimatedNetWeight))
      estimatedNetWeight = parseFloat(newState.models[netWeight].value);
    return isNaN(estimatedNetWeight) ? 0.0 : parseFloat(estimatedNetWeight).toFixed(2);
  };

  checkAnyWeightExists = site => {
    let tareWeight = site == 'pack' ? 'containerTare' : 'tareWeight'
    let grossWeight = site == 'pack' ? 'containerGross' : 'grossWeight'
    let netWeight = site == 'pack' ? 'estimatedNetWeight' : 'netWeight'
    some([
      get(this.state, `models.${tareWeight}.value`),
      get(this.state, `models.${grossWeight}.value`),
      get(this.state, `models.${netWeight}.value`),
    ])
  };

  validateWeightFields = site => {
    let tareWeight = site == 'pack' ? 'containerTare' : 'tareWeight'
    let grossWeight = site == 'pack' ? 'containerGross' : 'grossWeight'
    let netWeight = site == 'pack' ? 'estimatedNetWeight' : 'netWeight'
    return parseFloat(get(this.state, `models.${netWeight}.value`)).toFixed(2) ==
      (parseFloat(get(this.state, `models.${grossWeight}.value`)) - parseFloat(get(this.state, `models.${tareWeight}.value`))).toFixed(2);
  }

  setSellerAvailableTonnage() {
    const commodityId = get(this.state, 'models.commodityId.value');
    const seasonId = get(this.state, 'models.season.value');
    const gradeId = get(this.state, 'models.gradeId.value');
    const handlerId = get(this.state, 'models.consignor.handlerId.value')
    const ngrId = get(this.state, 'models.ngrId.value')
    if (!this.state.isLoadingAvailableTonnage && commodityId && gradeId && handlerId && seasonId && ngrId && get(this.state.selectedSite, 'entity') != 'farm_field') {
      this.setState({ isLoadingAvailableTonnage: true }, () => {
        const service = APIService.ngrs(ngrId).appendToUrl('available-stock/').appendToUrl(`?commodityId=${commodityId}&gradeId=${gradeId}&siteId=${handlerId}&season=${seasonId}`)

        const callback = res => {
          const newState = { ...this.state }
          newState.availableTonnageCache[service.URL] = res
          newState.sellerAvailableTonnage = res.totalTonnage;
          let availableTonnage = res.totalTonnage;
          if (this.outload && this.props.movement) {
            if (commodityId === get(this.outload, 'commodityId') && seasonId === get(this.outload, 'season') && gradeId === get(this.outload, 'gradeId')
              && handlerId === get(this.outload, 'handler.id') && ngrId == get(this.outload, 'ngrId'))
              availableTonnage = (Number(res.totalTonnage) + Number(this.state.models.estimatedNetWeight.value));
          }
          newState.sellerAvailableTonnageMessage = `Warning: Available ${getCountryLabel('tonnage')}: ${availableTonnage} ${getCountryConfig()?.truck?.unit}`
          this.setState(newState, () => this.props.forceStopLoader());
        }

        if (has(this.state.availableTonnageCache, service.URL))
          callback(this.state.availableTonnageCache[service.URL])
        else
          service.get(null, { 'REFERER-UNIT': this.state.unit, 'REFERER-UNIT-FOR-REQUEST': true }).then(callback)
      })
    } else if (this.state.sellerAvailableTonnageMessage)
      this.setState({ sellerAvailableTonnage: undefined, sellerAvailableTonnageMessage: '' });
  }

  checkNetWeightAgainstCurrentTonnage() {
    const grossWeight = parseFloat(this.state.models.grossWeight.value);
    const tareWeight = parseFloat(this.state.models.tareWeight.value);
    var estimatedNetWeight = parseFloat(this.state.models.estimatedNetWeight.value);
    const relevantStocks = find(get(this.state.storage, 'stocks'), stocksItem => {
      const stockNgrId = get(stocksItem, 'ngr.id');
      return !get(this.state.storage, 'isOwner')
        ? get(stocksItem, 'commodity.id') === this.state.models.commodityId.value &&
        (stockNgrId ? stockNgrId === this.state.models.ngrId.value : true) &&
        get(stocksItem, 'season') === this.state.models.season.value &&
        get(stocksItem, 'grade.id') === this.state.models.gradeId.value
        : stocksItem.commodity.id === this.state.models.commodityId.value;
    });
    const currentTonnage = get(relevantStocks, 'currentTonnage', 0);
    const existingNetWeight = this.outload ? this.outload.netWeight : 0;
    if (grossWeight && tareWeight) {
      estimatedNetWeight = grossWeight - tareWeight;
    }
    const isNetWeightGreaterThanCurrentTonnage = estimatedNetWeight && this.state.storage ? estimatedNetWeight > existingNetWeight + currentTonnage : false;
    this.setState(state => ({ ...state, isNetWeightGreaterThanCurrentTonnage }), () => {
      this.setSellerAvailableTonnage();
    });
  }

  regoCallback = data => {
    if (data?.isAvailable) {
      const newState = { ...this.state };
      newState.models.truckId.errors = [];
      this.setState(newState)
      let config = getCountryConfig();
      let payload = {
        rego: data?.rego.toUpperCase(),
        tareWeight: get(config, 'truck.tareWeight'),
        grossWeight: get(config, 'truck.grossWeight')
      }
      let companyId = config?.systemCompanyId;
      this.props.createTruck(companyId, payload, addTruck);
    }
  }

  handleSelectFieldChange(value, elementId, item) {
    if (value === 'Invalid date')
      value = ''
    let element = `models.${elementId}`;
    let validateAfterSet = elementId != 'truckId'
    if (includes(['date', 'time'], elementId)) {
      if (!this.validateAndSetDateTime(value, elementId))
        return;
    }

    this.setFieldValue(element, value, validateAfterSet, () => {
      if (includes(['season', 'ngrId', 'date', 'time'], elementId)) {
        this.checkNetWeightAgainstCurrentTonnage();
        if(elementId === 'season' && !this.state.isFetchingOrders && this.isCustomisedPackAndShip)
          this.fetchMatchingPackOrders(value, get(this.state, 'models.varietyId.value'));
      } else if (elementId === 'truckId') {
        if (item && item?.inputValue) {
          if (item?.inputValue.length < 3 || item?.inputValue.length > 10) {
            const newState = { ...this.state };
            newState.models.truckId.errors.push(truckRegoRegex().message);
            this.setState(newState);
            return
          } else
            this.props.validateRego(elementId, item?.inputValue, this.regoCallback);
        }
        this.setUpDefaultValueWeightAndOwnerCompany(value, item);
      }
    });
  }

  setUpDefaultValueWeightAndOwnerCompany = (truckId, item) => {
    const truck = find(this.state.trucks, { id: truckId }) || item;
    if (truck) {
      const newState = { ...this.state };
      if (!this.checkAnyWeightExists('container')) {
        if (this.state.selectedConsignor?.populateDefaultWeights) {
          newState.models.tareWeight.value = get(truck, 'totalWeights.tareWeight');
          newState.models.grossWeight.value = get(truck, 'totalWeights.grossWeight');
          newState.models.netWeight.value = get(truck, 'totalWeights.netWeight');
        }
      }
      if(this.isCustomisedPackAndShip && item?.estimatedTruckTare)
        newState.models.tareWeight.value = item?.estimatedTruckTare;
      newState.models.netWeight.errors = [];
      newState.models.truckId.value = truckId;
      if (!find(this.state.trucks, { id: item.id }))
        newState.trucks.push(item);
      this.setState(newState);
    }
  };

  getCommoditySpecs = commodityId => commodityId ? get(find(this.props.commodities, { id: commodityId }), 'specs', []) : []

  handleCommodityChange(value, elementId) {
    const commoditySpecs = this.getCommoditySpecs(value);
    const models = set(this.state.models, 'specs', this.getModelSpecsByCommoditySpecs(commoditySpecs));
    const pastCommodityId = this.state.models.commodityId.value
    if (!isEqual(this.state.models, models) || !isEqual(commoditySpecs, this.state.commoditySpecs)) {
      this.setState(
        state => ({
          ...state,
          models: set(state.models, 'specs', this.getModelSpecsByCommoditySpecs(commoditySpecs)),
          commoditySpecs,
        }),
        () => {
          this.syncCoilAndImpu();
          this.setFieldValue(`models.${elementId}`, value, true, () => {
            const pastSeasonValue = this.state.models.season.value
            let currentSeasonValue = pastSeasonValue
            if (FERTILISER_IDS.includes(this.state.models.commodityId.value))
              currentSeasonValue = SEASON_NA
            else if (FERTILISER_IDS.includes(pastCommodityId) && pastSeasonValue)
              currentSeasonValue = null
            if (currentSeasonValue != pastSeasonValue)
              this.setFieldValue('models.season', currentSeasonValue, true)
            this.checkNetWeightAgainstCurrentTonnage();
            this.getModelSpecsByStorageSpecs(commoditySpecs);
            const newState = { ...this.state };
            if (this.hasQuantityBasedCommodity()) newState.models.quantity.validators = [required()];
            else if (!isEmpty(this.props.commodities)) {
              newState.models.quantity.validators = [];
              newState.models.quantity.errors = [];
              newState.models.quantity.value = undefined;
            }
          });
        },
      );
    }
  }

  setGradeSpecs = gradeId => {
    const value = gradeId || this.state.models.gradeId.value
    const commodityGrades = get(
      find(this.props.commodities, { id: this.state.models.commodityId.value }),
      'grades',
      []
    );
    const gradeSpecs = get(
      find(commodityGrades, { id: value }),
      'specs',
      []
    );
    const gtaCode = get(
      find(commodityGrades, { id: value }),
      'gtaCode',
      []
    );
    if (value) {
      const updatedSpecs = gtaCode ? gradeSpecs : [];
      this.setState(
        state => ({
          ...state,
          gradeSpecs: updatedSpecs
        }),
        () => {
          forEach(gradeSpecs, (specCode) =>
            this.setFieldWarnings(`models.specs.${specCode['code']}`, specCode['code'])
          );
        }
      );
    }
  }

  getCounterPartLoad() {
    const { movement } = this.props;
    return get(movement, 'freightDelivery.load.0');
  }

  hasCOILOrIMPU(load) {
    if (load)
      return get(load, 'specs.coil') || get(load, 'specs.impu');
  }

  syncCoilAndImpu() {
    const counterPart = this.getCounterPartLoad();
    if (this.hasCOILOrIMPU(counterPart) && !this.hasCOILOrIMPU(this.outload) && !isEmpty(this.state.models.specs)) {
      const newState = { ...this.state };

      newState.models.specs['COIL'].value = counterPart.specs['coil'];
      newState.models.specs['IMPU'].value = counterPart.specs['impu'];
      this.setState(newState);
    }
  }

  handleVarietyChange(value) {
    if (this.state.models.varietyId.value !== value) {
      this.setFieldValue(`models.varietyId`, value, true, () => {
        if(!this.state.isFetchingOrders && this.isCustomisedPackAndShip)
          this.fetchMatchingPackOrders(get(this.state, 'models.season.value'), value, get(this.state, 'models.ngrId.value'))
      });
    }
  }

  handleGradeChange(object) {
    if (this.state.models.gradeId.value !== object.id) {
      this.setFieldValue('models.gradeId', isNumber(object.id) ? object.id : undefined, true, () => {
        this.checkNetWeightAgainstCurrentTonnage();
        this.setGradeSpecs(object.id);
      }
      );
    }
    if (!isEmpty(this.state.chemicalApplications))
      this.setState({ chemicalApplications: [] }, () => this.onChemicalApplicationAdd())
  }

  handleSpecsChange(specCode, specValue) {
    this.setFieldValue(`models.specs.${specCode}`, specValue);
    this.setFieldWarnings(`models.specs.${specCode}`, specCode);
  }

  setFieldValue(path, value, validateAfterSet = true, callback) {
    this.setState(
      state => set(state, `${path}.value`, value),
      () => {
        if (callback) callback(this.state);
        if (validateAfterSet) this.setFieldErrors(path);
      },
    );
  }

  setFieldWarnings(path, specCode) {
    this.setState(state => set(state, `${path}.warnings`, this.getFieldWarnings(path, specCode)));
  }

  setFieldErrors(path) {
    this.setState(state => set(state, `${path}.errors`, this.getFieldErrors(path)));
  }

  canRaiseMovementParentAmendRequest() {
    return get(this.props, 'movement.canRaiseParentAmendRequest');
  }

  getFieldWarnings(path, specCode) {
    const { gradeSpecs } = this.state;
    const warnings = [];
    const value = get(this.state, `${path}.value`);

    gradeSpecs.forEach(spec => {
      if (spec['code'] === specCode) {
        const min = get(spec, 'min', null)
        const max = get(spec, 'max', null)
        if (!(isNull(min) || isNull(max) || isNull(value))) {
          if (!(value >= min && value <= max))
            warnings.push(`GTA Suggested Range: ${min} - ${max}`);
        }
      }
    }
    )
    return warnings;
  }

  getFieldErrors(path) {
    const errors = [];
    const value = get(this.state, `${path}.value`);
    const validators = get(this.state, `${path}.validators`, []);
    validators.forEach(validator => {
      if (validator.isInvalid(value)) {
        errors.push(validator.message);
      }
      if (path == 'models.estimatedNetWeight' && this.props.movement) {
        const maxAllowedTonnage = this.getMaxAllowedTonnage();
        if (maxAllowedTonnage && parseFloat(value) > maxAllowedTonnage)
          errors.push(`${this.canRaiseMovementParentAmendRequest() ? 'Warning: ' : ''}Maximum allowed tonnage is ${maxAllowedTonnage} ${this.state.unit}`);
      }
    });
    if (path == 'models.consignor.handlerId' && this.isRestrictedToEditForSite(this.state.selectedConsignor)) {
      errors.push(LOAD_CREATE_OR_EDIT_ERROR_MESSAGE.replace('$action', 'created'))
    }
    if (
      path == 'models.storageId' &&
      this.state.models.storageId.value &&
      get(this.props, 'movement.freightPickup.load')
    ) {
      const loads = this.props.movement.freightPickup.load
      const isExistingStorage = some(loads, load => get(load, 'storageId') == this.state.models.storageId.value)
      if (isExistingStorage) {
        if (this.props.isCreate)
          errors.push('Load exists for this storage. Please select another storage.')
        else if (get(this.outload, 'storageId') != this.state.models.storageId.value)
          errors.push('Load exists for this storage. Please select another storage.')
      }
    }

    return errors;
  }

  setAllFieldsErrors(callback) {
    this.updateValidatorsBasedOnStorage(() => {
      var newState = cloneDeep(this.state);
      forEach(newState.models, (_, fieldKey) => {
        const path = `models.${fieldKey}`;
        newState = set(newState, `${path}.errors`, this.getFieldErrors(path));
      });
      if(this.isCustomisedPackAndShip && !get(this.props, 'movement.order') && !newState.matchingPackOrderId.value)
        newState.matchingPackOrderId.errors = ['This field is required']
      newState.chemicalApplications = this.state.chemicalApplications.map(application => {
        let errors = [];
        if (!application.commodityId || !application.storageId || !application.applicationRate)
          errors.push('This field is required');
        return { ...application, errors };
      });
      this.setState(newState, () => {
        this.setStockOwnerMandatoryError();
        if (callback)
          callback()
      });
    });
  }

  getIsFormValid() {
    if (!isEmpty(this.state.models.estimatedNetWeight.errors) && (!isSystemCompany() || this.state.models.estimatedNetWeight.value) && !this.canRaiseMovementParentAmendRequest())
      return false;
    if (!isEmpty(this.state.chemicalApplications) && this.state.chemicalApplications.some(chemicalApplication => !isEmpty(chemicalApplication.errors) || !isEmpty(chemicalApplication.commodityErrors)))
      return false;
    if (!isEmpty(this.state.models.consignor.handlerId.errors) && !isSystemCompany())
      return false;
    return !some(this.state.models, field => {
      let fieldsToOmit = [];
      if (isSystemCompany())
        fieldsToOmit.push('storageId', 'estimatedNetWeight', 'ngrId', 'consignor', 'netWeight', 'season', 'freightProviderId', 'truckId', 'farmFieldId', 'gradeId')
      if (this.isContainerMovement())
        fieldsToOmit.push('storageId');
      return some(field.validators, validator => {
        return validator.isInvalid(field.value);
      });
    });
  }

  hideGrossAndNetWeightFields = () => this.isCustomisedPackAndShip && this.state.packAndShip

  getSubmitData() {
    const { movement } = this.props;
    let data = {
      outload: { type: 'outload', feature: 'Pack Ship Form' },
      inload: { type: 'inload', feature: 'Pack Ship Form' },
    }
    const isEditing = this.outload
    const hideGrossAndNetWeightFields = this.hideGrossAndNetWeightFields()
    const packAndShipFields = ['consignee.handlerId', 'tareWeight', 'grossWeight', 'netWeight', 'truckId']
    const outloadFields = ['consignor.handlerId', 'storageId', 'date', 'time', 'commodityId', 'gradeId', 'varietyId', 'season', 'numberOfBags', 'ngrId', 'estimatedNetWeight', 'specs', 'comment']
    const inloadFields = ['consignee.handlerId', 'containerNumber', 'date', 'time', 'commodityId', 'gradeId', 'varietyId', 'season', 'ngrId', 'numberOfBags', 'containerTare', 'containerGross', 'estimatedNetWeight', 'specs', 'sealNumbers', 'comment', 'orderId']
    if (this.state.packAndShip)
      data['containerMovement'] = { containerNumber: movement.containerNumber }
    if (this.isCustomisedPackAndShip && this.state.packAndShip) {
      data['containerMovement'] = {...data['containerMovement']}
    }
    forEach(this.state.models, (field, fieldKey) => {
      if (!includes(packAndShipFields, fieldKey)) {
        if (fieldKey != 'consignor') {
          const path = `models.${fieldKey}`;
          if (fieldKey == 'specs')
            data['outload'] = set(data['outload'], `${fieldKey}`, mapValues(this.state.models.specs, spec => spec.value));
          else if (includes(outloadFields, fieldKey))
            data['outload'] = set(data['outload'], `${fieldKey}`, get(this.state, `${path}.value`, null));

          if (includes(inloadFields, fieldKey)) {
            if (fieldKey == 'sealNumbers')
              data['inload'].sealNumbers = map(this.state.models.sealNumbers.value, sealNumber => defaultTo(sealNumber.value, ''));
            else if (fieldKey == 'specs')
              data['inload'] = set(data['inload'], `${fieldKey}`, mapValues(this.state.models.specs, spec => spec.value));
            else
              data['inload'] = set(data['inload'], `${fieldKey}`, get(this.state, `${path}.value`, null));
          }
        }
      } else if (this.state.packAndShip) {
        if (fieldKey != 'consignee') {
          const path = `models.${fieldKey}`;
          if (includes(packAndShipFields, fieldKey))
            data['containerMovement'] = set(data['containerMovement'], `${fieldKey}`, get(this.state, `${path}.value`, null));
        }
      }
    });
    if (isSystemCompany() && !data.estimatedNetWeight) {
      data.outload.estimatedNetWeight = null;
      data.inload.estimatedNetWeight = null;
    }
    data.outload.storageId = null;
    const site = get(this.props.movement, 'freightPickup.consignor.sites[0].location');
    if (get(site, 'entity') === 'storage' && !isSystemCompany()) {
      data.outload.storageId = site.id;
    }
    if (this.state.models.consignor.handlerId.value && this.state.selectedSite && this.state.selectedSite.entity === 'storage') {
      data.outload.storageId = this.state.selectedSite.id;
      data.outload.farmFieldId = null;
    }

    if (this.state.selectedSite && this.state.selectedSite.entity === 'farm_field') {
      data.outload.storageId = null;
      data.outload.farmFieldId = this.state.selectedSite.id;
    }

    if (this.outload && this.props.storageId) {
      data.outload.storageId = this.props.storageId;
    }

    if (isNaN(parseFloat(data.outload.estimatedNetWeight))) {
      data.outload.estimatedNetWeight = null;
      data.inload.estimatedNetWeight = null;
    }

    if (this.props.movement) {
      data.outload.consignor = {
        handlerId: this.state.models.consignor.handlerId.value,
      };
      if (this.state.packAndShip) {
        data.containerMovement.consignee = {
          handlerId: this.state.models.consignee.handlerId.value,
        };
      }
    }
    const utcDateTime = getDateTimeInUTC(data.outload.date, data.outload.time);
    data.outload.dateTime = data.inload.dateTime = utcDateTime.dateTime;
    data = omit(data, ['outload.date', 'outload.time', 'inload.date', 'inload.time']);
    if (movement) {
      data.outload.freightMovementId = movement.id;
      data.inload.freightMovementId = movement.id;
    }
    data.outload.quantity = this.state.models.quantity.value;
    data.inload.quantity = this.state.models.quantity.value;

    let containerTare = parseFloat(data.inload.containerTare || 0);
    let truckTare = parseFloat(data?.containerMovement?.tareWeight || 0);
    data.inload.tareWeight = containerTare;
    if (data.containerMovement)
      data.containerMovement.tareWeight = truckTare;

    if (data.inload.tareWeight === 0)
      data.inload.tareWeight = null;

    let containerGross = parseFloat(data.inload.containerGross || 0);
    let truckGross = parseFloat(data?.containerMovement?.grossWeight || 0);
    data.inload.grossWeight = containerGross;
    if (data.containerMovement)
      data.containerMovement.grossWeight = truckGross;

    if (data.inload.grossWeight === 0)
      data.inload.grossWeight = null;
    data.inload['splitWeights'] = { 'container_tare': containerTare, 'truck_tare': truckTare, 'container_gross': containerGross, 'truck_gross': truckGross };
    if (data.containerMovement)
      data.containerMovement['splitWeights'] = { 'container_tare': containerTare, 'truck_tare': truckTare, 'container_gross': containerGross, 'truck_gross': truckGross, 'truck_net_weight': parseFloat(this.state.models.netWeight.value || 0), 'net_cargo': parseFloat(this.state.models.netCargo.value || 0) };
    if (this.state.packAndShip && (isNaN(parseFloat(data?.containerMovement?.tareWeight)) || data?.containerMovement?.tareWeight == 0))
      data.containerMovement.tareWeight = null;

    if (this.state.packAndShip && (isNaN(parseFloat(data?.containerMovement?.grossWeight)) || data?.containerMovement?.grossWeight == 0))
      data.containerMovement.grossWeight = null;

    if (hideGrossAndNetWeightFields) {
      let netCargo = parseFloat(this.state.models.netCargo.value || 0)
      let containerTare = parseFloat(this.state.models.containerTare.value || 0)
      data['outload']['estimatedNetWeight'] = netCargo;
      data['inload']['estimatedNetWeight'] = netCargo
      data['outload']['grossWeight'] = netCargo + containerTare;
      data['inload']['grossWeight'] = netCargo + containerTare;
    }
    if ((!isEditing || !get(this.props, 'movement.order')) && this.state.matchingPackOrderId.value)
      data.inload.orderId = this.state.matchingPackOrderId.value
    delete data.inload.containerTare;
    delete data.inload.containerGross;
    delete data.bhcSite;

    if (this.state.chemicalApplications.length > 0) {
      data.chemicalApplications = this.state.chemicalApplications.map(chemicalApplication => {
        let storageId = chemicalApplication.storageId;
        let farmFieldId = null;
        let storage = find(this.state.sites, { id: storageId });
        if (get(storage, 'entity') === 'farm_field') {
          farmFieldId = chemicalApplication.storageId;
          storageId = null;
        }
        let chemicalApplicationData = {
          commodityId: chemicalApplication.commodityId,
          storageId: storageId,
          farmFieldId: farmFieldId,
          applicationFee: chemicalApplication.applicationRate
        }
        if (get(chemicalApplication, 'chemicalLoadId'))
          chemicalApplicationData.chemicalLoadId = get(chemicalApplication, 'chemicalLoadId')
        return chemicalApplicationData;
      });
    }
    if (this.state.amendParentTonnage) {
      const parentTonnage = min([get(this.props, 'movement.maxAllowedTonnageOnOrder'), get(this.props, 'movement.parentTotalTonnageWithTolerance')]);
      data.amendParentToTonnage = (parentTonnage + parseFloat(get(this.state.models, 'estimatedNetWeight.value', 0)) - this.getMaxAllowedTonnage());
    }
    if (this.isStrictQuantityBased()) {
      data.outload.estimatedNetWeight = data.quantity;
      data.inload.estimatedNetWeight = data.quantity
    }
    let createData ={
      loads: [data.outload, data.inload]
    }
    if (this.state.packAndShip)
      createData['containerMovement'] = data?.containerMovement
    return isEditing ? data : createData
  }

  async getModelSpecsByCommoditySpecs(commoditySpecs) {
    const modelSpecs = {};
    if (!isEmpty(commoditySpecs)) {
      forEach(orderBy(commoditySpecs, 'order'), (spec) => {
        const validators = [valueBetween(spec.min, spec.max, true)];
        let specValue = get(this.outload, `specs.${spec.code.toLowerCase().replace('-', '')}`, null) ?? get(this.outload, `specs.${spec.code.replace('-', '')}`, null);
        modelSpecs[spec.code] = {
          ...FIELD,
          value: specValue,
          validators,
        };
      });
    }
    return modelSpecs;
  }

  async getModelSpecsByStorageSpecs(commoditySpecs) {
    const modelSpecs = {};
    let response = {};
    let commodityId = this.state.models.commodityId.value;
    let isCommodityCanola = false;
    let pickupSite = this.state.selectedConsignor
    if (commodityId === COMMODITIES.CANOLA) {
      isCommodityCanola = true;
    }
    if (isEmpty(commoditySpecs))
      commoditySpecs = get(find(this.props.commodities, { id: commodityId }), 'specs', []);
    if (!isEmpty(commoditySpecs)) {
      if (this.props.isCreate &&
        this.isSelectedSiteNotFarmField() &&
        !get(this.state.selectedSite, 'isGate') &&
        get(this.state.models.storageId, 'value', '') &&
        get(this.state.models, 'commodityId.value', '') &&
        (this.isContainerMovement() || get(pickupSite, 'stocksManagement'))
      ) {
        response = await APIService.storages(this.state.models.storageId.value).appendToUrl(`estimate_specs_grade?commodity_id=${this.state.models.commodityId.value}`).get();
      }
      forEach(orderBy(commoditySpecs, 'order'), (spec) => {
        let specValue = get(response, `specs.${spec.code.toLowerCase()}`, false) ? parseFloat(get(response, `specs.${spec.code.toLowerCase()}`, 0)).toFixed(2) : null;
        let validators = [valueBetween(spec.min, spec.max, true)];
        if (isCommodityCanola && includes(['COIL', 'IMPU'], spec.code)) {
          validators.push(required());
        }
        modelSpecs[spec.code] = {
          ...FIELD,
          value: get(this.outload, `specs.${spec.code.toLowerCase()}`, specValue),
          validators,
        };
      });
    }
    this.setState(
      state => ({
        ...state,
        models: set(state.models, 'specs', modelSpecs),
        commoditySpecs,
      })
    );
  }

  async getModelSpecsFromInloadSpecs(commoditySpecs, specValues) {
    if (specValues) {
      const modelSpecs = {};
      if (isEmpty(commoditySpecs))
        commoditySpecs = get(find(this.props.commodities, { id: this.state.models.commodityId.value }), 'specs', []);
      if (!isEmpty(commoditySpecs)) {
        forEach(orderBy(commoditySpecs, 'order'), (spec) => {
          let specValue = get(specValues, `${spec.code.toLowerCase()}`, false) ? parseFloat(get(specValues, `${spec.code.toLowerCase()}`, 0)).toFixed(2) : null;
          const validators = [valueBetween(spec.min, spec.max, true)];
          modelSpecs[spec.code] = {
            ...FIELD,
            value: specValue,
            validators,
          };
        });
      }
      this.setState(
        state => ({
          ...state,
          models: set(state.models, 'specs', modelSpecs),
          commoditySpecs,
        })
      );
    }
  }

  getNgrs() {
    const { companyId, companyIds, farmId, farmIds, movement, token } = this.props;
    let _companyIds = companyIds || (companyId ? [companyId] : []);
    let _farmIds = farmIds || (farmId ? [farmId] : []);
    if (isEmpty(_farmIds) && this.farmId)
      _farmIds = [this.farmId];
    if (this.outload) {
      let stockOwner = get(this.outload, 'ngr.companyId')
      _companyIds = stockOwner ? [stockOwner] : [];
      _farmIds = []
    }
    else {
      if (movement && !get(this.props, 'movement.commodityContract')) {
        _companyIds = uniq(compact([
          ..._companyIds,
          get(movement, 'customer.companyId'),
          get(movement, 'commodityContract.seller.companyId'),
          get(movement, 'commodityContract.buyer.companyId')
        ]));
        _farmIds = uniq(compact([
          ..._farmIds,
          get(movement, 'freightPickup.consignor.handlerId'),
          get(movement, 'freightDelivery.consignee.handlerId')
        ]));
      }
      else if (get(this.props, 'movement.commodityContract')) {
        _companyIds = [get(this.props, 'movement.commodityContract.seller.companyId')];
        _farmIds = [];
      }
    }
    const companyQueryString = map(_companyIds, id => { return `company_ids=${id}`; }).join('&');
    const farmQueryString = map(_farmIds, id => { return `farm_ids=${id}`; }).join('&');
    if ((companyQueryString || farmQueryString) && !get(this.state, 'queryParams.ngrId')) {
      const apiPath = APIService.ngrs().appendToUrl(`?${companyQueryString}&${farmQueryString}`);
      apiPath.get(token).then(ngrs => {
        this.setState(
          { ngrs: ngrs },
          () => {
            if (!this.outload) {
              if (movement) {
                const newState = { ...this.state };
                newState.models.ngrId.value = movement?.customer?.ngrId;
                newState.selectedNgr = movement?.customer?.ngr
                this.setState(newState);
              }
              else if (ngrs.length == 1) {
                const newState = { ...this.state };
                newState.models.ngrId.value = ngrs[0].id;
                this.setState(newState);
              }
            }
          },
        );
      });
    } else this.getNgr();
  }

  getNgr() {
    const { token } = this.props;
    const ngrId = get(this.state, 'queryParams.ngrId');
    if (ngrId && !this.outload) {
      APIService.ngrs(ngrId).get(token).then(ngr => {
        const newState = { ...this.state }
        if (ngr) {
          newState.models.ngrId.value = ngr.id
        }
        newState.selectedNgr = ngr
        this.setState(newState);
      });
    }
  }

  isNGRFieldDisabled() {
    return Boolean(
      get(this.props, 'movement.commodityContract.seller.ngrId')
    ) && false;
  }

  getSelectedNGRForForm() {
    const ngrFromOutload = get(this.outload, 'ngr');
    return (ngrFromOutload && isObject(ngrFromOutload) ? ngrFromOutload : null) ||
      get(this.state, 'selectedNgr') ||
      get(this.props, 'movement.commodityContract.seller.ngr') ||
      find(this.state.ngrs, { id: get(this.outload, 'ngrId') });
  }

  getNGRNumber() {
    const ngrFromOutload = get(this.outload, 'ngr');
    return get(this.getSelectedNGRForForm(), 'ngrNumber') ||
      (isString(ngrFromOutload) ? ngrFromOutload : undefined);
  }

  getNGRId() {
    return get(this.getSelectedNGRForForm(), 'id') || get(this.outload, 'ngrId');
  }

  getStorage() {
    if (this.props.storageId) {
      APIService.storages(this.props.storageId)
        .appendToUrl('?no_stocks&amend_archived')
        .get(this.props.token)
        .then(storage => {
          this.setState(
            state => ({ ...state, storage }),
            () => this.updateValidatorsBasedOnStorage(),
          );
        });
    } else if (this.props.movement) {
      const site = get(this.props.movement, 'freightPickup.consignor.sites[0].location');
      if (site && site.entity === 'storage') {
        this.setState(
          state => ({ ...state, storage: site }),
          () => this.updateValidatorsBasedOnStorage(),
        );
      }
    }
  }

  handleConsignorChange = (item) => {
    const newState = { ...this.state };
    const value = item?.id
    newState.models.consignor.handlerId.value = value;
    newState.models.consignor.handlerId.errors = this.isRestrictedToEditForSite(item) ? [LOAD_CREATE_OR_EDIT_ERROR_MESSAGE.replace('$action', 'created')] : [];
    if (item) {
      newState.selectedConsignor = item;
      newState.models.storageId.validators = [selected(), required()];
    } else {
      newState.selectedConsignor = undefined;
      newState.sites = [];
      newState.models.storageId.validators = [];
    }
    this.setState(newState, () => {
      if (this.state.selectedConsignor?.id)
        this.getConsignorSites(this.state.selectedConsignor.id);
      if (!isSystemCompany())
        this.setFieldErrors('models.consignor.handlerId');
      this.setFieldErrors('models.storageId');
      this.setVarietyMandatory();
      this.setSellerAvailableTonnage();
    });
  };

  getConsignorSites = async (handlerId) => {
    let sites = [];
    let queryString = 'storages/home/<USER>';
    if (get(this.outload, 'storageId') || this.storageId)
      queryString = queryString + `&storageId=${get(this.outload, 'storageId', this.storageId)}`;
    if (this.props.movement && get(this.props.movement, 'typeId') !== PACK_ORDER_TYPE_ID)
      queryString = queryString + '&with_containers=true';
    let homeStorages = await APIService
      .farms(handlerId)
      .appendToUrl(queryString)
      .get(this.props.token);

    homeStorages = map(homeStorages, storage => {
      return merge(storage, {
        id: storage.id,
        entity: 'storage',
        storageType: 'home',
      });
    });

    if (this.props.movement) {
      let farmFields = await APIService
        .farms(handlerId)
        .farm_fields()
        .get(this.props.token);

      farmFields = map(farmFields, ff => {
        return merge(ff, { entity: 'farm_field' });
      });

      sites = farmFields.concat(homeStorages);
    }
    else
      sites = homeStorages;
    if (this.props.movement && get(this.outload, 'farmFieldId') && !get(this.outload, 'farmField.isActive')) {
      let farmField = find(sites, { id: get(this.outload, 'farmFieldId'), entity: 'farm_field' })
      if (!farmField) {
        farmField = get(this.outload, 'farmField');
        if (farmField) {
          farmField['entity'] = 'farm_field';
          sites.push(farmField);
        }
      }
    }
    let selectedStorage = find(sites, { id: get(this.outload, 'siteId') }) || this.state.selectedSite;
    if (get(this.outload, 'farmFieldId'))
      selectedStorage = find(sites, { id: this.outload.farmFieldId, entity: 'farm_field' });
    if (this.props.storageId && !selectedStorage)
      selectedStorage = find(sites, { id: this.props.storageId });
    if (this.storageId && !selectedStorage)
      selectedStorage = find(sites, { id: this.storageId });
    if (!selectedStorage) {
      let storageId = get(this.state, 'models.storageId.value');
      if (storageId) {
        selectedStorage = find(sites, { id: storageId });
      }
    }

    this.setState({
      sites: sites, selectedSite: selectedStorage,
      models: {
        ...this.state.models,
        storageId: {
          ...this.state.models.storageId,
          value: get(selectedStorage, 'id')
        }
      }
    });
  };

  async getSites(isMounting = false) {
    let consignor = null;
    let storageId = this.outload?.storageId || this.storageId;
    let farmFieldId = this.outload?.farmFieldId;
    let handlerId = this.outload?.farmId
    let storage = this.outload?.storage
    if (this.props.movement) {
      consignor = this.props.movement?.freightDelivery?.consignor
      handlerId = handlerId || consignor?.handlerId
    }
    let sites = [];
    if (handlerId) {
      let queryString = 'storages/home/<USER>';
      if (storageId)
        queryString = queryString + `&storageId=${storageId}`;
      if (this.props.movement)
        queryString = queryString + '&with_containers=true';
      let homeStorages = await APIService
        .farms(handlerId)
        .appendToUrl(queryString)
        .get(this.props.token);
      let farmFields = await APIService
        .farms(handlerId)
        .farm_fields()
        .get(this.props.token);
      homeStorages = map(homeStorages, storage => {
        return merge(storage, {
          id: storage.id,
          entity: 'storage',
          storageType: 'home',
        });
      });

      farmFields = map(farmFields, ff => {
        return merge(ff, { entity: 'farm_field' });
      });
      sites = farmFields.concat(homeStorages);
    }
    if (!isEqual(this.state.sites, sites)) {
      this.setState({ sites: sites }, () => {
        const newState = { ...this.state };
        var site = null;
        if (storageId) {
          site = find(this.state.sites, {
            id: storageId,
            entity: storage?.entity || 'storage',
            storageType: storage?.storageType || 'home',
          });
          newState.models.storageId.value = this.outload?.bhcSite ? this.outload.bhcSite.id : storageId;
        } else if (farmFieldId) {
          newState.models.storageId.value = farmFieldId;
          newState.models.farmFieldId.value = farmFieldId;
          site = find(this.state.sites, {
            id: farmFieldId,
            entity: 'farm_field',
          });
        } else {
          if (this.props.optionType !== OPTION_TYPE_WEB_SPLIT_LOADS || !isMounting)
            newState.models.storageId.value = storageId || storage?.id || get(consignor, 'sites[0].locationId');
          site = find(this.state.sites, {
            id: newState.models.storageId.value,
            entity: get(consignor, 'sites[0].locationContentType'),
          });
        }
        newState.initialStorageId = newState.models.storageId.value;
        newState.selectedSite = site;
        newState.storage = site;

        this.setState(newState, () => {
          this.updateValidatorsBasedOnStorage();
          this.getModelSpecsByStorageSpecs({});
        });
      });
    }
  }

  updateValidatorsBasedOnStorage(callback) {
    const newState = { ...this.state };
    this.changeFreightPickupTareWeightGrossWeightValidators(newState);
    this.changeFreightPickupGradeValidators(newState);
    this.updateDependentValidatorsByStorage(newState);
    this.setState(newState, () => {
      if (callback) {
        callback();
      }
    });
  }

  isSelectedSiteNotFarmField = () => {
    return get(this.state, 'selectedSite.entity') !== 'farm_field';
  };

  handleSiteChange(value, id, item) {
    const newState = { ...this.state };
    newState.models.ngrId.validators = queryString.parse(this.props.location.search).ngrId ? [] : [required()];
    if (item) {
      if (item.entity === 'farm_field') {
        newState.models.ngrId.validators = [];
        newState.models.ngrId.errors = [];
      }
      newState.selectedSite = item;
      newState.storage = item;
      newState.models.storageId.value = item.id;
    } else {
      newState.selectedSite = undefined;
      newState.storage = undefined;
      newState.models.storageId.value = value;
    }
    this.setState(newState, () => {
      this.getTonnageFieldsStates();
      this.updateValidatorsBasedOnStorage();
      this.getModelSpecsByStorageSpecs({});
      this.setFieldErrors('models.storageId');
      this.setSellerAvailableTonnage();
    });
  }


  updateDependentValidatorsByStorage(newState) {
    if (this.isSelectedSiteNotFarmField()) {
      newState.models.season.validators = [required()];
      newState.models.gradeId.validators = [required()];
    } else {
      newState.models.season.validators = [];
      newState.models.gradeId.validators = [];
      newState.models.season.errors = [];
      newState.models.gradeId.errors = [];
    }
  }

  handleBlur = event => {
    this.setFieldErrors(event.target.id, event.target.value);
  };

  handleEstimatedNetWeightFieldChange = event => {
    let value = event.target.value
    if (isString(value))
      value = value.replaceAll(',', '').replaceAll(' ', '')
    this.setFieldValue(`models.${event.target.id}`, value, true, (newState) => {
      this.checkNetWeightAgainstCurrentTonnage();
      if (parseFloat(newState.models.estimatedNetWeight.value) > 0 && !this.validateWeightFields('pack')) {
        newState.models.containerGross.value = (parseFloat(newState.models.estimatedNetWeight.value) + parseFloat(newState.models.containerTare.value || 0)).toFixed(2);
        this.setState(newState);
      }
    });
  };

  handleNetWeightChange = event => {
    let value = event.target.value
    if (isString(value))
      value = value.replaceAll(',', '').replaceAll(' ', '')
    this.setFieldValue(`models.${event.target.id}`, value, true, (newState) => {
      if (parseFloat(newState.models.netWeight.value) > 0 && !this.validateWeightFields('container')) {
        newState.models.grossWeight.value = (parseFloat(newState.models.netWeight.value) + parseFloat(newState.models.tareWeight.value || 0)).toFixed(2);
        this.setState(newState);
      }
    });
  };

  handleNetCargoChange = event => {
    let value = event.target.value
    if (isString(value))
      value = value.replaceAll(',', '').replaceAll(' ', '')
    this.setFieldValue(`models.${event.target.id}`, value, true, (newState) => {
      if (parseFloat(newState.models.netWeight.value) > 0 && !this.validateWeightFields('container')) {
        newState.models.tareWeight.value = undefined;
        newState.models.grossWeight.value = undefined;
        this.setState(newState);
      }
    });
  }

  recalculateNetCargo = newState => (parseFloat(newState.models.grossWeight.value || 0) - parseFloat(newState.models.tareWeight.value || 0) - parseFloat(newState.models.containerTare.value || 0)).toFixed(2)

  isNetWeightMoreThanAllowed() {
    if (!get(this.props, 'movement.commodityContract') || !get(this.props, 'movement.order')) {
      return false;
    }
    const weightId = get(this.props, 'movement.commodityContract.weightId', DEFAULT_WEIGHT_ID);
    let maxTonnageAllowed = this.getMaxAllowedTonnage('netWeight')
    if (weightId === WEIGHT_ORIGIN_ID) {
      return this.getTonnage() > maxTonnageAllowed;
    } else {
      return false;
    }
  }

  isQuantityMoreThanAllowed() {
    if (!get(this.props, 'movement.commodityContract') || !get(this.props, 'movement.order')) {
      return false;
    }
    const weightId = get(this.props, 'movement.commodityContract.weightId', DEFAULT_WEIGHT_ID);
    let maxQuantityAllowed = this.getMaxAllowedTonnage('quantity')
    if (weightId === WEIGHT_ORIGIN_ID) {
      return this.getQuantity() > maxQuantityAllowed;
    } else {
      return false;
    }
  }

  getTonnage() {
    const grossWeight = this.state.models.grossWeight.value;
    const tareWeight = this.state.models.tareWeight.value;
    const estimatedNetWeight = this.state.models.estimatedNetWeight.value;
    const tonnage = grossWeight && tareWeight ?
      parseFloat(grossWeight) - parseFloat(tareWeight) :
      estimatedNetWeight ? parseFloat(estimatedNetWeight) : null;

    return tonnage ? parseFloat(parseFloat(tonnage).toFixed(2)) : tonnage;
  }

  getQuantity() {
    return this.state.models.quantity.value;
  }

  getSelectedCommodity = commodityId => {
    const id = commodityId || this.state.models.commodityId.value
    return id ? find(this.props.commodities, { id: id }) : null
  }

  hasQuantityBasedCommodity = commodityId => Boolean(this.getSelectedCommodity(commodityId || this.state.models.commodityId.value)?.isQuantityBased)

  isStrictQuantityBased = () => get(this.getSelectedCommodity(), 'isStrictQuantityBased')

  quantityLabel = () => get(this.getSelectedCommodity(), 'quantityLabel')

  quantityUnit = () => get(this.getSelectedCommodity(), 'unit')

  voidLoad() {
    if (this.isRestrictedToEditForSite(this.state.selectedConsignor)) {
      alertifyjs.error("Inturns into and Outturns from this site can only be created/edited by the company employees. Please contact the site manager for creating this load.", 5);
    } else {
      alertifyjs.confirm(
        'Warning',
        'Load will be marked Void. Do you want to proceed?',
        () => {
          APIService
            .loads(this.outload?.id)
            .appendToUrl('void/')
            .put().then(() => {
              alertifyjs.success('Load Deleted');
              window.location.reload();
              this.props.closeDrawer;
            });
        },
        () => { },
      );
    }
  }

  priceUnit = () => this.props.movement ? this.getTruckUnit() : getCountryDisplayUnit();

  applicationCommodityUnit(commodity, commodityId) {
    if (!commodity && commodityId && !isEmpty(this.props.commodities))
      commodity = find(this.props.commodities, { id: commodityId });
    return get(UNIT_ABBREVIATIONS, commodity?.unit);
  }

  getTruckUnit = () => get(find(this.state.trucks, { id: this.state.models.truckId.value }), 'unit') || this.state.unit;

  setSelectedProviderTruck = truck => {
    const newState = { ...this.state };
    newState.selectedTrucks.push(truck);
    newState.models.truckId.value = get(truck, 'id', null)
    if(this.isCustomisedPackAndShip)
      newState.models.tareWeight.value = get(truck, 'estimatedTruckTare', null)
    this.setState(newState);
  };

  handleTruckDetailsChange = event => {
    const newState = { ...this.state }
    let id = event.target.id;
    set(newState, `${id}.value`, id === 'models.restricted' ? event.target.checked : event.target.value);
    if (id !== 'models.restricted' && event.target.value) {
      const parts = id.split(".")
      const errorField = `truckErrors.${parts[1]}`;
      set(newState, errorField, '')
    }
    this.setState(newState);
  }

  handleTruckDetailsSelectValueChange(value, id) {
    const newState = { ...this.state };
    set(newState, `${id}.value`, value);
    const parts = id.split(".")
    const errorField = `truckErrors.${parts[1]}`;
    set(newState, errorField, '')
    this.setState(newState);
  }

  getEntityUrl() {
    const reason = get(this.props, 'movement.reason')
    const canAmendRelatedEntity = get(this.props, 'movement.canAmendRelatedEntity');
    if (reason) {
      let url = ''
      let identifier = ''
      if (reason === 'contract') {
        identifier = get(this.props, 'movement.order.commodityContract.contractNumber')
        url = canAmendRelatedEntity ? <a rel="noopener noreferrer" target='_blank' href={`#/contracts/${get(this.props, 'movement.order.commodityContract.id')}/edit`} onClick={event => window.open(event.target.href, '_blank')}>{identifier}</a> : identifier;
      }
      else if (reason === 'pickup order') {
        identifier = get(this.props, 'movement.order.freightPickup.orderNumber')
        url = canAmendRelatedEntity ? <a rel="noopener noreferrer" target='_blank' href={`#/freights/orders/${get(this.props, 'movement.order.pickupOrderId')}/edit`} onClick={event => window.open(event.target.href, '_blank')}>{identifier}</a> : identifier;
      }
      else if (reason === 'delivery order') {
        identifier = get(this.props, 'movement.order.freightDelivery.orderNumber')
        url = canAmendRelatedEntity ? <a rel="noopener noreferrer" target='_blank' href={`#/freights/orders/${get(this.props, 'movement.order.deliveryOrderId')}/edit`} onClick={event => window.open(event.target.href, '_blank')}>{identifier}</a> : identifier;
      }
      else if (reason === 'parent order') {
        identifier = get(this.props, 'movement.order.parentOrder.identifier')
        url = canAmendRelatedEntity ? <a rel="noopener noreferrer" target='_blank' href={`#/freights/orders/${get(this.props, 'movement.order.parentOrder.id')}/edit`} onClick={event => window.open(event.target.href, '_blank')}>{identifier}</a> : identifier;
      }
      return url
    }
  }

  onChemicalApplicationCommodityChange(obj, value) {
    let commodity = undefined
    if (value)
      commodity = find(this.props.commodities, { id: value });
    this.setState(prevState => ({
      chemicalApplications: prevState.chemicalApplications.map(chemicalApplication => {
        if (chemicalApplication.id == obj.id) {
          let commodityErrors = [];
          if (prevState.chemicalApplications.some(_chemicalApplication => _chemicalApplication.commodityId === value && _chemicalApplication.id !== obj.id)) {
            commodityErrors.push('This application is already defined.')
          }
          return { ...chemicalApplication, commodityId: value, commodity: commodity };
        }
        return { ...chemicalApplication }
      })
    }));
  }

  handleApplicationRateChange(obj, value) {
    this.setState(prevState => ({
      chemicalApplications: prevState.chemicalApplications.map(chemicalApplication => {
        if (chemicalApplication.id == obj.id)
          return { ...chemicalApplication, applicationRate: value };
        return { ...chemicalApplication }
      })
    }));
  }

  handleChemicalApplicationStorageChange(value, item, obj) {
    this.setState(prevState => ({
      chemicalApplications: prevState.chemicalApplications.map(chemicalApplication => {
        if (chemicalApplication.id == obj.id)
          return { ...chemicalApplication, storageId: value };
        return { ...chemicalApplication }
      })
    }));
  }

  onChemicalApplicationDelete(obj) {
    const newState = { ...this.state };
    let chemicalApplications = [...newState.chemicalApplications.filter(chemicalApplication => chemicalApplication.id !== obj.id)];
    newState.chemicalApplications = chemicalApplications.map((chemicalApplication, index) => ({ ...chemicalApplication, id: index + 1 }));
    this.setState(newState)
  }

  onChemicalApplicationAdd() {
    const newState = { ...this.state };
    const chemicalApplications = (isEmpty(get(this.props.movement, 'chemicalApplications')) ? get(this.props.movement, 'orderChemicalApplications') : get(this.props.movement, 'chemicalApplications')) || [];
    const obj = find(chemicalApplications, { gradeId: this.state.models.gradeId.value })
    let commodityErrors = [];
    const commodityId = get(obj, 'commodityId');
    if (commodityId && this.state.chemicalApplications.some(chemicalApplication => chemicalApplication.commodityId === commodityId))
      commodityErrors = ['This application is already defined.']
    const newObj = {
      id: newState.chemicalApplications.length + 1,
      commodityIdOptions: chemicalApplications.map(chemicalApplication => chemicalApplication.commodityId),
      commodityId: commodityId,
      commodity: find(this.props.commodities, { id: get(obj, 'commodityId') }),
      applicationRate: get(obj, 'applicationFee'),
      storageId: undefined,
      errors: [],
      commodityErrors: commodityErrors
    }
    newState.chemicalApplications = [...newState.chemicalApplications, newObj];
    this.setState(newState)
  }

  getEligibleApplicationRates(commodityId) {
    let eligibleApplicationRates = [];
    if (commodityId && !isEmpty(this.state.chemicalApplicationRates)) {
      let _eligibleApplicationRates = this.state.chemicalApplicationRates.filter(chemicalApplicationRate => chemicalApplicationRate.commodityId === commodityId);
      if (!isEmpty(_eligibleApplicationRates)) {
        _eligibleApplicationRates.forEach(_eligibleApplicationRate => {
          eligibleApplicationRates.push({ 'name': _eligibleApplicationRate.rate, 'id': _eligibleApplicationRate.rate })
        })
      }
    }
    return eligibleApplicationRates;
  }

  isWeighingAtDestination = () => get(this.props, 'movement.commodityContract.weightId', DEFAULT_WEIGHT_ID) === WEIGHT_DESTINATION_ID

  isFieldDisabled = field => {
    const isCommodityContractInvoiced = this.props.movement?.isCommodityContractInvoiced
    const isWeighingAtDestination = this.isWeighingAtDestination()
    if (isWeighingAtDestination)
      return false
    if (isCommodityContractInvoiced && ['consignor.handlerId', 'gradeId', 'varietyId', 'stockOwner', 'ngrId', 'specs.coil', 'specs.impu', 'tareWeight', 'grossWeight', 'netWeight'].includes(field))
      return true
    return false
  }

  setTruckOptions = (options, inputText) => this.setState({trucks: options, inputText: inputText})

  render() {
    const { movement } = this.props;
    const { specs } = this.state.models;
    const isShrinkageLoad = this.checkShrinkageLoad();
    const {
      selectedConsignor,
      seasonFloatingLabelText, ngrFloatingLabelText, ngrs, storage,
      selectedSite, commoditySpecs,
      gradeFloatingLabelText, sites,
      stockOwner, selectedNgr, isVarietyMandatoryLabel,
      selectedStockOwner, selectedConsignee
    } = this.state;
    const archivedStorage = find(sites, { 'storageId': get(this.outload, 'storageId', this.storageId), 'isActive': false }) || false;
    const estimatedNetWeightError = get(this.state.models, 'estimatedNetWeight.errors[0]', '');
    const ngrFieldError =
      get(this.state.models, 'ngrId.errors[0]', '') ||
      (movement && !this.state.models.ngrId.value ? NGR_REQUIRED_FOR_STOCKS_WARNING_MESSAGE : '');
    const isSelectedSiteFFAndWeighingAtDestination = get(selectedSite, 'entity') === 'farm_field' && get(movement, 'commodityContract.weightId') !== WEIGHT_ORIGIN_ID;
    const completedMovement = movement?.status === 'completed' && !isSystemCompany();
    const completedAndInvoicedMovement = Boolean(movement?.status === 'completed' && movement.isAnyInvoiced);
    const isStockUpdateLoad = includes(STORAGE_STOCK_EMPTY_UPDATE_OPTION_TYPES, get(this.outload, 'optionType'));
    const styleAdornment = (completedAndInvoicedMovement || isShrinkageLoad || isStockUpdateLoad) ? { color: 'rgb(162,162,162)', paddingRight: '32px' } : { color: 'rgb(162,162,162)' };
    const unit = this.priceUnit();
    const isDateTimeDisabled = completedAndInvoicedMovement || isShrinkageLoad || isStockUpdateLoad;
    const parentTonnage = min([get(this.props, 'movement.maxAllowedTonnageOnOrder'), get(this.props, 'movement.parentTotalTonnageWithTolerance')]);
    const maxParentTonnage = get(this.props, 'movement.maxAllowedTonnageOnOrder') || get(this.props, 'movement.parentTotalTonnageWithTolerance');
    const updateToTonnage = (parentTonnage + parseFloat(get(this.state.models, 'estimatedNetWeight.value', 0)) - this.getMaxAllowedTonnage());
    const entityName = get(this.props, 'movement.orderId') ? 'order' : 'contract';
    const isParentOrderIndependent = get(this.props, 'movement.isParentOrderIndependent');
    const isDirectContractMovement = !get(this.props, 'movement.orderId') && get(this.props, 'movement.commodityContractId');
    let showUpdateToTonnagePopup = this.state.showAmendTonnagePopup && !isParentOrderIndependent;
    if (showUpdateToTonnagePopup && isDirectContractMovement && get(this.props, 'movement.canAmendRelatedEntity'))
      showUpdateToTonnagePopup = true;
    else if (showUpdateToTonnagePopup && parseFloat(updateToTonnage) < get(this.props, 'movement.maxAllowedTonnageOnOrder'))
      showUpdateToTonnagePopup = true
    else
      showUpdateToTonnagePopup = false
    const parentEntityUrl = this.getEntityUrl();
    const amendMessage = get(this.props, 'movement.canAmendRelatedEntity') ? `Please amend ${get(this.props, 'movement.reason')} first.` : `Please contact the relevant party to amend ${get(this.props, 'movement.reason')}.`;
    const isSiteRestricted = this.outload && this.isRestrictedToEditForSite(this.state.selectedConsignor)
    const isStrictQuantityBasedCommodity = this.isStrictQuantityBased();
    const hideGrossAndNetWeightFields = this.hideGrossAndNetWeightFields()
    const containerMovement = this.props.movement?.containerMovement
    return (
      <div>
        <form onSubmit={this.handleSubmit} noValidate>
          <div className='cardForm cardForm--drawer'>
            <div className='cardForm-content row trucks col-xs-12 padding-reset'>
              <div className='col-xs-8 form-wrap' style={{ display: 'inline-block', marginTop: '16px' }}>
                <SiteAsyncAutocomplete
                  farmId={this.farmId || this.props.farmId}
                  variant='standard'
                  id='consignor.handlerId'
                  label='Pickup Site'
                  onChange={this.handleConsignorChange}
                  selected={selectedConsignor?.id ? { ...selectedConsignor, name: selectedConsignor.displayName || selectedConsignor.name } : null}
                  minLength={3}
                  errorText={this.state.models.consignor.handlerId.errors[0]}
                  style={{ float: 'right' }}
                  disabled={this.isFieldDisabled('consignor.handlerId') || isSiteRestricted || archivedStorage}
                  activeSitesOnly
                  includeSelectedSiteId={get(movement, 'freightPickup.consignor.handlerId')}
                  fetchTopSitesOnClear
                />
              </div>
              <div className='col-xs-4 form-wrap' style={{ display: 'inline-block' }}>
                <CommonAutoSelect
                  items={selectedConsignor?.id ? sites : []}
                  id='storageId'
                  label='Pickup Storage'
                  value={get(this.state.models, 'storageId.value', '')}
                  errorText={get(this.state.models, 'storageId.errors[0]', '')}
                  onChange={this.handleSiteChange}
                  selectedItem={selectedSite}
                  dontAutoselectSingleItem
                  disabled={archivedStorage}
                  storageFieldMix
                />
              </div>
              { !get(this.props, 'movement.order') && (
                <div className="col-sm-12 form-wrap-70" style={{marginTop: '-25px'}}>
                  <Autocomplete
                    fullWidth
                    options={this.state.matchingPackOrders}
                    value={this.state.matchingPackOrderId.value && find(this.state.matchingPackOrders, {id: this.state.matchingPackOrderId.value})}
                    id="matchingPackOrderId"
                    style={{float: 'left'}}
                    required
                    getOptionLabel={option => get(option, 'displayName')}
                    loading={this.state.isFetchingOrders && !this.state.matchingPackOrderId.value}
                    loadingText="Loading..." 
                    renderInput={params => (
                      <TextField
                        {...params}
                        label='Pack Order'
                        error={!isEmpty(get(this.state.matchingPackOrderId, 'errors', ''))}
                        helperText={get(this.state.matchingPackOrderId, 'errors', '')}
                        fullWidth
                        variant='standard'
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                              <>
                                  {this.state.isFetchingOrders && !this.state.matchingPackOrderId.value ? (
                                      <CircularProgress color="inherit" size={20} />
                                  ) : null}
                                  {params.InputProps.endAdornment}
                              </>
                          ),
                      }}
                      />
                    )}
                    onChange={(event, value) => this.setMatchingPackOrder(value ? value.id : null)}
                 />
                </div>
              )
              }
              <React.Fragment>
                <div className="col-xs-4 form-wrap">
                  <CommonTextField
                    id='containerNumber'
                    label='Container Name'
                    placeholder='Container Name'
                    value={this.state.models.containerNumber.value}
                    maxLength='255'
                    disabled
                  />
                </div>
                <div className={'col-xs-4 form-wrap'}>
                  <CommonDatePicker
                    id='date'
                    onChange={this.handleSelectFieldChange}
                    value={this.state.models.date.value}
                    errorText={get(this.state.models, 'date.errors[0]', '')}
                    maxDate={
                      get(movement, 'freightDelivery.inload') ?
                        new Date(movement.freightDelivery.inload.date) :
                        new Date()
                    }
                    disabled={isDateTimeDisabled}
                  />
                </div>
                <div className={'col-xs-4 form-wrap'}>
                  <CommonTimePicker
                    id='time'
                    onChange={this.handleSelectFieldChange}
                    value={this.state.models.time.value}
                    errorText={get(this.state.models, 'time.errors[0]', '')}
                    disabled={isDateTimeDisabled}
                  />
                </div>
              </React.Fragment>

              <div className='col-xs-4 form-wrap'>
                <CommodityAutoComplete
                  id='commodityId'
                  commodityId={this.state.models.commodityId.value}
                  onChange={this.handleCommodityChange}
                  errorText={get(this.state.models, 'commodityId.errors[0]', '')}
                  disabled={!!movement || completedMovement || isShrinkageLoad || isStockUpdateLoad}
                />
              </div>
              <div className='col-xs-4 form-wrap'>
                <SeasonSelect
                  id='season'
                  floatingLabelText={this.isSelectedSiteNotFarmField() ? seasonFloatingLabelText : 'Season (Optional)'}
                  season={this.state.models.season.value}
                  onChange={this.handleSelectFieldChange}
                  errorText={get(this.state.models, 'season.errors[0]', '')}
                  includeEmptyOption={!this.isSelectedSiteNotFarmField()}
                  disabled={!this.isCustomisedPackAndShip && movement.season}
                />
              </div>
              <div className='col-xs-4 form-wrap'>
                <GradeAutoComplete
                  id='gradeId'
                  floatingLabelText={this.isSelectedSiteNotFarmField() ? gradeFloatingLabelText : 'Grade (Optional)'}
                  commodityId={this.state.models.commodityId.value}
                  season={this.state.models.season.value}
                  selectedGradeId={this.state.models.gradeId.value}
                  selectedVarietyId={this.state.models.varietyId.value}
                  onChange={this.handleGradeChange}
                  errorText={get(this.state.models, 'gradeId.errors[0]', '')}
                  disabled={this.isCustomisedPackAndShip ? !this.isCustomisedPackAndShip : this.isFieldDisabled('gradeId')}
                  specs={mapValues(specs, 'value')}
                  dependsOnSeason
                />
              </div>
              <div className='col-xs-4 form-wrap'>
                <VarietyAutoComplete
                  id='varietyId'
                  commodityId={this.state.models.commodityId.value}
                  dependsOnCommodity
                  onChange={this.handleVarietyChange}
                  errorText={get(this.state.models, 'varietyId.errors[0]', '')}
                  label={isVarietyMandatoryLabel ? 'Variety' : 'Variety (Optional)'}
                  varietyId={this.state.models.varietyId.value}
                  disabled={this.isFieldDisabled('varietyId') || isShrinkageLoad}
                />
              </div>
              {
                this.isNGRFieldDisabled() ?
                  <div className='col-xs-4 form-wrap'>
                    <CommonTextField
                      id='ngrId'
                      label='NGR'
                      value={this.getNGRNumber()}
                      disabled
                    />
                  </div> :
                  <React.Fragment>
                    <div className='col-xs-4 form-wrap'>
                      <CompanyAutocomplete
                        variant='standard'
                        id='company'
                        label='Stock Owner'
                        companyId={stockOwner.value || get(selectedNgr, 'companyId')}
                        selected={selectedStockOwner}
                        onChange={this.onCompanyChange}
                        urlPath='directory/names/'
                        queryParams={{ include_self: true, excludeGroups: true }}
                        minLength={3}
                        style={{ float: 'right' }}
                        errorText={stockOwner.errors[0]}
                        disabled={this.isFieldDisabled('stockOwner')}
                        filterFunc={companies => filter(companies, company => {
                          if (get(this.outload, 'ngr.companyId') && get(this.props.movement, 'commodityContract.buyer.companyId') == get(this.props, 'outload.ngr.companyId'))
                            return true
                          else
                            return company.id != get(this.props.movement, 'commodityContract.buyer.companyId');
                        })}
                      />
                    </div>
                    <div className='col-xs-4 form-wrap'>
                      <CommonSelect
                        id='ngrId'
                        style={{textAlign: "left"}}
                        floatingLabelText={stockOwner.value ? ngrFloatingLabelText : 'NGR (Select Stock Owner)'}
                        items={stockOwner.value ? ngrs : []}
                        selectConfig={{ value: 'id', text: 'ngrNumber' }}
                        onChange={this.handleSelectFieldChange}
                        errorText={ngrFieldError}
                        value={this.state.models.ngrId.value || get(selectedNgr, 'id')}
                        disabled={this.isFieldDisabled('ngrId') || this.props.movement?.order}
                      />
                    </div>
                  </React.Fragment>
              }

              {
                movement &&
                <div className='col-xs-4 form-wrap'>
                  <CommonTextField
                    id='containerTare'
                    label='Container Tare'
                    placeholder='Please enter'
                    value={this.state.models.containerTare.value}
                    onChange={this.handleContainerTareChange}
                    onKeyDown={event => positiveDecimalFilter(event, 2, *********.99)}
                    disabled={this.isFieldDisabled('tareWeight')}
                    helperText={get(this.state.models, 'containerTare.errors[0]', '')}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position='end' style={styleAdornment}>
                          {unit}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              }
              {
                movement && !hideGrossAndNetWeightFields &&
                <div className='col-xs-4 form-wrap'>
                  <CommonTextField
                    id='containerGross'
                    label='Container Gross'
                    placeholder='Please enter'
                    value={this.state.models.containerGross.value}
                    onChange={this.handleContainerGrossChange}
                    onKeyDown={event => positiveDecimalFilter(event, 2, *********.99)}
                    disabled={this.isFieldDisabled('grossWeight')}
                    helperText={get(this.state.models, 'containerGross.errors[0]', '')}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position='end' style={styleAdornment}>
                          {unit}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              }
              {
                !isStrictQuantityBasedCommodity && !hideGrossAndNetWeightFields &&
                <div className='col-xs-4 form-wrap'>
                  <NumberField
                    id='estimatedNetWeight'
                    label='Net Weight'
                    placeholder='Net Weight'
                    value={this.state.models.estimatedNetWeight.value}
                    disabled={this.isFieldDisabled('netWeight')}
                    helperText={estimatedNetWeightError || get(this.state, 'sellerAvailableTonnageMessage')}
                    maxValue={*********.99}
                    onChange={this.handleEstimatedNetWeightFieldChange}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position='end' style={styleAdornment}>
                          {unit}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              }
              <div className='col-xs-4 form-wrap'>
                <NumberField
                  id='numberOfBags'
                  label={`Number of Bags ${this.currentCompany?.impexDocsEnabled ? '' : ' (Optional)'}`}
                  disabled={this.isFieldDisabled('numberOfBags')}
                  placeholder='Please enter'
                  value={this.state.models.numberOfBags.value}
                  onChange={this.handleNumberOfBagsFieldChange}
                  maxValue={*********.99}
                  helperText={get(this.state.models, 'numberOfBags.errors[0]', '')}
                />
              </div>
              {
                this.hasQuantityBasedCommodity() && !hideGrossAndNetWeightFields &&
                <div className='col-xs-4 form-wrap'>
                  <NumberField
                    id='quantity'
                    label={this.quantityLabel()}
                    disabled={this.isFieldDisabled('netWeight')}
                    placeholder='Please enter'
                    value={this.state.models.quantity.value}
                    onChange={this.handleQuantityFieldChange}
                    maxValue={*********.99}
                    helperText={get(this.state.models, 'quantity.errors[0]', '')}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position='end' style={{ color: 'rgb(162,162,162)' }}>
                          {this.quantityUnit()}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              }
              { map(this.state.models.sealNumbers.value || [], (sealNumber, index) => (
                <div key={index} className='col-xs-4 form-wrap'>
                  <CommonTextField
                    id={index}
                    label={`Seal ${index + 1} Number (Optional)`}
                    value={sealNumber.value}
                    onChange={this.handleSealChange}
                  />
                </div>
              ))}
              { !isEmpty(commoditySpecs) &&
              <SpecParametersValue
                commoditySpecs={commoditySpecs}
                fieldsSpecs={specs}
                onChange={this.handleSpecsChange}
                errorTexts={mapValues(specs, spec => get(spec, 'errors[0]', ''))}
                warningTexts={mapValues(specs, spec => get(spec, 'warnings[0]', ''))}
                clearIndex={get(storage, 'isOwner') ? [0] : [2]}
                showSecondarySpecs
              />
              }
              <div className='col-xs-12 form-wrap'>
                <CommonTextField
                  id='comment'
                  label='Comment'
                  value={this.state.models.comment.value}
                  onChange={this.handleFieldChange}
                  multiline
                  rows={3}
                  variant='outlined'
                />
              </div>
              {get(movement, 'isBlended') &&
                <React.Fragment>
                  <div className="col-xs-12" style={{ paddingRight: '0px', marginBottom: '30px' }}>
                    <h4 style={{ marginBottom: '0px' }}>Applications</h4>
                    {
                      map(this.state.chemicalApplications, (obj, index) => (
                        <div key={index}>
                          <div className="col-xs-12" style={{ padding: '0px 0px 0px 0px', marginTop: '20px' }}>
                            <div className="col-xs-4" style={{ paddingLeft: '0px' }}>
                              <CommodityAutoComplete
                                id='commodity'
                                commodityId={obj.commodityId}
                                selected={obj.commodity}
                                onChange={(value) => this.onChemicalApplicationCommodityChange(obj, value)}
                                label='Application Commodity'
                                errorText={get(obj, 'commodityErrors.0') || (obj.commodityId ? '' : get(obj, 'errors.0'))}
                                itemFilterFunc={commodities => filter(commodities, { type: 'chemical' })}
                                disabled={completedAndInvoicedMovement}
                              />
                            </div>
                            <div className="col-xs-3" style={{ paddingLeft: '0px', paddingRight: '0px' }}>
                              <CommonAutoSelect
                                id='applicationRate'
                                disabled={!obj.commodityId || completedAndInvoicedMovement}
                                onChange={value => this.handleApplicationRateChange(obj, value)}
                                label={`Application Rate (${this.applicationCommodityUnit(obj.commodity, obj.commodityId)}/${this.priceUnit()})`}
                                dataSourceConfig={{ text: 'name', value: 'id' }}
                                value={obj.applicationRate}
                                items={this.getEligibleApplicationRates(obj.commodityId)}
                                errorText={obj.applicationRate ? '' : get(obj, 'errors.0')}
                              />
                            </div>
                            <div className="col-xs-4" style={{ paddingRight: '0px' }}>
                              <CommonAutoSelect
                                items={sites}
                                id='storageId'
                                label='Storage'
                                value={obj.storageId}
                                errorText={obj.storageId ? '' : get(obj, 'errors.0', '')}
                                onChange={(value, id, item) => this.handleChemicalApplicationStorageChange(value, item, obj)}
                                dontAutoselectSingleItem
                                disabled={completedAndInvoicedMovement}
                              />
                            </div>
                            {
                              !completedAndInvoicedMovement &&
                              <div className='col-md-1' style={{ paddingRight: '0px' }}>
                                <IconButton onClick={() => this.onChemicalApplicationDelete(obj)} color='error'>
                                  <RemoveIcon fontSize='inherit' />
                                </IconButton>
                              </div>
                            }
                          </div>
                        </div>
                      ))
                    }
                    <div className='col-xs-12' style={{ marginTop: '30px', paddingLeft: '0px' }}>
                      <AddButton label='Application' onClick={() => this.onChemicalApplicationAdd()} style={{ float: 'left' }} />
                    </div>
                  </div>
                </React.Fragment>
              }
              <div className='col-xs-12 form-wrap' style={{ textAlign: 'left', paddingTop: '15px' }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      id='packAndShip'
                      color="primary"
                      checked={this.state.packAndShip}
                      onChange={this.handlePackShipCheckboxClick}
                      disabled={this.hasContainerMovement()}
                    />
                  }
                  label="Pack and Ship"
                />
              </div>
              {movement && this.state.packAndShip && (
                <div className='col-sm-12 no-padding' style={{ display: 'flex', gap: '8px', marginTop: '5px', width: '100%', paddingRight: '0px' }}>
                  <div className='col-xs-8 form-wrap' style={{ marginLeft: '-15px' }}>
                    <SiteAsyncAutocomplete
                      farmId={this.state.models.consignee.handlerId.value}
                      variant='standard'
                      id='consignee.handlerId'
                      label='Delivery Site (Optional)'
                      onChange={this.handleConsigneeChange}
                      selected={selectedConsignee?.id ? { ...selectedConsignee, name: selectedConsignee.displayName || selectedConsignee.name } : null}
                      minLength={3}
                      errorText={this.state.models.consignee.handlerId.errors[0]}
                      disabled={isSiteRestricted || this.isFieldDisabled('consignee.handlerId') || archivedStorage}
                      activeSitesOnly
                      includeSelectedSiteId={get(movement, 'freightDelivery.consignee.handlerId')}
                      fetchTopSitesOnClear
                    />
                  </div>
                  <div className='col-xs-4 form-wrap' style={{ marginTop: '-15px' }} >
                    { this.isCustomisedPackAndShip ?
                    <CommonAutoSelect
                      items={orderBy(this.state.trucks, 'id')}
                      id='truckId'
                      label={getCountryLabel('rego')}
                      value={this.state.models.truckId.value}
                      errorText={this.state.models.truckId.errors[0]}
                      onChange={this.handleSelectFieldChange}
                      dataSourceConfig={{ text: 'rego', value: 'id' }}
                      dontAutoselectSingleItem
                      top
                    /> : 
                    <CommonSelectInput
                      search
                      allowEmptyOptions
                      allowText={false}
                      endpoint="trucks/search/"
                      queryParams={{is_active: true}}
                      options={this.state.trucks}
                      optionMap={{ id: 'id', name: 'rego', companyId: 'companyId', companyName: 'companyName', totalWeights: 'totalWeights', categoryId: 'categoryId', code: 'code', steerPoint5: 'steerPoint5', steer1Point1: 'steer1Point1' }}
                      inputText={this.state.inputText || get(find(this.state.trucks, { id: this.state.models.truckId.value }), 'rego')}
                      id='truckId'
                      label={getCountryLabel('rego')}
                      getSelectedOption={this.setSelectedProviderTruck}
                      value={this.state.models.truckId.value}
                      disabled={this.disableForm}
                      create={this.props.createTruck}
                      actionCreator={addTruck}
                      validate={this.props.validateRego}
                      errorText={get(this.state.models, 'truckId.errors[0]', '')}
                      onChange={this.handleSelectFieldChange}
                      includeTruck={containerMovement ? get(containerMovement, 'freightDelivery.load.0.truck') : null}
                      setTruckOptions={this.setTruckOptions}
                    />
                    }
                  </div>
                </div>
              )}
              {
                movement && !isStrictQuantityBasedCommodity && this.state.packAndShip &&
                <div className='col-xs-4 form-wrap'>
                  <NumberField
                    id='tareWeight'
                    label={
                      isSelectedSiteFFAndWeighingAtDestination &&
                        !this.state.models.grossWeight.value
                        ? 'Truck Tare Weight (Optional)'
                        : 'Truck Tare Weight'
                    }
                    placeholder='Please enter'
                    value={this.state.models.tareWeight.value}
                    onChange={this.handleTareWeightChange}
                    maxValue={*********.99}
                    disabled={this.isFieldDisabled('tareWeight')}
                    helperText={get(this.state.models, 'tareWeight.errors[0]', '')}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position='end' style={styleAdornment}>
                          {this.getTruckUnit()}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              }
              {
                movement && !isStrictQuantityBasedCommodity && this.state.packAndShip &&
                <div className='col-xs-4 form-wrap'>
                  <NumberField
                    id='grossWeight'
                    label={
                      isSelectedSiteFFAndWeighingAtDestination &&
                        !this.state.models.tareWeight.value
                        ? 'Truck Gross Weight (Optional)'
                        : 'Truck Gross Weight'
                    }
                    placeholder='Please enter'
                    value={this.state.models.grossWeight.value}
                    onChange={this.handleGrossWeightChange}
                    disabled={this.isFieldDisabled('this.state.models.grossWeight')}
                    maxValue={*********.99}
                    helperText={get(this.state.models, 'grossWeight.errors[0]', '')}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position='end' style={styleAdornment}>
                          {this.getTruckUnit()}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              }
              {
                movement && !isStrictQuantityBasedCommodity && this.state.packAndShip &&
                <div className='col-xs-4 form-wrap'>
                  <NumberField
                    id='netWeight'
                    label='Truck Net Weight'
                    placeholder='Please enter'
                    value={this.state.models.netWeight.value ? this.state.models.netWeight.value : ''}
                    onChange={this.handleNetWeightChange}
                    maxValue={*********.99}
                    helperText={estimatedNetWeightError || get(this.state, 'sellerAvailableTonnageMessage')}
                    disabled={this.isFieldDisabled('netWeight')}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position='end' style={styleAdornment}>
                          {unit}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              }
              {
                movement && !isStrictQuantityBasedCommodity && this.state.packAndShip && this.isCustomisedPackAndShip &&
                <div className='col-xs-4 form-wrap'>
                  <NumberField
                    id='netCargo'
                    label='Net Cargo'
                    placeholder='Please enter'
                    value={this.state.models.netCargo.value ? this.state.models.netCargo.value : ''}
                    onChange={() => {}}
                    onBlur={this.handleNetCargoChange}
                    maxValue={*********.99}
                    helperText={get(this.state.models, 'netCargo.errors[0]')}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position='end' style={styleAdornment}>
                          {unit}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              }
            </div>
            <div className='col-xs-12 cardForm-action padding-reset'>
              <CommonButton
                label='Cancel'
                default={true}
                variant='outlined'
                onClick={this.props.closeDrawer}
              />
              <CommonButton
                type='submit'
                label='Save'
                primary
                variant="contained"
                disabled={this.state.submitting}
              />
            </div>
          </div>
          {(showUpdateToTonnagePopup || (this.state.showAmendTonnagePopup && isParentOrderIndependent)) &&
            <Dialog open onClose={() => this.setState({ showAmendTonnagePopup: false }, () => this.unlockSubmit())}>
              <DialogTitleWithCloseIcon onClose={() => this.setState({ showAmendTonnagePopup: false }, () => this.unlockSubmit())}>Warning</DialogTitleWithCloseIcon>
              <DialogContent>
                This {entityName} can take up to only {parentTonnage} {this.state.unit} (Inc tolerance).
                Saving this load will take the {entityName}&apos;s {this.countryTonnageLabel.toLowerCase()} to {updateToTonnage.toFixed(2)} {this.state.unit}. Do you want to automatically
                update the {entityName} {this.countryTonnageLabel.toLowerCase()} to {updateToTonnage.toFixed(2)} {this.state.unit} on saving this load?
              </DialogContent>
              <DialogActions>
                <Button
                  type='button'
                  onClick={() => this.setState({ showAmendTonnagePopup: false }, () => this.unlockSubmit())}
                  variant='outlined'>
                  Cancel
                </Button>
                <Button type='button' onClick={() => this.setState({ amendParentTonnage: true, showAmendTonnagePopup: false }, () => this.confirmSubmit(this.getSubmitData()))} color='primary' variant='outlined'>
                  Yes, Proceed
                </Button>
              </DialogActions>
            </Dialog>
          }
          {this.state.showAmendTonnagePopup && !isParentOrderIndependent && !showUpdateToTonnagePopup &&
            <Dialog open onClose={() => this.setState({ showAmendTonnagePopup: false }, () => this.unlockSubmit())}>
              <DialogTitleWithCloseIcon onClose={() => this.setState({ showAmendTonnagePopup: false }, () => this.unlockSubmit())}>Warning</DialogTitleWithCloseIcon>
              <DialogContent>
                This {entityName} can take up to only {parentTonnage} {this.state.unit} (Inc tolerance)
                {isDirectContractMovement ? '' : `and can be amended up to ${maxParentTonnage} ${this.state.unit} due to ${get(this.props, 'movement.reason')}`} {parentEntityUrl}. {amendMessage}
              </DialogContent>
              <DialogActions>
                <Button
                  type='button'
                  onClick={() => this.setState({ showAmendTonnagePopup: false }, () => this.unlockSubmit())}
                  variant='outlined'>
                  Cancel
                </Button>
              </DialogActions>
            </Dialog>
          }
        </form>
      </div>
    );
  }
}

const mapStateToProps = state => {
  return {
    token: state.main.user.token,
    userCompanyId: state.main.user.user.companyId,
    user: state.main.user.user,
    commodities: state.master.commodities.items,
    isRegoAvailable: state.main.isRegoAvailable,
    createdTruck: state.companies.companies.company.trucks.createdTruck,
    isLoading: state.main.isLoading,
  };
};

const mapDispatchToProps = dispatch => ({
  getCommodities: () => dispatch(getCommodities()),
  validateRego: (key, value, callback) => dispatch(validateRego(key, value, callback)),
  createTruck: (companyId, data, addTruck) => dispatch(createTruck(companyId, data, addTruck)),
  createPackShipLoads: (data, callback) => dispatch(createPackShipLoads(data, callback)),
  updatePackShipLoads: (movementId, data, callBack) => dispatch(updatePackShipLoads(movementId, data, callBack)),
  emptyCreatedTruck: () => dispatch(emptyCreatedTruck()),
  isLoading: () => dispatch(isLoading()),
  forceStopLoader: () => dispatch(forceStopLoader())
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(PackShipLoadsForm));
