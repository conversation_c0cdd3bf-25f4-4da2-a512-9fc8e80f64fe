import React from 'react';
import { connect } from "react-redux";
import { includes, get, find, isEmpty } from "lodash";
import alertifyjs from 'alertifyjs'
import APIService from '../services/APIService';
import {
  getSelectedOrder,
  receiveOrder,
  receiveAllocation,
  getOrders,
  getPaginatedFreightOrdersResponse,
} from "../actions/companies/orders";
import {isLoading} from "../actions/main";
import {
  getFreightOrdersGlobalListingHeaders, ORDERS_TABLE_COLUMN_LIMIT
} from "../common/constants";
import {
  isAtGlobalOrders,
  getContractIdFromCurrentRoute,
  getOrderIdFromCurrentRoute,
  getAbsoluteUrl,
} from "../common/utils";
import {
  getFreightOrderActionMenuOptions, regenerateOrderPDF
} from '../components/freights/utils';
import GenericTable from '../components/GenericTable';
import {handleFreightOrderOptionsMapper} from "../actions/companies/orders";
import FreightOrderActions from '../components/freights/FreightOrderActions';
import { getCustomColumns } from '../actions/companies/index';

class FreightOrdersTable extends React.Component {

  componentDidMount() {
    this.props.getCustomColumns('freight_order_table');
  }

  render() {
    return(
      <div>
        <GenericTable {...this.props}/>
        <FreightOrderActions {...this.props}/>
      </div>
    );
  }

}
const mapStateToProps = state => {
  const hasParentOrderId = document.location.href.includes('parent_order_id');
  const subItems = state.companies.orders.subItems;
  const clickedOption = state.companies.orders.clickedOption;
  const freightOrderColumns = [...getFreightOrdersGlobalListingHeaders(hasParentOrderId)];
  let customColumns = freightOrderColumns;
  const tableColumnsOrder = get(state.companies, 'companies.tableColumns');
  if (tableColumnsOrder && !isEmpty(tableColumnsOrder)) {
    customColumns = [];
    tableColumnsOrder.forEach(val => {
      const obj = find(freightOrderColumns, {key: val});
      if (obj) {
        customColumns.push(obj);
      }
    });
  }
  if (customColumns.length > ORDERS_TABLE_COLUMN_LIMIT) {
    customColumns = customColumns.splice(0, ORDERS_TABLE_COLUMN_LIMIT-1);
  }
  return {
    columns: customColumns,
    items: state.companies.orders.items,
    scrollToTopOnUpdate: false,
    optionsItems: (item) => getFreightOrderActionMenuOptions(item, subItems, clickedOption),
    currentUser: state.main.user.user,
    rowHighlightedMap: {'isHighlighted': 'delhi-blue'},
    paginationData: state.companies.orders.paginationData,
    globalSearch: true,
    clearSearch: getPaginatedFreightOrdersResponse,
    useNestedOptionMenu: true,
    canRaiseVoidRequestForOrder: state.companies.orders.canRaiseVoidRequestForOrder,
    canRaiseVoidAndDuplicateRequestForOrder: state.companies.orders.canRaiseVoidAndDuplicateRequestForOrder,
    selectedOrderId: state.companies.orders.selectedOrderId,
    shouldFetchOrder: state.companies.orders.shouldFetchOrder,
    selectedOrder: state.companies.orders.selectedOrder,
    clickedOption: clickedOption,
    subItems: subItems,
    userToken: state.main.user.token,
    canCloseOutForOrder: state.companies.orders.canCloseOutForOrder,
    voidFilter: true,
    displayIDColumn: 'identifier',
    canAssignParentForSelectedFreightOrder: state.companies.orders.canAssignParentForSelectedFreightOrder,
  };
};

const mapDispatchToProps = (dispatch, that) => {
  const { dontRedirect } = that;
  const queryParams = window.NEW_ORDERS_VIEW_TOGGLE ? '': '&type_id=1&type_id=2';
  return {
    handleOptionClick: (event, item, baseEntity) => {
      if(item?.key === 'recalculate_distance') {
        let message = alertifyjs.warning('Updating distance, please wait for few seconds.', 10)
        APIService.freights().orders(baseEntity.id).appendToUrl('distance/').put().then(response => {
          message.dismiss()
          if(response?.id)
            alertifyjs.success('Successfully updated distance.', 2)
          else
            alertifyjs.error('Unable to update, please contact AgriChain Support.', 2)
        })
      } else {
        const canPassContractDetailsMappingFrom = (get(item, 'key') === 'void_and_duplicate' && that.isContractDetailsModule)
        const mappingFrom = canPassContractDetailsMappingFrom ? 'contractDetailsModule' : 'listing';
        if(get(item, 'key') === 'regenerate_pdf')
          return regenerateOrderPDF(baseEntity);
        else
          dispatch(handleFreightOrderOptionsMapper(event, item, baseEntity, mappingFrom));
      }
    },
    handleDefaultCellClick: (item) => {
      const receiver = isAtGlobalOrders() ? receiveOrder : (item.parentOrderId ? receiveAllocation : receiveOrder);
      dispatch(getSelectedOrder(item.id, receiver, false, false, false, false, true, item.requestedUnit));
      dispatch(isLoading('nonExistingComponent'));
      if(dontRedirect) {
        const orderType = isAtGlobalOrders() ? 'fo' : (item.parentOrderId ? 'fa' : 'fo');
        document.location.hash = `${getAbsoluteUrl(document.location.hash)}?orderId=${item.id}&orderType=${orderType}`;
      } else {
        document.location.hash = '/freights/orders/' + item.id + '/order';
      }
    },
    navigateTo: (url) => {dispatch(getOrders('', '', url, true));},
    changePageSize: (url, pageSize) => {
      if (includes(url, '?')){
        url = `${url}&page_size=${pageSize}${queryParams}`;
      } else {
        url = `${url}?page_size=${pageSize}${queryParams}`;
      }
      dispatch(getOrders('', '', url, true));
    },
    getSearchSortUrl: (pageSize, page, searchText, orderBy, order, path, includeVoid) => {
      const service = APIService.freights().orders();
      service.appendToUrl(`web/search/?page_size=${pageSize}${queryParams}`);
      if (!isAtGlobalOrders()) {
        const contractId = getContractIdFromCurrentRoute();
        if(contractId) {
          service.appendToUrl(`&commodity_contract_id=${contractId}${queryParams}`);
        }
        const orderId = getOrderIdFromCurrentRoute();
        if(orderId) {
          if(path.includes('/allocations') || path.includes('/freights/orders'))
            service.appendToUrl(`&parent_order_id=${orderId}${queryParams}`);
          else
            service.appendToUrl(`&order_id=${orderId}${queryParams}`);
        }
      }
      if (page) {
        service.appendToUrl(`&page=${page}${queryParams}`);
      }
      if(searchText) {
        service.appendToUrl(`&search=${searchText}${queryParams}`);
      }
      if(orderBy) {
        service.appendToUrl(`&order_by=${orderBy}&order=${order}${queryParams}`);
      }
      if(includeVoid) {
        service.appendToUrl(`&include_void=${includeVoid}`)
      }
      return service.URL;
    },
    getCustomColumns: tableType => dispatch(getCustomColumns(tableType)),
    dispatch
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(FreightOrdersTable);
