import React from "react";
import { connect } from 'react-redux';
import { includes, isEmpty, uniq, without, isEqual } from 'lodash';
import GenericTable from '../components/GenericTable';
import { getPaginatedVendorDecsResponse, getVendorDecs } from '../actions/companies/vendor-decs';
import APIService from '../services/APIService';
import Skeleton from '@mui/material/Skeleton';
import { isGlobalEligibilityDecsPath, isSystemCompany, isCurrentUserGrower } from "../common/utils";
import { Virtuoso } from 'react-virtuoso';
import DeclarationList from './DeclarationList'

class EligibilityDecsTable extends React.Component {
  constructor(props) {
    super(props);
    this.state  = {
      expanded: props.expanded || [],
    }
  }

  componentDidUpdate(prevProps) {
    if(!isEqual(this.props.expanded, prevProps.expanded))
      this.setState({expanded: this.props.expanded})
  }

  expandItems = (event, companyId) => {
    event.preventDefault()
    event.stopPropagation()
    if(companyId && this.state.expanded.includes(companyId)) {
      this.setState({expanded: without(this.state.expanded, companyId)})
    }
    else if(companyId && !this.state.expanded.includes(companyId)) {
      this.setState({expanded: uniq([...this.state.expanded, companyId])})
    }
  }

  render() {
    return (
      <React.Fragment>
        { !isSystemCompany() && isGlobalEligibilityDecsPath () ?
          <Virtuoso
            data={this.props.items}
            style={{ height: '100vh' }}
            itemContent={(index, obj) => {
              const isExpanded = this.state.expanded.includes(isCurrentUserGrower() ? obj?.licenseeId : obj.declarantId)
              return (
                <div id={index} key={index} style={{marginTop: (index == 0 || isExpanded) ? '6px': 0}}>
                  <div style={{ background: '#F5F5F5', display: 'inline-block', width: '100%' }}>
                    <DeclarationList
                      party={obj}
                      isExpanded={isExpanded}
                      onClick={this.expandItems}
                    />
                  </div>
                  <div style={{padding: '0 10px', marginTop: isExpanded ? '-0.5%' : 0}}>
                      {(isEmpty(obj?.items) || !isExpanded) ?
                        isExpanded ? <Skeleton width='100%' height={100} /> : <div />
                        : <GenericTable
                            nested
                            {...this.props}
                            items={obj?.items}
                            columns={this.props.columns}
                            showHeader showHeaderValue={false}
                            changePageSize={null}
                            getSearchSortUrl={null}
                            handleOptionClick={null}
                            navigateTo={null}
                            height={(((obj.items?.length || 0) + 1) * 38) + 'px'}
                          />
                      }
                  </div>
                </div>
              )}
            }
          />
          : <GenericTable {...this.props} />
        }
      </React.Fragment>
    );
  }
}

const mapStateToProps = state => {
  return {
    clearSearch: getPaginatedVendorDecsResponse,
    items: state.companies.vendorDecs.items,
    paginationData: isGlobalEligibilityDecsPath() ? null : state.companies.vendorDecs.paginationData,
    scrollToTopOnUpdate: false,
    globalSearch: true,
    user: state.main.user,
    columns: !isSystemCompany() && isGlobalEligibilityDecsPath() ? [
      { key: 'identifier', header: 'Eligibility Dec No.', className: 'large', sortable: false },
      { key: 'season', header: 'Season', className: 'large', sortable: false },
      { key: 'createdOn', header: 'Created On', className: 'small', sortable: false },
      { key: 'createdBy', header: 'Created By', className: 'xxsmall', sortable: false },
    ] : 
    [
      { key: 'identifier', header: 'Eligibility Dec No.', className: 'small', sortable: false },
      { key: 'season', header: 'Season', className: 'large', sortable: false },
      { key: 'declarantName', header: 'Declarant', className: 'small', sortable: false },
      { key: 'licenseeName', header: 'Licensee', className: 'small', sortable: false },
      { key: 'createdOn', header: 'Created On', className: 'small', sortable: false },
      { key: 'createdBy', header: 'Created By', className: 'xxsmall', sortable: false },
    ],
    order: 'desc',
    orderBy: 'createdAt',
    displayIDColumn: 'identifier',
    rowHighlightedMap: {'isHighlighted': 'delhi-blue'},
    useNestedOptionMenu: true,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    handleDefaultCellClick: item => {
      document.location.hash = '/eligibility-decs/' + item.id + '/details';
    },
    navigateTo: (url) => {
      dispatch(getVendorDecs(url, true, null, true));
    },
    changePageSize: (url, pageSize) => {
      if (includes(url, '?')){
        url = `${url}&page_size=${pageSize}`;
      } else {
        url = `${url}?page_size=${pageSize}`;
      }
      dispatch(getVendorDecs(url, true, null, true));
    },
    getSearchSortUrl: (pageSize, page, searchText, orderBy, order) => {
      const service = APIService.vendor_decs();
      service.appendToUrl(`web/search/?page_size=${pageSize}`);
      if (page) {
        service.appendToUrl(`&page=${page}`);
      }
      if(searchText) {
        service.appendToUrl(`&search=${searchText}`);
      }
      if(orderBy) {
        service.appendToUrl(`&order_by=${orderBy}&order=${order}`);
      }

      return service.URL;
    },
    dispatch
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(EligibilityDecsTable);
