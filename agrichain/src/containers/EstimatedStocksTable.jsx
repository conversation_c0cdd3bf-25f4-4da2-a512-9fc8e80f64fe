import { connect } from 'react-redux';
import GenericTable from '../components/GenericTable';
import { clickEditStorageIcon } from '../actions/companies/storages';
import { getGradeName } from '../common/utils';

const mapStateToProps = () => {
  let columns = [
    { key: 'name', header: 'Storage Name', },
    { key: 'typeName', header: 'Type' },
    { key: 'size', header: 'Size (MT)' },
    { key: 'commodity.displayName', header: 'Commodity'},
    { key: 'variety.name', header: 'Variety' },
    { default: getGradeName, header: 'Grade' },
    { key: 'address.address', header: 'Address', map: {
        name: 'address.address',
        lat: 'address.latitude',
        lng: 'address.longitude'
      }}
  ];
  return {
    columns: columns,
    items: [],
    orderBy: 'name',
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    handleDefaultCellClick: (item) => {
        dispatch(clickEditStorageIcon(item.id));
    }
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(GenericTable);
