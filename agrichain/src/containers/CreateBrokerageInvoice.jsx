import { connect } from 'react-redux';
import BrokerageInvoiceForm from '../components/invoices/BrokerageInvoiceForm';
import {createAmendInvoice, createInvoice, deleteInvoice, editInvoice, generateInvoice} from '../actions/companies/invoices';
import {clickAddEmployeeButton} from "../actions/company-settings/employees";

const mapDispatchToProps = (dispatch) => {
  return {
    submit: (data) => {
      dispatch(createInvoice(data));
    },
    createAmendInvoice: (data, successCallback) => {
      dispatch(createAmendInvoice(data, false, false, successCallback));
    },
    editInvoice: (data, invoiceId, contractId) => {
      dispatch(editInvoice(data, invoiceId, contractId));
    },
    deleteInvoice: (invoiceId) => {
      dispatch(deleteInvoice(invoiceId));
    },
    generateInvoice: (invoiceId, contractId, data) => {
      dispatch(generateInvoice(invoiceId, contractId, null, data));
    },
    handleAddEmployeeButtonClick: () => {
      dispatch(clickAddEmployeeButton());
    }
  };
};

export default connect(null, mapDispatchToProps)(BrokerageInvoiceForm);
