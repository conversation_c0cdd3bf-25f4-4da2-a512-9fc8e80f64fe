import { connect } from 'react-redux';
import GenericTable from '../components/GenericTable';
import find from 'lodash/find';
import get from 'lodash/get';
import {getDocketNumberValue, getCommoditySpecColumns, getCountryLabel} from '../common/utils';

const mapStateToProps = (state) => {
  const commodityId = get(state, 'companies.outloads.items[0].commodityId', null);
  const commodity = find(state.master.commodities.items, (commodity) => {
    return commodity.id === commodityId;
  });

  return {
    columns: [
      { key: 'date', header: 'Date', },
      { key: 'time', header: 'Time' },
      { key: 'contract.referenceNumber', header: 'Contract No' },
      { key: 'referenceNumber', header: 'Movement No' },
      { key: 'freightProvider.name', header: 'Ft. Provider' },
      { key: 'truck.rego', header: getCountryLabel('rego') },
      { valueFunction: getDocketNumberValue, header: () => `${getCountryLabel('docket')} No`, fieldType: 'url-conditional-multi', urlKey: 'docketImageUrl' },
      { key: 'variety.name', header: 'Variety' },
      ...getCommoditySpecColumns(commodity),
      { key: 'netWeight', header: 'Tonnage' },
      { key: 'createdBy.name', header: 'User' },
    ],
    items: state.companies.outloads.items,
  };
};

export default connect(mapStateToProps)(GenericTable);
