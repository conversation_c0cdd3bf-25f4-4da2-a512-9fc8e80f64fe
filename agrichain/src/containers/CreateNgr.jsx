import { connect } from 'react-redux';
import NgrForm from '../components/ngrs/NgrForm';
import { createNgr, createAndGetNgr } from '../actions/api/ngrs';
import { addNgr } from '../actions/companies/ngrs';

function submit(farmId, data, onSuccess) {
  return (dispatch) => {
    if (onSuccess) {
      dispatch(createAndGetNgr(farmId, data, addNgr, onSuccess));
    } else {
      dispatch(createNgr(farmId, data, addNgr));
    }
  };
}

export default connect(null, { submit })(NgrForm);
