import { connect } from 'react-redux';
import GenericTable from '../components/GenericTable';
import { clickEditFarmEmployeeIcon } from '../actions/companies/farm-employees';
import { isInMyCompanyContext, getFarmIdFromCurrentRoute } from '../common/utils';
import { upsert, unlink } from '../actions/companies/key-contacts';

const mapStateToProps = (state) => {
  var farmEmployees = state.companies.farmEmployees.items;
  const keyContactTab = { key: 'keyContact', header: 'Key Contact', checkbox: true, onChange: 'handleKeyContactClick'};
  let columns = [
    { key: 'firstName', header: 'First Name', },
    { key: 'lastName', header: 'Last Name', },
    { key: 'type.displayName', header: 'Role' },
    { key: 'title', header: 'Job Title' },
    { key: 'mobile', header: 'Mobile' },
    { key: 'email', header: 'Email' },
  ];
  if(!isInMyCompanyContext()) {
    columns.splice(0, 0, keyContactTab);
  }

  return {
    columns: columns,
    items: farmEmployees,
    scrollToTopOnUpdate: false,
    orderBy: 'firstName',
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    handleDefaultCellClick: (item) => {
        dispatch(clickEditFarmEmployeeIcon(item.id));
    },
    handleKeyContactClick: (selectedItem, isChecked) => {
      if(isChecked) {
        dispatch(upsert({employeeId: selectedItem.id}, 'farms', getFarmIdFromCurrentRoute()));
      } else {
        dispatch(unlink('farms', getFarmIdFromCurrentRoute()));
      }
    }
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(GenericTable);
