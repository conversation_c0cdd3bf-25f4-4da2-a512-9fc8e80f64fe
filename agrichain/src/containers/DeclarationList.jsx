import React from 'react'
import ListItem from '@mui/material/ListItem'
import ListItemText from '@mui/material/ListItemText'
import Tooltip from '@mui/material/Tooltip'
import { isCurrentUserGrower } from '../common/utils'

const DeclarationList = ({ party, isExpanded, onClick, controls, ...rest }) => {
  const { licenseeId, licenseeName, declarantId, declarantName } = party
  const name = isCurrentUserGrower() ? licenseeName :  declarantName
  const partyId = isCurrentUserGrower() ? licenseeId : declarantId
  return (
    <span className='col-xs-12 padding-reset' style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
      <Tooltip title={isExpanded ? 'Click to collapse' : 'Click to expand and view items'}>
        <ListItem
          sx={{cursor: 'pointer', padding: '13px 12px', width: "100%"}}
          {...rest}
          onClick={event => onClick(event, partyId, name)}
        >
          <ListItemText
            sx={{marginTop: '2px', marginBottom: 0}}
            primary={name}
          />
        </ListItem>
      </Tooltip>
      {controls}
    </span>
  )
}

export default DeclarationList
