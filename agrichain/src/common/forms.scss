@import "./variables";

.text-black {
    textarea, input {
        color: $colorBlack !important;
        -webkit-text-fill-color: $colorBlack !important;
    }
}

#contract-content {
  padding-right: 10px;
}

.cardForm {
  display: inline-block;
  width: 100%;
  background: $colorWhite;
  margin-bottom: 10px;
  padding: 20px 15px 10px;
  border-radius: 4px;
  box-shadow: $boxShadow;

  &-title {
    width: 100%;
    font-size: 1.250rem;
    padding-left: 10px;
    margin: 0 0 20px 0;
  }

  &-sub-title {
    width: 100%;
    font-size: 1rem;
    padding-left: 10px;
    margin: 0 0 20px 0;
    padding-top: 30px;
  }

  &-content {
    display: flex;
    flex-wrap: wrap;

    & .form-wrap {
      min-height: 85px;
    }

    & .form-wrap-60 {
      min-height: 60px;
      margin-top: 15px;
    }

    & .form-wrap-70 {
      min-height: 70px;
    }

    & .form-wrap-55 {
      min-height: 55px;
    }
  }

  & .top15 {
    position: relative;
    top: 15px;
  }

  & .text-black {
    textarea, input {
      color: $colorBlack;
    }
  }

  & .top-15 {
    position: relative;
    top: -15px;
  }

  & .datePicker > div {
    width: 100%;
  }

  & .text-area {
    border: 1px solid #a5a1a1;
    border-bottom: 0;
    margin-bottom: 40px;

    #details-helper-text {
      position: absolute;
      bottom: -20px;
    }
  }

  & .field-label {
    font-size: 1rem;
    color: $colorLightGrey;
    margin-bottom: 5px;

    &.font-large {
      font-size: 1.250rem;
      color: $colorBlack;
    }
  }

  & .text-only h3 {
    color: $colorLightGrey;
    font-size: 1rem;
    border-bottom: 1px solid $colorLightGrey;
  }

  & .padding-reset {
    padding-left: 0;
    padding-right: 0;
  }

  & .arrow-button {
    margin-top: 58px;
    height: 48px;
    padding-left: 40px;
  }

  & .spread-text {
    margin-bottom: 25px;
    border-bottom: 1px solid $colorLightGrey;
    width: 42%;
    max-width: 500px;
  }

  & .cardForm-action {
    display: flex;
    width: 100%;
    justify-content: flex-end;
  }

  &.cardForm--drawer {
    box-shadow: none;
    padding: 0;
  }

  & .trucks {
    & .col-sm-3 {
      width: 30%;
    }

    & .float-minus-button {
      position: relative;
      top: 12px;
    }
    &.trucks-labels {
      font-size: 14px;
    }
  }

  & .label-small label {
    font-size: 0.8rem;
  }
}

.icon-lock {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: transparent url("/images/lock-icon.png") no-repeat;
  background-size: 20px;
  position: absolute;
  top: 23px;
  right: 0;
}

.icon-lock-inline {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: transparent url("/images/lock-icon.png") no-repeat;
    background-size: 20px;
    position: absolute;
    right: 0;
}

.side-drawer-icon-lock {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: transparent url("/images/lock-icon.png") no-repeat;
  background-size: 20px;
  position: absolute;
  top: 6px;
  right: -22px;
}

.relative-pos {
  position: relative;
  width: 100%;
  display: inline-block;
}

div[aria-pressed="false"] {
  color: $colorBlack;
}

.consignee-wrap {
  position: relative;

  & .btn-minus {
    position: absolute;
    right: -35px;
    bottom: 38px;
  }
}
