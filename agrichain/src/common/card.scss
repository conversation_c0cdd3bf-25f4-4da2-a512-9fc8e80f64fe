@import "./variables.scss";

.page-wrap {
  margin-top: 20px;
}

.label-content {
  min-height: 80px;
  color: $colorLightGrey;
  font-size: 12px;
  span {
    display: block;
    margin-top: 8px;
    font-size: 16px;
    color: $colorBlack;
  }
}

.card-header-content {
    float: right;
    display: flex;
    border-left: 1px solid $colorGrey;
}
.card-header-item {
    font-size: 0.813rem;
    display: flex;
    flex-direction: column;
    margin: 0 20px;
    align-items: center;

    & label {
        font-size: 0.750rem;
        color: $colorLightGrey;
        font-weight: 300;
    }
}

.card {
  display: inline-block;
  background: $colorWhite;
  margin-bottom: 10px;
  border-radius: 4px;
  width: 100%;
  box-shadow: $boxShadow;

  .card-header {
    display: inline-block;
    width: 100%;
  }

  & .header-title {
    display: inline-block;
    font-size: 1.250rem;
    margin: 0;
    padding: 15px 0 15px 15px;
  }

  & .header-content {
    float: right;
    display: flex;
    margin-right: 25px;
    border-left: 1px solid $colorGrey;
  }

  & .header-item {
    font-size: 0.813rem;
    display: flex;
    flex-direction: column;
    margin: 0 25px;
    padding: 12px 0 10px;
    align-items: center;

    & label {
      font-size: 0.750rem;
      color: $colorLightGrey;
      font-weight: 300;
    }
  }

  & .padding-reset {
    padding-left: 0;
    padding-right: 0;
  }

  & .toggle-btn {
    width: 65px;
    height: 58px;
    float: right;
    border: none;
    border-left: 1px solid $colorGrey;
    position: relative;

    &:after {
      height: 30px;
      position: absolute;
      content: "";
      top: 18px;
      left: 0;
      right: 0;
      background: url("/images/<EMAIL>") no-repeat 17px 0;
      background-size: 28px;
      -webkit-transition: all 0.5s ease;
      -moz-transition: all 0.5s ease;
      -ms-transition: all 0.5s ease;
      transition: all 0.5s ease;
    }
  }

  & .card-content {
    padding: 15px 0;
    display: inline-block;
    width: 100%;
    border-top: 1px solid $colorGrey;
  }

  &.collapse {
    & .card-content {
      display: none;
    }

    & .toggle-btn:after {
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
      transform: rotate(180deg);
      -webkit-transition: all 0.5s ease;
      -moz-transition: all 0.5s ease;
      -ms-transition: all 0.5s ease;
      transition: all 0.5s ease;
    }
  }
}
