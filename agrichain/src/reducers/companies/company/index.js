import { VISIT_COMPANY_DETAILS } from '../../../actions/companies';
import trucksReducer from '../../common/trucks';

const initialState = {
  id: undefined,
  trucks: undefined,
};

const reducer = (state = initialState, action) => {
  switch (action.type) {
    case VISIT_COMPANY_DETAILS:
      return {
        ...state,
        id: action.companyId,
      };
    default:
      return {
        ...state,
        trucks: trucksReducer(state.trucks, action),
      };
  }
};

export default reducer;
