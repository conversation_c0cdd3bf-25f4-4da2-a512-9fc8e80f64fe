import APIService from '../../services/APIService';
import { startFetchingVarieties, resetIsFetchingVarieties, receiveVarieties } from '../master/varieties';

export const getVarieties = () => (dispatch, getState) => {
  const { token } = getState().main.user;
  const { isFetchingVarieties } = getState().master.varieties;
  if (!isFetchingVarieties) {
    dispatch(startFetchingVarieties());
    APIService.varieties()
      .get(token)
      .then(items => {
        dispatch(receiveVarieties(items));
      })
      .catch(() => {
        dispatch(resetIsFetchingVarieties());
      });
  }
};
