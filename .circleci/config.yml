# Javascript Node CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-javascript/ for more details
#
version: 2.1
jobs:
  test:
    docker:
      - image: cimg/node:16.3.0-browsers
    resource_class: small
    working_directory: ~/web
    steps:
      - checkout
      - run:
          name: npm install
          command: |
            cd ~/web/agrichain
            npm install --force
          no_output_timeout: '20m'
      - run:
          name: eslint
          command: |
            cd ~/web/agrichain
            ./node_modules/eslint/bin/eslint.js --ext .jsx,.js src/
  automation:
    docker:
      - image: cimg/python:3.11.11-browsers
      - image: cimg/postgres:15.1
        command:
          - "postgres"
          - "-c"
          - "max_connections=1000"
          - "-c"
          - "shared_buffers=3GB"
    resource_class: large
    steps:
      - checkout
      - run:
          name: apt-get update
          command: |
            sudo apt-get update --fix-missing -y
            sudo apt-get install curl software-properties-common -y
            sudo apt-get upgrade -y
            sudo apt-get install apt-transport-https ca-certificates
      - browser-tools/install-chrome:
          replace-existing: true
      - browser-tools/install-chromedriver
      - run:
          command: |
            google-chrome --version
            chromedriver --version
          name: Check Google Chrome/Driver install
      - add_ssh_keys:
          fingerprints:
            - "f3:37:83:e3:dd:b9:9b:e9:ae:0e:da:97:73:e3:33:3d"
            - "91:65:3b:ed:47:5a:51:99:2f:06:8e:f7:65:51:44:ef"
            - "b2:d6:d6:ba:c3:09:18:cd:06:58:7d:58:11:51:85:90"
      - run:
          name: Patching SSH Key
          command: |
            echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDFkVcvUJhr0WoeP2B+0M5gzvixyGWMCJAmv4toQBABIa4g7owV5mAuv1Awu9YHuF1mZ3TL5QULmBxpc3HbQn9I38bjJg8MFRBiVMFKNEF0zLkqbssd3mTzSs2ZsAI15ocERCL959cKQxLDA24seRV9Ep54P3f01bGjx8RUXokFcC4wVtOAxfJXaLXN1kRE70mX7H8zha62Bg7bW9eUerIYRwE214577v8NYpebYI6xuqqnZbY2cx0LlPxmu+DOSrgrtohMJQ7cb+CXy2FT/xrjBGZYCvH58Ga1Mn7q2GOQvoVrKZ1s2RU8LdbrMyLcLJN0OVACSaO43n00QG+gZb/L <EMAIL>" > ~/.ssh/id_rsa.pub
            chmod 644 ~/.ssh/id_rsa.pub
            mv ~/.ssh/id_rsa_b2d6d6bac30918cd06587d5811518590 ~/.ssh/id_rsa
            chmod 600 ~/.ssh/id_rsa
            echo "Host github.com" >> ~/.ssh/config
            echo "  IdentitiesOnly no" >> ~/.ssh/config
            echo "  IdentityFile /home/<USER>/.ssh/id_rsa" >> ~/.ssh/config
            ssh-add
      - run:
          name: Cloning API
          command: |
            branch=$(git rev-parse --abbrev-ref HEAD)
            repo="**************:BlockGrain/api.git"
            dest="$HOME/api"
            default_branch="master"
            if git ls-remote --exit-code --heads "$repo" "$branch" >/dev/null 2>&1; then
              git clone --depth 1 --single-branch --branch "$branch" "$repo" "$dest"
            else
              git clone --depth 1 --single-branch --branch "$default_branch" "$repo" "$dest"
            fi
      - run:
          name: API Setup
          command: |
            cd ~/api/
            touch .env
            python3 -m venv venv
            . venv/bin/activate
            pip install --upgrade pip
            pip install -r requirements.txt --quiet
            sudo apt-get install postgresql-client
            sudo apt-get install wkhtmltopdf -y
            sudo apt-get install libgeos-dev -y
            psql -U postgres -h localhost -d postgres  -c "create role readonly"
            psql -U postgres -h localhost -d postgres < schema.dmp &> /dev/null
            psql -U postgres -h localhost -d postgres < migrations.sql &> /dev/null
            psql -U postgres -h localhost -d postgres < content_types.sql &> /dev/null
            LOG=0 ENV=ci python manage.py migrate &> /dev/null
            python manage.py loaddata core/fixtures/* &> /dev/null
            python manage.py loaddata core/test_fixtures/* &> /dev/null
            python manage.py create_gates
            python manage.py add_bhc_companies_to_directory
            ENV=ci GOOGLE_API_KEY=$GOOGLE_API_KEY nohup python manage.py runserver 0.0.0.0:9000 > api.out &
            echo | cat api.out
      - run:
          name: Web Setup
          command: |
            cd ~/project/agrichain/
            echo "Installing Packages..."
            npm install --silent --force
            sleep 1
            nohup bash -c "API_URL=http://localhost:9000 NODE_OPTIONS=--openssl-legacy-provider NODE_ENV=development PORT=3000 NO_UI_LOG=true npm start" > npm.out &
            echo | cat npm.out
      - run:
          name: Cloning Automation
          command: |
            branch=$(git rev-parse --abbrev-ref HEAD)
            repo="**************:BlockGrain/web-automation.git"
            dest="$HOME/web-automation"
            default_branch="master"
            if git ls-remote --exit-code --heads "$repo" "$branch" >/dev/null 2>&1; then
              git clone --depth 1 --single-branch --branch "$branch" "$repo" "$dest"
            else
              git clone --depth 1 --single-branch --branch "$default_branch" "$repo" "$dest"
            fi
      - run:
          name: Automation Setup
          command: |
            cd ~/web-automation
            python3 -m venv venv
            . venv/bin/activate
            pip install --upgrade pip
            pip install -r requirements.txt
            pip install typing-extensions
      - run:
          name: Running Automation
          command: |
            cd ~/web-automation
            . venv/bin/activate
            mkdir failed_scenarios_screenshots_old
            python3 behave_parallel.py -t smoke -c true -p 4
            export FAILURES=`ls failed_scenarios_screenshots | wc -l`
            [ "$FAILURES" -eq "0" ]
          no_output_timeout: '20m'
      - store_artifacts:
          path: /home/<USER>/web-automation/failed_scenarios_screenshots
          destination: test-reports
      - store_artifacts:
          path: /home/<USER>/web-automation/failed_scenarios_screenshots_old
          destination: test-reports-first-run
      - store_artifacts:
          path: /home/<USER>/api/api.out
          destination: api-logs
      - store_artifacts:
          path: /home/<USER>/project/agrichain/npm.out
          destination: web-logs
      - store_artifacts:
          path: /home/<USER>/api/api_old.out
          destination: api-logs-first-run

  build:
    docker:
      - image: cimg/python:3.11.11
    resource_class: small
    steps:
      - checkout
      - run:
          name: Install awscli
          command: |
            pip install awscli --upgrade --user
      - setup_remote_docker
      - run:
          name: Upload image to ECR
          command: |
            export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
            export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
            export AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION
            export TAG=`git describe --tags $CIRCLE_SHA1`
            ~/.local/bin/aws ecr get-login --no-include-email | /bin/bash
            cd ~/project/agrichain
            docker build -t 275446854307.dkr.ecr.ap-southeast-2.amazonaws.com/agrichain/web:$TAG .
            docker push 275446854307.dkr.ecr.ap-southeast-2.amazonaws.com/agrichain/web:$TAG
orbs:
  browser-tools: circleci/browser-tools@1.4.8
workflows:
  version: 2
  build_and_test:
    jobs:
      - build:
          filters:
            branches:
              ignore: /.*/
            tags:
              only: /^image-.*/
          context: agrichain-global
      - hold_test:
          type: approval
          filters:
            branches:
              ignore:
                - master
                - /^release.*/  # Ignore branches starting with "release"
                - ankercloud*
      - test:
        filters:
          branches:
            ignore:
              - ankercloud*
      - automation:
          name: automation_branch
          requires:
            - test
            - hold_test
          context: agrichain-global
          filters:
            branches:
              ignore:
                - master
                - /^release.*/
                - ankercloud*
      - automation:
          name: automation_smoke
          context: agrichain-global
          requires:
            - test
          filters:
            branches:
              only:
                - master
