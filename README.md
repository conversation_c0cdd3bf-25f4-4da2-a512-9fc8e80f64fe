# BlockGrain Web Client App
## Dev (Local)
#### Docker Setup

1.  Install [Docker](https://docs.docker.com/docker-for-mac/install/#download-docker-for-mac) (if not already installed)
2. `<NAME_EMAIL>:BlockGrain/web.git`
3. `cd web/agrichain`
4.  `docker-compose up -d` (Note: Use `-d` for background)
5. `docker exec -it agrichain_web_1 npm rebuild node-sass && docker restart agrichain_web_1` 
6. Visit http://localhost:3000 to check if everything works fine (you should see a login page).


#### Pre-commit Setup
We use pre-commit to automatically run linters before code is pushed.
1. Installation
* macOS `brew install pre-commit`
* Linux (Debian/Ubuntu) `sudo apt update && sudo apt install pre-commit`
* Using pip (works cross-platform) `pip install pre-commit`

2. Setup Hooks
Install the hooks in your local Git repo: `pre-commit install --hook-type pre-commit --hook-type pre-push`
```

